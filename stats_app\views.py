from django.shortcuts import render, get_object_or_404
from django.views.generic import (
    List<PERSON>iew, CreateView, UpdateView, DeleteView, TemplateView
)
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import JsonResponse
from django.db.models import Sum, Avg, Q
from django.utils import timezone
from datetime import datetime, date
import json

from .models import Company, Item, Target, StatisticalEntry, UserProfile
from .forms import (
    CompanyForm, ItemForm, TargetForm, StatisticalEntryForm,
    ReportFilterForm
)


class CompanyDataMixin:
    """Mixin لتطبيق فصل البيانات حسب الشركة"""

    def get_user_companies(self):
        """الحصول على الشركات المسموح للمستخدم بالوصول إليها"""
        if self.request.user.is_superuser:
            return Company.objects.filter(is_active=True)

        try:
            user_profile = self.request.user.userprofile
            if user_profile.company:
                return Company.objects.filter(id=user_profile.company.id, is_active=True)
        except UserProfile.DoesNotExist:
            pass

        return Company.objects.none()

    def filter_queryset_by_company(self, queryset):
        """تصفية الاستعلام حسب الشركات المسموحة"""
        allowed_companies = self.get_user_companies()
        if allowed_companies.exists():
            return queryset.filter(company__in=allowed_companies)
        return queryset.none()


# ==================== الصفحة الرئيسية ====================
class DashboardView(LoginRequiredMixin, CompanyDataMixin, TemplateView):
    template_name = 'stats_app/dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # الحصول على الشركات المسموحة
        companies = self.get_user_companies()

        # إحصائيات بسيطة
        context['total_companies'] = companies.count()
        context['total_items'] = Item.objects.filter(is_active=True).count()
        context['total_entries'] = self.filter_queryset_by_company(
            StatisticalEntry.objects.filter(date__year=timezone.now().year)
        ).count()
        context['total_targets'] = self.filter_queryset_by_company(
            Target.objects.filter(year=timezone.now().year)
        ).count()

        return context


# ==================== إدارة الشركات ====================
class CompanyListView(LoginRequiredMixin, CompanyDataMixin, ListView):
    model = Company
    template_name = 'stats_app/company_list.html'
    context_object_name = 'companies'
    paginate_by = 10

    def get_queryset(self):
        return self.get_user_companies()


class CompanyCreateView(LoginRequiredMixin, CreateView):
    model = Company
    form_class = CompanyForm
    template_name = 'stats_app/company_form.html'
    success_url = reverse_lazy('stats_app:company_list')

    def form_valid(self, form):
        messages.success(self.request, 'تم إنشاء الشركة بنجاح')
        return super().form_valid(form)


class CompanyUpdateView(LoginRequiredMixin, UpdateView):
    model = Company
    form_class = CompanyForm
    template_name = 'stats_app/company_form.html'
    success_url = reverse_lazy('stats_app:company_list')

    def form_valid(self, form):
        messages.success(self.request, 'تم تحديث الشركة بنجاح')
        return super().form_valid(form)


class CompanyDeleteView(LoginRequiredMixin, DeleteView):
    model = Company
    template_name = 'stats_app/company_confirm_delete.html'
    success_url = reverse_lazy('stats_app:company_list')

    def delete(self, request, *args, **kwargs):
        messages.success(request, 'تم حذف الشركة بنجاح')
        return super().delete(request, *args, **kwargs)


# ==================== إدارة العناصر ====================
class ItemListView(LoginRequiredMixin, ListView):
    model = Item
    template_name = 'stats_app/item_list.html'
    context_object_name = 'items'
    paginate_by = 10

    def get_queryset(self):
        return Item.objects.filter(is_active=True)


class ItemCreateView(LoginRequiredMixin, CreateView):
    model = Item
    form_class = ItemForm
    template_name = 'stats_app/item_form.html'
    success_url = reverse_lazy('stats_app:item_list')

    def form_valid(self, form):
        messages.success(self.request, 'تم إنشاء العنصر بنجاح')
        return super().form_valid(form)


class ItemUpdateView(LoginRequiredMixin, UpdateView):
    model = Item
    form_class = ItemForm
    template_name = 'stats_app/item_form.html'
    success_url = reverse_lazy('stats_app:item_list')

    def form_valid(self, form):
        messages.success(self.request, 'تم تحديث العنصر بنجاح')
        return super().form_valid(form)


class ItemDeleteView(LoginRequiredMixin, DeleteView):
    model = Item
    template_name = 'stats_app/item_confirm_delete.html'
    success_url = reverse_lazy('stats_app:item_list')

    def delete(self, request, *args, **kwargs):
        messages.success(request, 'تم حذف العنصر بنجاح')
        return super().delete(request, *args, **kwargs)


# ==================== إدارة المستهدفات ====================
class TargetListView(LoginRequiredMixin, CompanyDataMixin, ListView):
    model = Target
    template_name = 'stats_app/target_list.html'
    context_object_name = 'targets'
    paginate_by = 10

    def get_queryset(self):
        return self.filter_queryset_by_company(Target.objects.all())


class TargetCreateView(LoginRequiredMixin, CreateView):
    model = Target
    form_class = TargetForm
    template_name = 'stats_app/target_form.html'
    success_url = reverse_lazy('stats_app:target_list')

    def form_valid(self, form):
        messages.success(self.request, 'تم إنشاء المستهدف بنجاح')
        return super().form_valid(form)


class TargetUpdateView(LoginRequiredMixin, UpdateView):
    model = Target
    form_class = TargetForm
    template_name = 'stats_app/target_form.html'
    success_url = reverse_lazy('stats_app:target_list')

    def form_valid(self, form):
        messages.success(self.request, 'تم تحديث المستهدف بنجاح')
        return super().form_valid(form)


class TargetDeleteView(LoginRequiredMixin, DeleteView):
    model = Target
    template_name = 'stats_app/target_confirm_delete.html'
    success_url = reverse_lazy('stats_app:target_list')

    def delete(self, request, *args, **kwargs):
        messages.success(request, 'تم حذف المستهدف بنجاح')
        return super().delete(request, *args, **kwargs)


# ==================== إدارة البيانات الإحصائية ====================
class StatisticalEntryListView(LoginRequiredMixin, CompanyDataMixin, ListView):
    model = StatisticalEntry
    template_name = 'stats_app/entry_list.html'
    context_object_name = 'entries'
    paginate_by = 10

    def get_queryset(self):
        return self.filter_queryset_by_company(StatisticalEntry.objects.all())


class StatisticalEntryCreateView(LoginRequiredMixin, CreateView):
    model = StatisticalEntry
    form_class = StatisticalEntryForm
    template_name = 'stats_app/entry_form.html'
    success_url = reverse_lazy('stats_app:entry_list')

    def form_valid(self, form):
        messages.success(self.request, 'تم إنشاء البيانات الإحصائية بنجاح')
        return super().form_valid(form)


class StatisticalEntryUpdateView(LoginRequiredMixin, UpdateView):
    model = StatisticalEntry
    form_class = StatisticalEntryForm
    template_name = 'stats_app/entry_form.html'
    success_url = reverse_lazy('stats_app:entry_list')

    def form_valid(self, form):
        messages.success(self.request, 'تم تحديث البيانات الإحصائية بنجاح')
        return super().form_valid(form)


class StatisticalEntryDeleteView(LoginRequiredMixin, DeleteView):
    model = StatisticalEntry
    template_name = 'stats_app/entry_confirm_delete.html'
    success_url = reverse_lazy('stats_app:entry_list')

    def delete(self, request, *args, **kwargs):
        messages.success(request, 'تم حذف البيانات الإحصائية بنجاح')
        return super().delete(request, *args, **kwargs)


# ==================== التقارير البسيطة ====================
class SimpleReportsView(LoginRequiredMixin, CompanyDataMixin, TemplateView):
    template_name = 'stats_app/simple_reports.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # الحصول على الشركات والعناصر
        context['companies'] = self.get_user_companies()
        context['items'] = Item.objects.filter(is_active=True)
        context['years'] = [2022, 2023, 2024]
        context['data_types'] = Target.DATA_TYPES

        return context


class SimpleReportDataView(LoginRequiredMixin, CompanyDataMixin, TemplateView):
    """API بسيط للتقارير"""

    def get(self, request, *args, **kwargs):
        # الحصول على المعاملات
        company_id = request.GET.get('company')
        item_id = request.GET.get('item')
        year = int(request.GET.get('year', timezone.now().year))
        data_type = request.GET.get('data_type')

        # بناء الاستعلام
        entries = self.filter_queryset_by_company(
            StatisticalEntry.objects.filter(date__year=year)
        )
        targets = self.filter_queryset_by_company(
            Target.objects.filter(year=year)
        )

        if company_id:
            entries = entries.filter(company_id=company_id)
            targets = targets.filter(company_id=company_id)
        if item_id:
            entries = entries.filter(item_id=item_id)
            targets = targets.filter(item_id=item_id)
        if data_type:
            entries = entries.filter(data_type=data_type)
            targets = targets.filter(data_type=data_type)

        # إعداد البيانات الشهرية
        monthly_data = []
        monthly_targets = []
        labels = []

        for month in range(1, 13):
            month_name = dict(Target.MONTHS)[month]
            labels.append(month_name)

            month_achieved = entries.filter(date__month=month).aggregate(Sum('value'))['value__sum'] or 0
            month_target = targets.filter(month=month).aggregate(Sum('target_value'))['target_value__sum'] or 0

            monthly_data.append(float(month_achieved))
            monthly_targets.append(float(month_target))

        return JsonResponse({
            'labels': labels,
            'datasets': [
                {
                    'label': 'المحقق',
                    'data': monthly_data,
                    'backgroundColor': 'rgba(54, 162, 235, 0.6)',
                    'borderColor': 'rgba(54, 162, 235, 1)',
                    'borderWidth': 2
                },
                {
                    'label': 'المستهدف',
                    'data': monthly_targets,
                    'backgroundColor': 'rgba(255, 99, 132, 0.6)',
                    'borderColor': 'rgba(255, 99, 132, 1)',
                    'borderWidth': 2
                }
            ]
        })