from django.shortcuts import render, get_object_or_404, redirect
from django.views.generic import (
    <PERSON><PERSON><PERSON><PERSON>, CreateView, UpdateView, DeleteView, TemplateView, View
)
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import JsonResponse
from django.db.models import Sum, Avg, Q
from django.utils import timezone
from datetime import datetime, date
import json

from .models import Company, Item, Target, StatisticalEntry, UserProfile, SystemSettings
from .forms import (
    CompanyForm, ItemForm, TargetForm, StatisticalEntryForm,
    ReportFilterForm
)


class CompanyDataMixin:
    """Mixin لتطبيق فصل البيانات حسب الشركة"""

    def get_user_companies(self):
        """الحصول على الشركات المسموح للمستخدم بالوصول إليها"""
        if self.request.user.is_superuser:
            return Company.objects.filter(is_active=True)

        try:
            user_profile = self.request.user.userprofile
            if user_profile.company:
                return Company.objects.filter(id=user_profile.company.id, is_active=True)
        except UserProfile.DoesNotExist:
            pass

        return Company.objects.none()

    def filter_queryset_by_company(self, queryset):
        """تصفية الاستعلام حسب الشركات المسموحة"""
        allowed_companies = self.get_user_companies()
        if allowed_companies.exists():
            return queryset.filter(company__in=allowed_companies)
        return queryset.none()


# ==================== الصفحة الرئيسية ====================
class DashboardView(LoginRequiredMixin, CompanyDataMixin, TemplateView):
    template_name = 'stats_app/dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # الحصول على الشركات المسموحة
        companies = self.get_user_companies()

        # إحصائيات بسيطة
        context['total_companies'] = companies.count()
        context['total_items'] = Item.objects.filter(is_active=True).count()
        context['total_entries'] = self.filter_queryset_by_company(
            StatisticalEntry.objects.filter(date__year=timezone.now().year)
        ).count()
        context['total_targets'] = self.filter_queryset_by_company(
            Target.objects.filter(year=timezone.now().year)
        ).count()

        return context


# ==================== إدارة الشركات ====================
class CompanyListView(LoginRequiredMixin, CompanyDataMixin, ListView):
    model = Company
    template_name = 'stats_app/company_list.html'
    context_object_name = 'companies'
    paginate_by = 10

    def get_queryset(self):
        return self.get_user_companies()


class CompanyCreateView(LoginRequiredMixin, CreateView):
    model = Company
    form_class = CompanyForm
    template_name = 'stats_app/company_form.html'
    success_url = reverse_lazy('stats_app:company_list')

    def form_valid(self, form):
        messages.success(self.request, 'تم إنشاء الشركة بنجاح')
        return super().form_valid(form)


class CompanyUpdateView(LoginRequiredMixin, UpdateView):
    model = Company
    form_class = CompanyForm
    template_name = 'stats_app/company_form.html'
    success_url = reverse_lazy('stats_app:company_list')

    def form_valid(self, form):
        messages.success(self.request, 'تم تحديث الشركة بنجاح')
        return super().form_valid(form)


class CompanyDeleteView(LoginRequiredMixin, DeleteView):
    model = Company
    template_name = 'stats_app/company_confirm_delete.html'
    success_url = reverse_lazy('stats_app:company_list')

    def delete(self, request, *args, **kwargs):
        messages.success(request, 'تم حذف الشركة بنجاح')
        return super().delete(request, *args, **kwargs)


# ==================== إدارة العناصر ====================
class ItemListView(LoginRequiredMixin, ListView):
    model = Item
    template_name = 'stats_app/item_list.html'
    context_object_name = 'items'
    paginate_by = 10

    def get_queryset(self):
        return Item.objects.filter(is_active=True)


class ItemCreateView(LoginRequiredMixin, CreateView):
    model = Item
    form_class = ItemForm
    template_name = 'stats_app/item_form.html'
    success_url = reverse_lazy('stats_app:item_list')

    def form_valid(self, form):
        messages.success(self.request, 'تم إنشاء العنصر بنجاح')
        return super().form_valid(form)


class ItemUpdateView(LoginRequiredMixin, UpdateView):
    model = Item
    form_class = ItemForm
    template_name = 'stats_app/item_form.html'
    success_url = reverse_lazy('stats_app:item_list')

    def form_valid(self, form):
        messages.success(self.request, 'تم تحديث العنصر بنجاح')
        return super().form_valid(form)


class ItemDeleteView(LoginRequiredMixin, DeleteView):
    model = Item
    template_name = 'stats_app/item_confirm_delete.html'
    success_url = reverse_lazy('stats_app:item_list')

    def delete(self, request, *args, **kwargs):
        messages.success(request, 'تم حذف العنصر بنجاح')
        return super().delete(request, *args, **kwargs)


# ==================== إدارة المستهدفات ====================
class TargetListView(LoginRequiredMixin, CompanyDataMixin, ListView):
    model = Target
    template_name = 'stats_app/target_list.html'
    context_object_name = 'targets'
    paginate_by = 10

    def get_queryset(self):
        return self.filter_queryset_by_company(Target.objects.all())


class TargetCreateView(LoginRequiredMixin, CreateView):
    model = Target
    form_class = TargetForm
    template_name = 'stats_app/target_form.html'
    success_url = reverse_lazy('stats_app:target_list')

    def form_valid(self, form):
        messages.success(self.request, 'تم إنشاء المستهدف بنجاح')
        return super().form_valid(form)


class TargetUpdateView(LoginRequiredMixin, UpdateView):
    model = Target
    form_class = TargetForm
    template_name = 'stats_app/target_form.html'
    success_url = reverse_lazy('stats_app:target_list')

    def form_valid(self, form):
        messages.success(self.request, 'تم تحديث المستهدف بنجاح')
        return super().form_valid(form)


class TargetDeleteView(LoginRequiredMixin, DeleteView):
    model = Target
    template_name = 'stats_app/target_confirm_delete.html'
    success_url = reverse_lazy('stats_app:target_list')

    def delete(self, request, *args, **kwargs):
        messages.success(request, 'تم حذف المستهدف بنجاح')
        return super().delete(request, *args, **kwargs)


# ==================== إدارة البيانات الإحصائية ====================
class StatisticalEntryListView(LoginRequiredMixin, CompanyDataMixin, ListView):
    model = StatisticalEntry
    template_name = 'stats_app/entry_list.html'
    context_object_name = 'entries'
    paginate_by = 10

    def get_queryset(self):
        return self.filter_queryset_by_company(StatisticalEntry.objects.all())


class StatisticalEntryCreateView(LoginRequiredMixin, CreateView):
    model = StatisticalEntry
    form_class = StatisticalEntryForm
    template_name = 'stats_app/entry_form.html'
    success_url = reverse_lazy('stats_app:entry_list')

    def form_valid(self, form):
        messages.success(self.request, 'تم إنشاء البيانات الإحصائية بنجاح')
        return super().form_valid(form)


class StatisticalEntryUpdateView(LoginRequiredMixin, UpdateView):
    model = StatisticalEntry
    form_class = StatisticalEntryForm
    template_name = 'stats_app/entry_form.html'
    success_url = reverse_lazy('stats_app:entry_list')

    def form_valid(self, form):
        messages.success(self.request, 'تم تحديث البيانات الإحصائية بنجاح')
        return super().form_valid(form)


class StatisticalEntryDeleteView(LoginRequiredMixin, DeleteView):
    model = StatisticalEntry
    template_name = 'stats_app/entry_confirm_delete.html'
    success_url = reverse_lazy('stats_app:entry_list')

    def delete(self, request, *args, **kwargs):
        messages.success(request, 'تم حذف البيانات الإحصائية بنجاح')
        return super().delete(request, *args, **kwargs)


# ==================== التقارير البسيطة ====================
class SimpleReportsView(LoginRequiredMixin, CompanyDataMixin, TemplateView):
    template_name = 'stats_app/simple_reports.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # الحصول على الشركات والعناصر
        context['companies'] = self.get_user_companies()
        context['items'] = Item.objects.filter(is_active=True)
        context['data_types'] = Target.DATA_TYPES

        # الحصول على السنوات التي تحتوي على بيانات فعلية
        available_years = set()

        # السنوات من المستهدفات
        target_years = self.filter_queryset_by_company(Target.objects.all()).values_list('year', flat=True).distinct()
        available_years.update(target_years)

        # السنوات من الإدخالات
        entry_years = self.filter_queryset_by_company(StatisticalEntry.objects.all()).values_list('date__year', flat=True).distinct()
        available_years.update(entry_years)

        # إضافة السنوات المالية
        fiscal_years = self.filter_queryset_by_company(Target.objects.all()).values_list('fiscal_year', flat=True).distinct()
        available_years.update([fy for fy in fiscal_years if fy])

        fiscal_years_entries = self.filter_queryset_by_company(StatisticalEntry.objects.all()).values_list('fiscal_year', flat=True).distinct()
        available_years.update([fy for fy in fiscal_years_entries if fy])

        # ترتيب السنوات تنازلياً
        context['years'] = sorted(available_years, reverse=True) if available_years else [timezone.now().year]

        # إضافة إعدادات النظام
        context['system_settings'] = SystemSettings.get_settings()

        return context


class SimpleReportDataView(LoginRequiredMixin, CompanyDataMixin, TemplateView):
    """API بسيط للتقارير"""

    def get(self, request, *args, **kwargs):
        # الحصول على المعاملات
        company_id = request.GET.get('company')
        item_id = request.GET.get('item')
        year = int(request.GET.get('year', timezone.now().year))
        data_type = request.GET.get('data_type')

        # بناء الاستعلام
        entries = self.filter_queryset_by_company(
            StatisticalEntry.objects.filter(date__year=year)
        )
        targets = self.filter_queryset_by_company(
            Target.objects.filter(year=year)
        )

        if company_id:
            entries = entries.filter(company_id=company_id)
            targets = targets.filter(company_id=company_id)
        if item_id:
            entries = entries.filter(item_id=item_id)
            targets = targets.filter(item_id=item_id)
        if data_type:
            entries = entries.filter(data_type=data_type)
            targets = targets.filter(data_type=data_type)

        # إعداد البيانات الشهرية
        monthly_data = []
        monthly_targets = []
        labels = []

        for month in range(1, 13):
            month_name = dict(Target.MONTHS)[month]
            labels.append(month_name)

            month_achieved = entries.filter(date__month=month).aggregate(Sum('value'))['value__sum'] or 0
            month_target = targets.filter(month=month).aggregate(Sum('target_value'))['target_value__sum'] or 0

            monthly_data.append(float(month_achieved))
            monthly_targets.append(float(month_target))

        return JsonResponse({
            'labels': labels,
            'datasets': [
                {
                    'label': 'المحقق',
                    'data': monthly_data,
                    'backgroundColor': 'rgba(54, 162, 235, 0.6)',
                    'borderColor': 'rgba(54, 162, 235, 1)',
                    'borderWidth': 2
                },
                {
                    'label': 'المستهدف',
                    'data': monthly_targets,
                    'backgroundColor': 'rgba(255, 99, 132, 0.6)',
                    'borderColor': 'rgba(255, 99, 132, 1)',
                    'borderWidth': 2
                }
            ]
        })


# ==================== إعدادات النظام ====================
class SystemSettingsView(LoginRequiredMixin, TemplateView):
    template_name = 'stats_app/system_settings.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['settings'] = SystemSettings.get_settings()
        return context

    def post(self, request, *args, **kwargs):
        """تحديث إعدادات النظام"""
        if not request.user.is_superuser:
            messages.error(request, 'ليس لديك صلاحية لتعديل إعدادات النظام')
            return redirect('stats_app:system_settings')

        settings = SystemSettings.get_settings()

        # تحديث الإعدادات
        settings.fiscal_year_start_month = int(request.POST.get('fiscal_year_start_month', 7))
        settings.fiscal_year_start_day = int(request.POST.get('fiscal_year_start_day', 1))
        settings.fiscal_year_end_month = int(request.POST.get('fiscal_year_end_month', 6))
        settings.fiscal_year_end_day = int(request.POST.get('fiscal_year_end_day', 30))
        settings.system_name = request.POST.get('system_name', 'نظام الإحصائيات الشهرية')
        settings.default_currency = request.POST.get('default_currency', 'ريال')
        settings.reports_decimal_places = int(request.POST.get('reports_decimal_places', 2))
        settings.show_fiscal_year_in_reports = request.POST.get('show_fiscal_year_in_reports') == 'on'
        settings.updated_by = request.user

        settings.save()

        messages.success(request, 'تم تحديث إعدادات النظام بنجاح')
        return redirect('stats_app:system_settings')


# ==================== تقرير المقارنة بالأعوام السابقة ====================
class YearlyComparisonView(LoginRequiredMixin, CompanyDataMixin, TemplateView):
    template_name = 'stats_app/yearly_comparison.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # الحصول على الشركات والعناصر
        context['companies'] = self.get_user_companies()
        context['items'] = Item.objects.filter(is_active=True)
        context['data_types'] = Target.DATA_TYPES

        # الحصول على السنوات المتوفرة
        available_years = set()

        # السنوات من المستهدفات
        target_years = self.filter_queryset_by_company(Target.objects.all()).values_list('fiscal_year', flat=True).distinct()
        available_years.update([fy for fy in target_years if fy])

        # السنوات من الإدخالات
        entry_years = self.filter_queryset_by_company(StatisticalEntry.objects.all()).values_list('fiscal_year', flat=True).distinct()
        available_years.update([fy for fy in entry_years if fy])

        # ترتيب السنوات تنازلياً
        if available_years:
            context['years'] = sorted(available_years, reverse=True)
        else:
            context['years'] = [timezone.now().year]

        # إضافة إعدادات النظام
        context['system_settings'] = SystemSettings.get_settings()

        return context


class YearlyComparisonDataView(LoginRequiredMixin, CompanyDataMixin, View):
    """API لبيانات المقارنة السنوية"""

    def get(self, request, *args, **kwargs):
        # الحصول على المعاملات
        company_id = request.GET.get('company')
        item_id = request.GET.get('item')
        data_type = request.GET.get('data_type', 'production')
        years = request.GET.getlist('years[]')

        # التحقق من صحة معرف الشركة
        if company_id:
            allowed_companies = self.get_user_companies()
            if not allowed_companies.filter(id=company_id).exists():
                company_id = None

        # إعداد الاستعلام الأساسي
        entries_query = self.filter_queryset_by_company(StatisticalEntry.objects.all())
        targets_query = self.filter_queryset_by_company(Target.objects.all())

        # تطبيق الفلاتر
        if company_id:
            entries_query = entries_query.filter(company_id=company_id)
            targets_query = targets_query.filter(company_id=company_id)

        if item_id:
            entries_query = entries_query.filter(item_id=item_id)
            targets_query = targets_query.filter(item_id=item_id)

        if data_type:
            entries_query = entries_query.filter(data_type=data_type)
            targets_query = targets_query.filter(data_type=data_type)

        if years:
            # تحويل السنوات إلى integers
            year_ints = []
            for year in years:
                try:
                    year_ints.append(int(year))
                except (ValueError, TypeError):
                    continue

            if year_ints:
                entries_query = entries_query.filter(fiscal_year__in=year_ints)
                targets_query = targets_query.filter(fiscal_year__in=year_ints)

        # إعداد البيانات للمقارنة
        comparison_data = self.prepare_yearly_comparison_data(
            entries_query, targets_query, year_ints if years else []
        )

        return JsonResponse(comparison_data)

    def prepare_yearly_comparison_data(self, entries_query, targets_query, selected_years):
        """إعداد بيانات المقارنة السنوية"""

        # الحصول على السنوات المتوفرة إذا لم يتم تحديدها
        if not selected_years:
            fiscal_years = set()
            fiscal_years.update(entries_query.values_list('fiscal_year', flat=True).distinct())
            fiscal_years.update(targets_query.values_list('fiscal_year', flat=True).distinct())
            selected_years = sorted([int(fy) for fy in fiscal_years if fy], reverse=True)[:5]  # آخر 5 سنوات
        else:
            # التأكد من أن السنوات المختارة هي integers
            selected_years = [int(year) for year in selected_years if year]

        # إعداد البيانات
        datasets = []
        summary_data = {}

        # ألوان مختلفة لكل سنة
        colors = [
            '#007bff', '#28a745', '#dc3545', '#ffc107', '#17a2b8',
            '#6f42c1', '#e83e8c', '#fd7e14', '#20c997', '#6c757d'
        ]

        for i, year in enumerate(selected_years):
            year_color = colors[i % len(colors)]

            # بيانات السنة
            year_entries = entries_query.filter(fiscal_year=year)
            year_targets = targets_query.filter(fiscal_year=year)

            # البيانات الشهرية للسنة
            monthly_data = []
            monthly_targets = []

            # الحصول على أشهر السنة المالية
            settings = SystemSettings.get_settings()
            fiscal_months = settings.get_fiscal_year_months(year)

            for month_info in fiscal_months:
                month = month_info['month']
                month_year = month_info['year']

                # البيانات الفعلية
                month_entries = year_entries.filter(
                    date__year=month_year,
                    date__month=month
                )
                actual_value = month_entries.aggregate(
                    total=Sum('value')
                )['total'] or 0

                # المستهدفات
                month_targets = year_targets.filter(
                    year=month_year,
                    month=month
                )
                target_value = month_targets.aggregate(
                    total=Sum('target_value')
                )['total'] or 0

                monthly_data.append(float(actual_value))
                monthly_targets.append(float(target_value))

            # إضافة بيانات السنة للرسم البياني
            datasets.append({
                'label': f'الفعلي {year}',
                'data': monthly_data,
                'borderColor': year_color,
                'backgroundColor': year_color + '20',
                'fill': False,
                'tension': 0.1
            })

            datasets.append({
                'label': f'المستهدف {year}',
                'data': monthly_targets,
                'borderColor': year_color,
                'backgroundColor': year_color + '40',
                'borderDash': [5, 5],
                'fill': False,
                'tension': 0.1
            })

            # إحصائيات السنة
            total_actual = sum(monthly_data)
            total_target = sum(monthly_targets)
            achievement_rate = (total_actual / total_target * 100) if total_target > 0 else 0

            summary_data[year] = {
                'total_actual': total_actual,
                'total_target': total_target,
                'achievement_rate': round(achievement_rate, 2),
                'monthly_data': monthly_data,
                'monthly_targets': monthly_targets
            }

        # تسميات الأشهر
        if selected_years:
            settings = SystemSettings.get_settings()
            fiscal_months = settings.get_fiscal_year_months(selected_years[0])
            labels = [month['name'] for month in fiscal_months]
        else:
            labels = []

        # حساب معدلات النمو
        growth_rates = self.calculate_growth_rates(summary_data, selected_years)

        # أفضل وأسوأ أداء
        performance_analysis = self.analyze_performance(summary_data)

        return {
            'labels': labels,
            'datasets': datasets,
            'summary': summary_data,
            'growth_rates': growth_rates,
            'performance_analysis': performance_analysis,
            'selected_years': selected_years
        }

    def calculate_growth_rates(self, summary_data, years):
        """حساب معدلات النمو بين السنوات"""
        growth_rates = {}

        sorted_years = sorted(years)

        for i in range(1, len(sorted_years)):
            current_year = sorted_years[i]
            previous_year = sorted_years[i-1]

            if current_year in summary_data and previous_year in summary_data:
                current_actual = summary_data[current_year]['total_actual']
                previous_actual = summary_data[previous_year]['total_actual']

                if previous_actual > 0:
                    growth_rate = ((current_actual - previous_actual) / previous_actual) * 100
                    growth_rates[f'{previous_year}-{current_year}'] = round(growth_rate, 2)

        return growth_rates

    def analyze_performance(self, summary_data):
        """تحليل الأداء"""
        if not summary_data:
            return {}

        # أفضل سنة من حيث الإنجاز
        best_achievement = max(summary_data.items(), key=lambda x: x[1]['achievement_rate'])

        # أفضل سنة من حيث القيمة الفعلية
        best_actual = max(summary_data.items(), key=lambda x: x[1]['total_actual'])

        # أسوأ سنة من حيث الإنجاز
        worst_achievement = min(summary_data.items(), key=lambda x: x[1]['achievement_rate'])

        # متوسط الإنجاز
        avg_achievement = sum(data['achievement_rate'] for data in summary_data.values()) / len(summary_data)

        return {
            'best_achievement_year': best_achievement[0],
            'best_achievement_rate': best_achievement[1]['achievement_rate'],
            'best_actual_year': best_actual[0],
            'best_actual_value': best_actual[1]['total_actual'],
            'worst_achievement_year': worst_achievement[0],
            'worst_achievement_rate': worst_achievement[1]['achievement_rate'],
            'average_achievement': round(avg_achievement, 2)
        }