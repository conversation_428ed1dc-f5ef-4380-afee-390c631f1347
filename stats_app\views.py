from django.shortcuts import render, get_object_or_404
from django.views.generic import (
    ListView, CreateView, UpdateView, DeleteView, TemplateView
)
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import JsonResponse
from django.db.models import Sum, Avg, Q
from django.utils import timezone
from datetime import datetime, date
import json

from .models import Company, Item, Target, StatisticalEntry, UserProfile
from .forms import (
    CompanyForm, ItemForm, TargetForm, StatisticalEntryForm,
    ReportFilterForm
)


class AdminRequiredMixin(UserPassesTestMixin):
    """Mixin للتحقق من صلاحيات المدير"""
    def test_func(self):
        try:
            profile = self.request.user.userprofile
            return profile.is_system_admin
        except:
            return self.request.user.is_superuser


class CompanyUserMixin(UserPassesTestMixin):
    """Mixin للتحقق من صلاحيات مستخدم الشركة"""
    def test_func(self):
        try:
            profile = self.request.user.userprofile
            return profile.is_system_admin or profile.is_company_user
        except:
            return self.request.user.is_superuser


class DashboardView(LoginRequiredMixin, TemplateView):
    """لوحة التحكم الرئيسية"""
    template_name = 'stats_app/dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # إحصائيات عامة
        context['total_companies'] = Company.objects.filter(is_active=True).count()
        context['total_items'] = Item.objects.filter(is_active=True).count()
        context['total_entries'] = StatisticalEntry.objects.count()
        context['total_targets'] = Target.objects.count()

        # إحصائيات الشهر الحالي
        current_month = timezone.now().month
        current_year = timezone.now().year

        context['current_month_entries'] = StatisticalEntry.objects.filter(
            date__month=current_month,
            date__year=current_year
        ).count()

        # أحدث الإدخالات
        context['recent_entries'] = StatisticalEntry.objects.select_related(
            'company', 'item', 'created_by'
        ).order_by('-created_at')[:5]

        return context


# عروض الشركات
class CompanyListView(LoginRequiredMixin, ListView):
    model = Company
    template_name = 'stats_app/company_list.html'
    context_object_name = 'companies'
    paginate_by = 20

    def get_queryset(self):
        return Company.objects.all().order_by('name')


class CompanyCreateView(AdminRequiredMixin, CreateView):
    model = Company
    form_class = CompanyForm
    template_name = 'stats_app/company_form.html'
    success_url = reverse_lazy('stats_app:company_list')

    def form_valid(self, form):
        messages.success(self.request, 'تم إنشاء الشركة بنجاح')
        return super().form_valid(form)


class CompanyUpdateView(AdminRequiredMixin, UpdateView):
    model = Company
    form_class = CompanyForm
    template_name = 'stats_app/company_form.html'
    success_url = reverse_lazy('stats_app:company_list')

    def form_valid(self, form):
        messages.success(self.request, 'تم تحديث الشركة بنجاح')
        return super().form_valid(form)


class CompanyDeleteView(AdminRequiredMixin, DeleteView):
    model = Company
    template_name = 'stats_app/company_confirm_delete.html'
    success_url = reverse_lazy('stats_app:company_list')

    def delete(self, request, *args, **kwargs):
        messages.success(request, 'تم حذف الشركة بنجاح')
        return super().delete(request, *args, **kwargs)


# عروض العناصر
class ItemListView(LoginRequiredMixin, ListView):
    model = Item
    template_name = 'stats_app/item_list.html'
    context_object_name = 'items'
    paginate_by = 20

    def get_queryset(self):
        return Item.objects.all().order_by('name')


class ItemCreateView(AdminRequiredMixin, CreateView):
    model = Item
    form_class = ItemForm
    template_name = 'stats_app/item_form.html'
    success_url = reverse_lazy('stats_app:item_list')

    def form_valid(self, form):
        messages.success(self.request, 'تم إنشاء العنصر بنجاح')
        return super().form_valid(form)


class ItemUpdateView(AdminRequiredMixin, UpdateView):
    model = Item
    form_class = ItemForm
    template_name = 'stats_app/item_form.html'
    success_url = reverse_lazy('stats_app:item_list')

    def form_valid(self, form):
        messages.success(self.request, 'تم تحديث العنصر بنجاح')
        return super().form_valid(form)


class ItemDeleteView(AdminRequiredMixin, DeleteView):
    model = Item
    template_name = 'stats_app/item_confirm_delete.html'
    success_url = reverse_lazy('stats_app:item_list')

    def delete(self, request, *args, **kwargs):
        messages.success(request, 'تم حذف العنصر بنجاح')
        return super().delete(request, *args, **kwargs)


# عروض المستهدفات
class TargetListView(LoginRequiredMixin, ListView):
    model = Target
    template_name = 'stats_app/target_list.html'
    context_object_name = 'targets'
    paginate_by = 20

    def get_queryset(self):
        queryset = Target.objects.select_related('company', 'item', 'created_by')

        # تصفية حسب صلاحيات المستخدم
        try:
            profile = self.request.user.userprofile
            if not profile.is_system_admin and profile.is_company_user:
                queryset = queryset.filter(company=profile.company)
        except:
            pass

        return queryset.order_by('-year', '-month', 'company__name')


class TargetCreateView(CompanyUserMixin, CreateView):
    model = Target
    form_class = TargetForm
    template_name = 'stats_app/target_form.html'
    success_url = reverse_lazy('stats_app:target_list')

    def form_valid(self, form):
        form.instance.created_by = self.request.user
        messages.success(self.request, 'تم إنشاء المستهدف بنجاح')
        return super().form_valid(form)

    def get_form(self, form_class=None):
        form = super().get_form(form_class)
        # تقييد الشركات حسب صلاحيات المستخدم
        try:
            profile = self.request.user.userprofile
            if not profile.is_system_admin and profile.is_company_user:
                form.fields['company'].queryset = Company.objects.filter(id=profile.company.id)
        except:
            pass
        return form


class TargetUpdateView(CompanyUserMixin, UpdateView):
    model = Target
    form_class = TargetForm
    template_name = 'stats_app/target_form.html'
    success_url = reverse_lazy('stats_app:target_list')

    def form_valid(self, form):
        messages.success(self.request, 'تم تحديث المستهدف بنجاح')
        return super().form_valid(form)


class TargetDeleteView(CompanyUserMixin, DeleteView):
    model = Target
    template_name = 'stats_app/target_confirm_delete.html'
    success_url = reverse_lazy('stats_app:target_list')

    def delete(self, request, *args, **kwargs):
        messages.success(request, 'تم حذف المستهدف بنجاح')
        return super().delete(request, *args, **kwargs)


# عروض الإدخالات الإحصائية
class StatisticalEntryListView(LoginRequiredMixin, ListView):
    model = StatisticalEntry
    template_name = 'stats_app/entry_list.html'
    context_object_name = 'entries'
    paginate_by = 20

    def get_queryset(self):
        queryset = StatisticalEntry.objects.select_related('company', 'item', 'created_by')

        # تصفية حسب صلاحيات المستخدم
        try:
            profile = self.request.user.userprofile
            if not profile.is_system_admin and profile.is_company_user:
                queryset = queryset.filter(company=profile.company)
        except:
            pass

        return queryset.order_by('-date', 'company__name')


class StatisticalEntryCreateView(CompanyUserMixin, CreateView):
    model = StatisticalEntry
    form_class = StatisticalEntryForm
    template_name = 'stats_app/entry_form.html'
    success_url = reverse_lazy('stats_app:entry_list')

    def form_valid(self, form):
        form.instance.created_by = self.request.user
        messages.success(self.request, 'تم إنشاء الإدخال الإحصائي بنجاح')
        return super().form_valid(form)

    def get_form(self, form_class=None):
        form = super().get_form(form_class)
        # تقييد الشركات حسب صلاحيات المستخدم
        try:
            profile = self.request.user.userprofile
            if not profile.is_system_admin and profile.is_company_user:
                form.fields['company'].queryset = Company.objects.filter(id=profile.company.id)
        except:
            pass
        return form


class StatisticalEntryUpdateView(CompanyUserMixin, UpdateView):
    model = StatisticalEntry
    form_class = StatisticalEntryForm
    template_name = 'stats_app/entry_form.html'
    success_url = reverse_lazy('stats_app:entry_list')

    def form_valid(self, form):
        messages.success(self.request, 'تم تحديث الإدخال الإحصائي بنجاح')
        return super().form_valid(form)


class StatisticalEntryDeleteView(CompanyUserMixin, DeleteView):
    model = StatisticalEntry
    template_name = 'stats_app/entry_confirm_delete.html'
    success_url = reverse_lazy('stats_app:entry_list')

    def delete(self, request, *args, **kwargs):
        messages.success(request, 'تم حذف الإدخال الإحصائي بنجاح')
        return super().delete(request, *args, **kwargs)


# عروض التقارير
class ReportsView(LoginRequiredMixin, TemplateView):
    template_name = 'stats_app/reports.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['form'] = ReportFilterForm()
        return context


class ComparisonReportView(LoginRequiredMixin, TemplateView):
    template_name = 'stats_app/comparison_report.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # الحصول على معاملات التصفية
        company_id = self.request.GET.get('company')
        item_id = self.request.GET.get('item')
        month = self.request.GET.get('month')
        year = self.request.GET.get('year')

        # بناء الاستعلام
        entries = StatisticalEntry.objects.select_related('company', 'item')
        targets = Target.objects.select_related('company', 'item')

        if company_id:
            entries = entries.filter(company_id=company_id)
            targets = targets.filter(company_id=company_id)

        if item_id:
            entries = entries.filter(item_id=item_id)
            targets = targets.filter(item_id=item_id)

        if month:
            entries = entries.filter(date__month=month)
            targets = targets.filter(month=month)

        if year:
            entries = entries.filter(date__year=year)
            targets = targets.filter(year=year)

        # تجميع البيانات للمقارنة
        comparison_data = []
        for target in targets:
            entry = entries.filter(
                company=target.company,
                item=target.item,
                date__month=target.month,
                date__year=target.year
            ).first()

            if entry:
                comparison_data.append({
                    'target': target,
                    'entry': entry,
                    'quantity_achievement': entry.get_quantity_achievement_percentage(),
                    'value_achievement': entry.get_value_achievement_percentage(),
                })

        context['comparison_data'] = comparison_data
        context['form'] = ReportFilterForm(self.request.GET)

        return context


class ChartsView(LoginRequiredMixin, TemplateView):
    template_name = 'stats_app/charts.html'


class ChartDataAPIView(LoginRequiredMixin, TemplateView):
    """API لبيانات الرسوم البيانية"""

    def get(self, request, *args, **kwargs):
        chart_type = request.GET.get('type', 'monthly_comparison')

        if chart_type == 'monthly_comparison':
            return self.get_monthly_comparison_data()
        elif chart_type == 'company_performance':
            return self.get_company_performance_data()

        return JsonResponse({'error': 'نوع الرسم البياني غير مدعوم'})

    def get_monthly_comparison_data(self):
        """بيانات مقارنة الأداء الشهري"""
        current_year = timezone.now().year

        data = {
            'labels': [],
            'datasets': [
                {
                    'label': 'المستهدف',
                    'data': [],
                    'backgroundColor': 'rgba(54, 162, 235, 0.2)',
                    'borderColor': 'rgba(54, 162, 235, 1)',
                },
                {
                    'label': 'المحقق',
                    'data': [],
                    'backgroundColor': 'rgba(255, 99, 132, 0.2)',
                    'borderColor': 'rgba(255, 99, 132, 1)',
                }
            ]
        }

        for month in range(1, 13):
            month_name = dict(Target.MONTHS)[month]
            data['labels'].append(month_name)

            # إجمالي المستهدف للشهر
            target_sum = Target.objects.filter(
                month=month, year=current_year
            ).aggregate(Sum('target_value'))['target_value__sum'] or 0

            # إجمالي المحقق للشهر
            achieved_sum = StatisticalEntry.objects.filter(
                date__month=month, date__year=current_year
            ).aggregate(Sum('value'))['value__sum'] or 0

            data['datasets'][0]['data'].append(float(target_sum))
            data['datasets'][1]['data'].append(float(achieved_sum))

        return JsonResponse(data)

    def get_company_performance_data(self):
        """بيانات أداء الشركات"""
        companies = Company.objects.filter(is_active=True)

        data = {
            'labels': [company.name for company in companies],
            'datasets': [{
                'label': 'نسبة الإنجاز (%)',
                'data': [],
                'backgroundColor': [
                    'rgba(255, 99, 132, 0.2)',
                    'rgba(54, 162, 235, 0.2)',
                    'rgba(255, 205, 86, 0.2)',
                    'rgba(75, 192, 192, 0.2)',
                    'rgba(153, 102, 255, 0.2)',
                ],
                'borderColor': [
                    'rgba(255, 99, 132, 1)',
                    'rgba(54, 162, 235, 1)',
                    'rgba(255, 205, 86, 1)',
                    'rgba(75, 192, 192, 1)',
                    'rgba(153, 102, 255, 1)',
                ],
                'borderWidth': 1
            }]
        }

        current_month = timezone.now().month
        current_year = timezone.now().year

        for company in companies:
            # حساب نسبة الإنجاز للشركة
            targets = Target.objects.filter(
                company=company, month=current_month, year=current_year
            ).aggregate(Sum('target_value'))['target_value__sum'] or 0

            achieved = StatisticalEntry.objects.filter(
                company=company, date__month=current_month, date__year=current_year
            ).aggregate(Sum('value'))['value__sum'] or 0

            percentage = (achieved / targets * 100) if targets > 0 else 0
            data['datasets'][0]['data'].append(round(percentage, 2))

        return JsonResponse(data)
