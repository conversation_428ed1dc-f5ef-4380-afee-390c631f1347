from django.shortcuts import render, get_object_or_404
from django.views.generic import (
    ListView, CreateView, UpdateView, DeleteView, TemplateView
)
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import JsonResponse
from django.db.models import Sum, Avg, Q
from django.utils import timezone
from datetime import datetime, date
import json

from .models import Company, Item, Target, StatisticalEntry, UserProfile
from .forms import (
    CompanyForm, ItemForm, TargetForm, StatisticalEntryForm,
    ReportFilterForm
)


class AdminRequiredMixin(UserPassesTestMixin):
    """Mixin للتحقق من صلاحيات المدير"""
    def test_func(self):
        try:
            profile = self.request.user.userprofile
            return profile.is_system_admin
        except:
            return self.request.user.is_superuser


class CompanyUserMixin(UserPassesTestMixin):
    """Mixin للتحقق من صلاحيات مستخدم الشركة"""
    def test_func(self):
        try:
            profile = self.request.user.userprofile
            return profile.is_system_admin or profile.is_company_user
        except:
            return self.request.user.is_superuser


class DashboardView(LoginRequiredMixin, TemplateView):
    """لوحة التحكم الرئيسية"""
    template_name = 'stats_app/dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # إحصائيات عامة
        context['total_companies'] = Company.objects.filter(is_active=True).count()
        context['total_items'] = Item.objects.filter(is_active=True).count()
        context['total_entries'] = StatisticalEntry.objects.count()
        context['total_targets'] = Target.objects.count()

        # إحصائيات الشهر الحالي
        current_month = timezone.now().month
        current_year = timezone.now().year

        context['current_month_entries'] = StatisticalEntry.objects.filter(
            date__month=current_month,
            date__year=current_year
        ).count()

        # أحدث الإدخالات
        context['recent_entries'] = StatisticalEntry.objects.select_related(
            'company', 'item', 'created_by'
        ).order_by('-created_at')[:5]

        return context


class DataTypesDashboardView(LoginRequiredMixin, TemplateView):
    """لوحة تحكم أنواع البيانات"""
    template_name = 'stats_app/data_types_dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # إحصائيات حسب نوع البيانات
        context['capacity_targets'] = Target.objects.filter(data_type='capacity').count()
        context['production_targets'] = Target.objects.filter(data_type='production').count()
        context['inventory_targets'] = Target.objects.filter(data_type='inventory').count()
        context['sales_targets'] = Target.objects.filter(data_type='sales').count()

        context['capacity_entries'] = StatisticalEntry.objects.filter(data_type='capacity').count()
        context['production_entries'] = StatisticalEntry.objects.filter(data_type='production').count()
        context['inventory_entries'] = StatisticalEntry.objects.filter(data_type='inventory').count()
        context['sales_entries'] = StatisticalEntry.objects.filter(data_type='sales').count()

        # بيانات الشركات
        companies = Company.objects.filter(is_active=True)
        companies_data = []

        for company in companies:
            company_data = {
                'company': company,
                'capacity_count': StatisticalEntry.objects.filter(company=company, data_type='capacity').count(),
                'production_count': StatisticalEntry.objects.filter(company=company, data_type='production').count(),
                'inventory_count': StatisticalEntry.objects.filter(company=company, data_type='inventory').count(),
                'sales_count': StatisticalEntry.objects.filter(company=company, data_type='sales').count(),
            }
            company_data['total_count'] = (
                company_data['capacity_count'] +
                company_data['production_count'] +
                company_data['inventory_count'] +
                company_data['sales_count']
            )
            companies_data.append(company_data)

        context['companies_data'] = companies_data

        return context


# عروض الشركات
class CompanyListView(LoginRequiredMixin, ListView):
    model = Company
    template_name = 'stats_app/company_list.html'
    context_object_name = 'companies'
    paginate_by = 20

    def get_queryset(self):
        return Company.objects.all().order_by('name')


class CompanyCreateView(AdminRequiredMixin, CreateView):
    model = Company
    form_class = CompanyForm
    template_name = 'stats_app/company_form.html'
    success_url = reverse_lazy('stats_app:company_list')

    def form_valid(self, form):
        messages.success(self.request, 'تم إنشاء الشركة بنجاح')
        return super().form_valid(form)


class CompanyUpdateView(AdminRequiredMixin, UpdateView):
    model = Company
    form_class = CompanyForm
    template_name = 'stats_app/company_form.html'
    success_url = reverse_lazy('stats_app:company_list')

    def form_valid(self, form):
        messages.success(self.request, 'تم تحديث الشركة بنجاح')
        return super().form_valid(form)


class CompanyDeleteView(AdminRequiredMixin, DeleteView):
    model = Company
    template_name = 'stats_app/company_confirm_delete.html'
    success_url = reverse_lazy('stats_app:company_list')

    def delete(self, request, *args, **kwargs):
        messages.success(request, 'تم حذف الشركة بنجاح')
        return super().delete(request, *args, **kwargs)


# عروض العناصر
class ItemListView(LoginRequiredMixin, ListView):
    model = Item
    template_name = 'stats_app/item_list.html'
    context_object_name = 'items'
    paginate_by = 20

    def get_queryset(self):
        return Item.objects.all().order_by('name')


class ItemCreateView(AdminRequiredMixin, CreateView):
    model = Item
    form_class = ItemForm
    template_name = 'stats_app/item_form.html'
    success_url = reverse_lazy('stats_app:item_list')

    def form_valid(self, form):
        messages.success(self.request, 'تم إنشاء العنصر بنجاح')
        return super().form_valid(form)


class ItemUpdateView(AdminRequiredMixin, UpdateView):
    model = Item
    form_class = ItemForm
    template_name = 'stats_app/item_form.html'
    success_url = reverse_lazy('stats_app:item_list')

    def form_valid(self, form):
        messages.success(self.request, 'تم تحديث العنصر بنجاح')
        return super().form_valid(form)


class ItemDeleteView(AdminRequiredMixin, DeleteView):
    model = Item
    template_name = 'stats_app/item_confirm_delete.html'
    success_url = reverse_lazy('stats_app:item_list')

    def delete(self, request, *args, **kwargs):
        messages.success(request, 'تم حذف العنصر بنجاح')
        return super().delete(request, *args, **kwargs)


# عروض المستهدفات
class TargetListView(LoginRequiredMixin, ListView):
    model = Target
    template_name = 'stats_app/target_list.html'
    context_object_name = 'targets'
    paginate_by = 20

    def get_queryset(self):
        queryset = Target.objects.select_related('company', 'item', 'created_by')

        # تصفية حسب صلاحيات المستخدم
        try:
            profile = self.request.user.userprofile
            if not profile.is_system_admin and profile.is_company_user:
                queryset = queryset.filter(company=profile.company)
        except:
            pass

        # تصفية حسب نوع البيانات إذا تم تمريره
        data_type = self.request.GET.get('data_type')
        if data_type:
            queryset = queryset.filter(data_type=data_type)

        return queryset.order_by('-year', '-month', 'company__name')


class TargetCreateView(CompanyUserMixin, CreateView):
    model = Target
    form_class = TargetForm
    template_name = 'stats_app/target_form.html'
    success_url = reverse_lazy('stats_app:target_list')

    def form_valid(self, form):
        form.instance.created_by = self.request.user
        messages.success(self.request, 'تم إنشاء المستهدف بنجاح')
        return super().form_valid(form)

    def get_form(self, form_class=None):
        form = super().get_form(form_class)
        # تقييد الشركات حسب صلاحيات المستخدم
        try:
            profile = self.request.user.userprofile
            if not profile.is_system_admin and profile.is_company_user:
                form.fields['company'].queryset = Company.objects.filter(id=profile.company.id)
        except:
            pass

        # تعيين نوع البيانات الافتراضي إذا تم تمريره
        data_type = self.request.GET.get('data_type')
        if data_type and data_type in dict(Target.DATA_TYPES):
            form.fields['data_type'].initial = data_type

        return form


class TargetUpdateView(CompanyUserMixin, UpdateView):
    model = Target
    form_class = TargetForm
    template_name = 'stats_app/target_form.html'
    success_url = reverse_lazy('stats_app:target_list')

    def form_valid(self, form):
        messages.success(self.request, 'تم تحديث المستهدف بنجاح')
        return super().form_valid(form)


class TargetDeleteView(CompanyUserMixin, DeleteView):
    model = Target
    template_name = 'stats_app/target_confirm_delete.html'
    success_url = reverse_lazy('stats_app:target_list')

    def delete(self, request, *args, **kwargs):
        messages.success(request, 'تم حذف المستهدف بنجاح')
        return super().delete(request, *args, **kwargs)


# عروض الإدخالات الإحصائية
class StatisticalEntryListView(LoginRequiredMixin, ListView):
    model = StatisticalEntry
    template_name = 'stats_app/entry_list.html'
    context_object_name = 'entries'
    paginate_by = 20

    def get_queryset(self):
        queryset = StatisticalEntry.objects.select_related('company', 'item', 'created_by')

        # تصفية حسب صلاحيات المستخدم
        try:
            profile = self.request.user.userprofile
            if not profile.is_system_admin and profile.is_company_user:
                queryset = queryset.filter(company=profile.company)
        except:
            pass

        # تصفية حسب نوع البيانات إذا تم تمريره
        data_type = self.request.GET.get('data_type')
        if data_type:
            queryset = queryset.filter(data_type=data_type)

        return queryset.order_by('-date', 'company__name')


class StatisticalEntryCreateView(CompanyUserMixin, CreateView):
    model = StatisticalEntry
    form_class = StatisticalEntryForm
    template_name = 'stats_app/entry_form.html'
    success_url = reverse_lazy('stats_app:entry_list')

    def form_valid(self, form):
        form.instance.created_by = self.request.user
        messages.success(self.request, 'تم إنشاء الإدخال الإحصائي بنجاح')
        return super().form_valid(form)

    def get_form(self, form_class=None):
        form = super().get_form(form_class)
        # تقييد الشركات حسب صلاحيات المستخدم
        try:
            profile = self.request.user.userprofile
            if not profile.is_system_admin and profile.is_company_user:
                form.fields['company'].queryset = Company.objects.filter(id=profile.company.id)
        except:
            pass

        # تعيين نوع البيانات الافتراضي إذا تم تمريره
        data_type = self.request.GET.get('data_type')
        if data_type and data_type in dict(StatisticalEntry.DATA_TYPES):
            form.fields['data_type'].initial = data_type

        return form


class StatisticalEntryUpdateView(CompanyUserMixin, UpdateView):
    model = StatisticalEntry
    form_class = StatisticalEntryForm
    template_name = 'stats_app/entry_form.html'
    success_url = reverse_lazy('stats_app:entry_list')

    def form_valid(self, form):
        messages.success(self.request, 'تم تحديث الإدخال الإحصائي بنجاح')
        return super().form_valid(form)


class StatisticalEntryDeleteView(CompanyUserMixin, DeleteView):
    model = StatisticalEntry
    template_name = 'stats_app/entry_confirm_delete.html'
    success_url = reverse_lazy('stats_app:entry_list')

    def delete(self, request, *args, **kwargs):
        messages.success(request, 'تم حذف الإدخال الإحصائي بنجاح')
        return super().delete(request, *args, **kwargs)


# عروض التقارير
class ReportsView(LoginRequiredMixin, TemplateView):
    template_name = 'stats_app/reports.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['form'] = ReportFilterForm()
        return context


class ComparisonReportView(LoginRequiredMixin, TemplateView):
    template_name = 'stats_app/comparison_report.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # الحصول على معاملات التصفية
        company_id = self.request.GET.get('company')
        item_id = self.request.GET.get('item')
        data_type = self.request.GET.get('data_type')
        month = self.request.GET.get('month')
        year = self.request.GET.get('year')

        # بناء الاستعلام
        entries = StatisticalEntry.objects.select_related('company', 'item')
        targets = Target.objects.select_related('company', 'item')

        if company_id:
            entries = entries.filter(company_id=company_id)
            targets = targets.filter(company_id=company_id)

        if item_id:
            entries = entries.filter(item_id=item_id)
            targets = targets.filter(item_id=item_id)

        if data_type:
            entries = entries.filter(data_type=data_type)
            targets = targets.filter(data_type=data_type)

        if month:
            entries = entries.filter(date__month=month)
            targets = targets.filter(month=month)

        if year:
            entries = entries.filter(date__year=year)
            targets = targets.filter(year=year)

        # تجميع البيانات للمقارنة
        comparison_data = []
        for target in targets:
            entry = entries.filter(
                company=target.company,
                item=target.item,
                data_type=target.data_type,
                date__month=target.month,
                date__year=target.year
            ).first()

            if entry:
                comparison_data.append({
                    'target': target,
                    'entry': entry,
                    'quantity_achievement': entry.get_quantity_achievement_percentage(),
                    'value_achievement': entry.get_value_achievement_percentage(),
                })

        context['comparison_data'] = comparison_data
        context['form'] = ReportFilterForm(self.request.GET)

        return context


class ChartsView(LoginRequiredMixin, TemplateView):
    template_name = 'stats_app/charts.html'


class DetailedChartsView(LoginRequiredMixin, TemplateView):
    """صفحة الرسوم البيانية التفصيلية"""
    template_name = 'stats_app/detailed_charts.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # إضافة نموذج التصفية
        context['filter_form'] = ReportFilterForm(self.request.GET)

        # الحصول على معاملات التصفية
        company_id = self.request.GET.get('company')
        item_id = self.request.GET.get('item')
        data_type = self.request.GET.get('data_type')
        year = self.request.GET.get('year', timezone.now().year)

        context['selected_company'] = company_id
        context['selected_item'] = item_id
        context['selected_data_type'] = data_type
        context['selected_year'] = year

        return context


class ChartDataAPIView(LoginRequiredMixin, TemplateView):
    """API لبيانات الرسوم البيانية"""

    def get(self, request, *args, **kwargs):
        chart_type = request.GET.get('type', 'monthly_comparison')

        if chart_type == 'monthly_comparison':
            return self.get_monthly_comparison_data()
        elif chart_type == 'company_performance':
            return self.get_company_performance_data()
        elif chart_type == 'detailed_monthly':
            return self.get_detailed_monthly_data()
        elif chart_type == 'data_types_comparison':
            return self.get_data_types_comparison()
        elif chart_type == 'company_data_types':
            return self.get_company_data_types()
        elif chart_type == 'achievement_analysis':
            return self.get_achievement_analysis()
        elif chart_type == 'trend_analysis':
            return self.get_trend_analysis()
        elif chart_type == 'capacity_utilization':
            return self.get_capacity_utilization()
        elif chart_type == 'kpi_dashboard':
            return self.get_kpi_dashboard()
        elif chart_type == 'variance_analysis':
            return self.get_variance_analysis()
        elif chart_type == 'efficiency_metrics':
            return self.get_efficiency_metrics()
        elif chart_type == 'seasonal_analysis':
            return self.get_seasonal_analysis()
        elif chart_type == 'correlation_matrix':
            return self.get_correlation_matrix()
        elif chart_type == 'performance_ranking':
            return self.get_performance_ranking()
        elif chart_type == 'growth_analysis':
            return self.get_growth_analysis()
        elif chart_type == 'benchmark_analysis':
            return self.get_benchmark_analysis()
        elif chart_type == 'risk_assessment':
            return self.get_risk_assessment()
        elif chart_type == 'forecasting':
            return self.get_forecasting_data()

        return JsonResponse({'error': 'نوع الرسم البياني غير مدعوم'})

    def get_monthly_comparison_data(self):
        """بيانات مقارنة الأداء الشهري"""
        current_year = timezone.now().year

        data = {
            'labels': [],
            'datasets': [
                {
                    'label': 'المستهدف',
                    'data': [],
                    'backgroundColor': 'rgba(54, 162, 235, 0.2)',
                    'borderColor': 'rgba(54, 162, 235, 1)',
                },
                {
                    'label': 'المحقق',
                    'data': [],
                    'backgroundColor': 'rgba(255, 99, 132, 0.2)',
                    'borderColor': 'rgba(255, 99, 132, 1)',
                }
            ]
        }

        for month in range(1, 13):
            month_name = dict(Target.MONTHS)[month]
            data['labels'].append(month_name)

            # إجمالي المستهدف للشهر
            target_sum = Target.objects.filter(
                month=month, year=current_year
            ).aggregate(Sum('target_value'))['target_value__sum'] or 0

            # إجمالي المحقق للشهر
            achieved_sum = StatisticalEntry.objects.filter(
                date__month=month, date__year=current_year
            ).aggregate(Sum('value'))['value__sum'] or 0

            data['datasets'][0]['data'].append(float(target_sum))
            data['datasets'][1]['data'].append(float(achieved_sum))

        return JsonResponse(data)

    def get_company_performance_data(self):
        """بيانات أداء الشركات"""
        companies = Company.objects.filter(is_active=True)

        data = {
            'labels': [company.name for company in companies],
            'datasets': [{
                'label': 'نسبة الإنجاز (%)',
                'data': [],
                'backgroundColor': [
                    'rgba(255, 99, 132, 0.2)',
                    'rgba(54, 162, 235, 0.2)',
                    'rgba(255, 205, 86, 0.2)',
                    'rgba(75, 192, 192, 0.2)',
                    'rgba(153, 102, 255, 0.2)',
                ],
                'borderColor': [
                    'rgba(255, 99, 132, 1)',
                    'rgba(54, 162, 235, 1)',
                    'rgba(255, 205, 86, 1)',
                    'rgba(75, 192, 192, 1)',
                    'rgba(153, 102, 255, 1)',
                ],
                'borderWidth': 1
            }]
        }

        current_month = timezone.now().month
        current_year = timezone.now().year

        for company in companies:
            # حساب نسبة الإنجاز للشركة
            targets = Target.objects.filter(
                company=company, month=current_month, year=current_year
            ).aggregate(Sum('target_value'))['target_value__sum'] or 0

            achieved = StatisticalEntry.objects.filter(
                company=company, date__month=current_month, date__year=current_year
            ).aggregate(Sum('value'))['value__sum'] or 0

            percentage = (achieved / targets * 100) if targets > 0 else 0
            data['datasets'][0]['data'].append(round(percentage, 2))

        return JsonResponse(data)

    def get_detailed_monthly_data(self):
        """بيانات تفصيلية شهرية حسب نوع البيانات"""
        company_id = self.request.GET.get('company')
        item_id = self.request.GET.get('item')
        year_param = self.request.GET.get('year', timezone.now().year)
        year = int(year_param) if year_param else timezone.now().year

        # بناء الاستعلام
        entries = StatisticalEntry.objects.filter(date__year=year)
        targets = Target.objects.filter(year=year)

        if company_id:
            entries = entries.filter(company_id=company_id)
            targets = targets.filter(company_id=company_id)

        if item_id:
            entries = entries.filter(item_id=item_id)
            targets = targets.filter(item_id=item_id)

        data = {
            'labels': [dict(Target.MONTHS)[i] for i in range(1, 13)],
            'datasets': []
        }

        colors = {
            'capacity': {'bg': 'rgba(54, 162, 235, 0.6)', 'border': 'rgba(54, 162, 235, 1)'},
            'production': {'bg': 'rgba(75, 192, 192, 0.6)', 'border': 'rgba(75, 192, 192, 1)'},
            'inventory': {'bg': 'rgba(255, 205, 86, 0.6)', 'border': 'rgba(255, 205, 86, 1)'},
            'sales': {'bg': 'rgba(255, 99, 132, 0.6)', 'border': 'rgba(255, 99, 132, 1)'}
        }

        for data_type, display_name in Target.DATA_TYPES:
            monthly_data = []
            for month in range(1, 13):
                month_entries = entries.filter(data_type=data_type, date__month=month)
                total_value = month_entries.aggregate(Sum('value'))['value__sum'] or 0
                monthly_data.append(float(total_value))

            data['datasets'].append({
                'label': display_name,
                'data': monthly_data,
                'backgroundColor': colors[data_type]['bg'],
                'borderColor': colors[data_type]['border'],
                'borderWidth': 2,
                'tension': 0.4
            })

        return JsonResponse(data)

    def get_data_types_comparison(self):
        """مقارنة أنواع البيانات"""
        company_id = self.request.GET.get('company')
        year_param = self.request.GET.get('year', timezone.now().year)
        year = int(year_param) if year_param else timezone.now().year

        entries = StatisticalEntry.objects.filter(date__year=year)
        targets = Target.objects.filter(year=year)

        if company_id:
            entries = entries.filter(company_id=company_id)
            targets = targets.filter(company_id=company_id)

        data = {
            'labels': [display_name for _, display_name in Target.DATA_TYPES],
            'datasets': [
                {
                    'label': 'المستهدف',
                    'data': [],
                    'backgroundColor': 'rgba(54, 162, 235, 0.6)',
                    'borderColor': 'rgba(54, 162, 235, 1)',
                    'borderWidth': 1
                },
                {
                    'label': 'المحقق',
                    'data': [],
                    'backgroundColor': 'rgba(255, 99, 132, 0.6)',
                    'borderColor': 'rgba(255, 99, 132, 1)',
                    'borderWidth': 1
                }
            ]
        }

        for data_type, _ in Target.DATA_TYPES:
            target_sum = targets.filter(data_type=data_type).aggregate(Sum('target_value'))['target_value__sum'] or 0
            achieved_sum = entries.filter(data_type=data_type).aggregate(Sum('value'))['value__sum'] or 0

            data['datasets'][0]['data'].append(float(target_sum))
            data['datasets'][1]['data'].append(float(achieved_sum))

        return JsonResponse(data)

    def get_company_data_types(self):
        """بيانات الشركات حسب نوع البيانات"""
        data_type = self.request.GET.get('data_type', 'production')
        year_param = self.request.GET.get('year', timezone.now().year)
        year = int(year_param) if year_param else timezone.now().year

        companies = Company.objects.filter(is_active=True)
        entries = StatisticalEntry.objects.filter(data_type=data_type, date__year=year)

        data = {
            'labels': [company.name for company in companies],
            'datasets': [{
                'label': f'{dict(Target.DATA_TYPES)[data_type]} - القيمة المحققة',
                'data': [],
                'backgroundColor': [
                    'rgba(255, 99, 132, 0.8)',
                    'rgba(54, 162, 235, 0.8)',
                    'rgba(255, 205, 86, 0.8)',
                    'rgba(75, 192, 192, 0.8)',
                    'rgba(153, 102, 255, 0.8)',
                ],
                'borderWidth': 1
            }]
        }

        for company in companies:
            company_total = entries.filter(company=company).aggregate(Sum('value'))['value__sum'] or 0
            data['datasets'][0]['data'].append(float(company_total))

        return JsonResponse(data)

    def get_achievement_analysis(self):
        """تحليل نسب الإنجاز"""
        company_id = self.request.GET.get('company')
        data_type = self.request.GET.get('data_type')
        year_param = self.request.GET.get('year', timezone.now().year)
        year = int(year_param) if year_param else timezone.now().year

        # بناء الاستعلام
        targets = Target.objects.filter(year=year)
        entries = StatisticalEntry.objects.filter(date__year=year)

        if company_id:
            targets = targets.filter(company_id=company_id)
            entries = entries.filter(company_id=company_id)

        if data_type:
            targets = targets.filter(data_type=data_type)
            entries = entries.filter(data_type=data_type)

        # حساب نسب الإنجاز الشهرية
        monthly_achievements = []
        labels = []

        for month in range(1, 13):
            month_name = dict(Target.MONTHS)[month]
            labels.append(month_name)

            month_targets = targets.filter(month=month).aggregate(Sum('target_value'))['target_value__sum'] or 0
            month_achieved = entries.filter(date__month=month).aggregate(Sum('value'))['value__sum'] or 0

            achievement_percentage = (month_achieved / month_targets * 100) if month_targets > 0 else 0
            monthly_achievements.append(round(achievement_percentage, 2))

        data = {
            'labels': labels,
            'datasets': [{
                'label': 'نسبة الإنجاز (%)',
                'data': monthly_achievements,
                'backgroundColor': 'rgba(75, 192, 192, 0.6)',
                'borderColor': 'rgba(75, 192, 192, 1)',
                'borderWidth': 2,
                'tension': 0.4,
                'fill': True
            }]
        }

        return JsonResponse(data)

    def get_trend_analysis(self):
        """تحليل الاتجاهات"""
        company_id = self.request.GET.get('company')
        item_id = self.request.GET.get('item')
        data_type = self.request.GET.get('data_type', 'production')

        # الحصول على بيانات آخر 12 شهر
        from datetime import datetime, timedelta
        from dateutil.relativedelta import relativedelta

        end_date = datetime.now().date()
        start_date = end_date - relativedelta(months=11)

        entries = StatisticalEntry.objects.filter(
            data_type=data_type,
            date__gte=start_date,
            date__lte=end_date
        )

        if company_id:
            entries = entries.filter(company_id=company_id)
        if item_id:
            entries = entries.filter(item_id=item_id)

        # تجميع البيانات حسب الشهر
        monthly_data = {}
        current_date = start_date

        while current_date <= end_date:
            month_key = current_date.strftime('%Y-%m')
            month_label = f"{current_date.strftime('%B')} {current_date.year}"

            month_entries = entries.filter(
                date__year=current_date.year,
                date__month=current_date.month
            )

            total_quantity = month_entries.aggregate(Sum('quantity'))['quantity__sum'] or 0
            total_value = month_entries.aggregate(Sum('value'))['value__sum'] or 0

            monthly_data[month_label] = {
                'quantity': float(total_quantity),
                'value': float(total_value)
            }

            current_date += relativedelta(months=1)

        data = {
            'labels': list(monthly_data.keys()),
            'datasets': [
                {
                    'label': 'الكمية',
                    'data': [data['quantity'] for data in monthly_data.values()],
                    'backgroundColor': 'rgba(54, 162, 235, 0.6)',
                    'borderColor': 'rgba(54, 162, 235, 1)',
                    'borderWidth': 2,
                    'yAxisID': 'y'
                },
                {
                    'label': 'القيمة',
                    'data': [data['value'] for data in monthly_data.values()],
                    'backgroundColor': 'rgba(255, 99, 132, 0.6)',
                    'borderColor': 'rgba(255, 99, 132, 1)',
                    'borderWidth': 2,
                    'yAxisID': 'y1'
                }
            ]
        }

        return JsonResponse(data)

    def get_capacity_utilization(self):
        """تحليل استغلال الطاقة الإنتاجية"""
        company_id = self.request.GET.get('company')
        item_id = self.request.GET.get('item')
        year_param = self.request.GET.get('year', timezone.now().year)
        year = int(year_param) if year_param else timezone.now().year

        # الحصول على بيانات الطاقة الإنتاجية والإنتاج الفعلي
        capacity_entries = StatisticalEntry.objects.filter(
            data_type='capacity',
            date__year=year
        )
        production_entries = StatisticalEntry.objects.filter(
            data_type='production',
            date__year=year
        )

        if company_id:
            capacity_entries = capacity_entries.filter(company_id=company_id)
            production_entries = production_entries.filter(company_id=company_id)

        if item_id:
            capacity_entries = capacity_entries.filter(item_id=item_id)
            production_entries = production_entries.filter(item_id=item_id)

        # حساب نسبة الاستغلال الشهرية
        monthly_utilization = []
        labels = []

        for month in range(1, 13):
            month_name = dict(Target.MONTHS)[month]
            labels.append(month_name)

            month_capacity = capacity_entries.filter(date__month=month).aggregate(Sum('quantity'))['quantity__sum'] or 0
            month_production = production_entries.filter(date__month=month).aggregate(Sum('quantity'))['quantity__sum'] or 0

            utilization_percentage = (month_production / month_capacity * 100) if month_capacity > 0 else 0
            monthly_utilization.append(round(utilization_percentage, 2))

        # تحديد الألوان حسب نسبة الاستغلال
        background_colors = []
        for util in monthly_utilization:
            if util >= 90:
                background_colors.append('rgba(220, 53, 69, 0.8)')  # أحمر - استغلال عالي جداً
            elif util >= 75:
                background_colors.append('rgba(40, 167, 69, 0.8)')  # أخضر - استغلال جيد
            elif util >= 50:
                background_colors.append('rgba(255, 193, 7, 0.8)')  # أصفر - استغلال متوسط
            else:
                background_colors.append('rgba(108, 117, 125, 0.8)')  # رمادي - استغلال ضعيف

        data = {
            'labels': labels,
            'datasets': [{
                'label': 'نسبة استغلال الطاقة الإنتاجية (%)',
                'data': monthly_utilization,
                'backgroundColor': background_colors,
                'borderColor': 'rgba(54, 162, 235, 1)',
                'borderWidth': 1
            }]
        }

        return JsonResponse(data)

    def get_kpi_dashboard(self):
        """لوحة مؤشرات الأداء الرئيسية (KPI)"""
        company_id = self.request.GET.get('company')
        year_param = self.request.GET.get('year', timezone.now().year)
        year = int(year_param) if year_param else timezone.now().year

        # بناء الاستعلام
        entries = StatisticalEntry.objects.filter(date__year=year)
        targets = Target.objects.filter(year=year)

        if company_id:
            entries = entries.filter(company_id=company_id)
            targets = targets.filter(company_id=company_id)

        # حساب مؤشرات الأداء الرئيسية
        kpis = {}

        for data_type, display_name in Target.DATA_TYPES:
            type_entries = entries.filter(data_type=data_type)
            type_targets = targets.filter(data_type=data_type)

            # إجمالي المستهدف والمحقق
            total_target = type_targets.aggregate(Sum('target_value'))['target_value__sum'] or 0
            total_achieved = type_entries.aggregate(Sum('value'))['value__sum'] or 0

            # نسبة الإنجاز
            achievement_rate = (total_achieved / total_target * 100) if total_target > 0 else 0

            # معدل النمو (مقارنة مع الشهر السابق)
            current_month = timezone.now().month
            current_month_achieved = type_entries.filter(date__month=current_month).aggregate(Sum('value'))['value__sum'] or 0
            prev_month = current_month - 1 if current_month > 1 else 12
            prev_month_achieved = type_entries.filter(date__month=prev_month).aggregate(Sum('value'))['value__sum'] or 0

            growth_rate = ((current_month_achieved - prev_month_achieved) / prev_month_achieved * 100) if prev_month_achieved > 0 else 0

            # الانحراف المعياري
            monthly_values = []
            for month in range(1, 13):
                month_value = type_entries.filter(date__month=month).aggregate(Sum('value'))['value__sum'] or 0
                monthly_values.append(month_value)

            import statistics
            std_deviation = statistics.stdev(monthly_values) if len([v for v in monthly_values if v > 0]) > 1 else 0

            # معامل التباين
            mean_value = statistics.mean([v for v in monthly_values if v > 0]) if monthly_values else 0
            coefficient_of_variation = (std_deviation / mean_value * 100) if mean_value > 0 else 0

            kpis[data_type] = {
                'name': display_name,
                'achievement_rate': round(achievement_rate, 2),
                'growth_rate': round(growth_rate, 2),
                'total_target': float(total_target),
                'total_achieved': float(total_achieved),
                'variance': float(total_achieved - total_target),
                'std_deviation': round(std_deviation, 2),
                'coefficient_of_variation': round(coefficient_of_variation, 2)
            }

        return JsonResponse({'kpis': kpis})

    def get_variance_analysis(self):
        """تحليل التباين (Variance Analysis)"""
        company_id = self.request.GET.get('company')
        data_type = self.request.GET.get('data_type', 'production')
        year_param = self.request.GET.get('year', timezone.now().year)
        year = int(year_param) if year_param else timezone.now().year

        entries = StatisticalEntry.objects.filter(data_type=data_type, date__year=year)
        targets = Target.objects.filter(data_type=data_type, year=year)

        if company_id:
            entries = entries.filter(company_id=company_id)
            targets = targets.filter(company_id=company_id)

        variance_data = {
            'labels': [dict(Target.MONTHS)[i] for i in range(1, 13)],
            'datasets': [
                {
                    'label': 'التباين المطلق',
                    'data': [],
                    'backgroundColor': 'rgba(255, 99, 132, 0.6)',
                    'borderColor': 'rgba(255, 99, 132, 1)',
                    'borderWidth': 2,
                    'yAxisID': 'y'
                },
                {
                    'label': 'التباين النسبي (%)',
                    'data': [],
                    'backgroundColor': 'rgba(54, 162, 235, 0.6)',
                    'borderColor': 'rgba(54, 162, 235, 1)',
                    'borderWidth': 2,
                    'type': 'line',
                    'yAxisID': 'y1'
                }
            ]
        }

        for month in range(1, 13):
            month_target = targets.filter(month=month).aggregate(Sum('target_value'))['target_value__sum'] or 0
            month_achieved = entries.filter(date__month=month).aggregate(Sum('value'))['value__sum'] or 0

            absolute_variance = month_achieved - month_target
            relative_variance = (absolute_variance / month_target * 100) if month_target > 0 else 0

            variance_data['datasets'][0]['data'].append(float(absolute_variance))
            variance_data['datasets'][1]['data'].append(round(relative_variance, 2))

        return JsonResponse(variance_data)

    def get_efficiency_metrics(self):
        """مقاييس الكفاءة"""
        company_id = self.request.GET.get('company')
        year_param = self.request.GET.get('year', timezone.now().year)
        year = int(year_param) if year_param else timezone.now().year

        entries = StatisticalEntry.objects.filter(date__year=year)
        if company_id:
            entries = entries.filter(company_id=company_id)

        # حساب مقاييس الكفاءة
        efficiency_data = {
            'labels': [dict(Target.MONTHS)[i] for i in range(1, 13)],
            'datasets': []
        }

        # كفاءة الإنتاج (الإنتاج / الطاقة الإنتاجية)
        production_efficiency = []
        # كفاءة المبيعات (المبيعات / الإنتاج)
        sales_efficiency = []
        # معدل دوران المخزون (المبيعات / المخزون)
        inventory_turnover = []

        for month in range(1, 13):
            month_entries = entries.filter(date__month=month)

            capacity = month_entries.filter(data_type='capacity').aggregate(Sum('quantity'))['quantity__sum'] or 0
            production = month_entries.filter(data_type='production').aggregate(Sum('quantity'))['quantity__sum'] or 0
            inventory = month_entries.filter(data_type='inventory').aggregate(Sum('quantity'))['quantity__sum'] or 0
            sales = month_entries.filter(data_type='sales').aggregate(Sum('quantity'))['quantity__sum'] or 0

            prod_eff = (production / capacity * 100) if capacity > 0 else 0
            sales_eff = (sales / production * 100) if production > 0 else 0
            inv_turnover = (sales / inventory) if inventory > 0 else 0

            production_efficiency.append(round(prod_eff, 2))
            sales_efficiency.append(round(sales_eff, 2))
            inventory_turnover.append(round(inv_turnover, 2))

        efficiency_data['datasets'] = [
            {
                'label': 'كفاءة الإنتاج (%)',
                'data': production_efficiency,
                'backgroundColor': 'rgba(75, 192, 192, 0.6)',
                'borderColor': 'rgba(75, 192, 192, 1)',
                'borderWidth': 2
            },
            {
                'label': 'كفاءة المبيعات (%)',
                'data': sales_efficiency,
                'backgroundColor': 'rgba(255, 205, 86, 0.6)',
                'borderColor': 'rgba(255, 205, 86, 1)',
                'borderWidth': 2
            },
            {
                'label': 'معدل دوران المخزون',
                'data': inventory_turnover,
                'backgroundColor': 'rgba(153, 102, 255, 0.6)',
                'borderColor': 'rgba(153, 102, 255, 1)',
                'borderWidth': 2,
                'yAxisID': 'y1'
            }
        ]

        return JsonResponse(efficiency_data)

    def get_seasonal_analysis(self):
        """التحليل الموسمي"""
        company_id = self.request.GET.get('company')
        data_type = self.request.GET.get('data_type', 'production')

        # الحصول على بيانات آخر 3 سنوات
        current_year = timezone.now().year
        years = [current_year - 2, current_year - 1, current_year]

        entries = StatisticalEntry.objects.filter(
            data_type=data_type,
            date__year__in=years
        )

        if company_id:
            entries = entries.filter(company_id=company_id)

        seasonal_data = {
            'labels': [dict(Target.MONTHS)[i] for i in range(1, 13)],
            'datasets': []
        }

        colors = [
            {'bg': 'rgba(255, 99, 132, 0.6)', 'border': 'rgba(255, 99, 132, 1)'},
            {'bg': 'rgba(54, 162, 235, 0.6)', 'border': 'rgba(54, 162, 235, 1)'},
            {'bg': 'rgba(75, 192, 192, 0.6)', 'border': 'rgba(75, 192, 192, 1)'}
        ]

        for i, year in enumerate(years):
            year_data = []
            for month in range(1, 13):
                month_value = entries.filter(
                    date__year=year,
                    date__month=month
                ).aggregate(Sum('value'))['value__sum'] or 0
                year_data.append(float(month_value))

            seasonal_data['datasets'].append({
                'label': f'سنة {year}',
                'data': year_data,
                'backgroundColor': colors[i]['bg'],
                'borderColor': colors[i]['border'],
                'borderWidth': 2,
                'tension': 0.4
            })

        # إضافة المتوسط الموسمي
        seasonal_avg = []
        for month in range(1, 13):
            month_values = []
            for year in years:
                month_value = entries.filter(
                    date__year=year,
                    date__month=month
                ).aggregate(Sum('value'))['value__sum'] or 0
                if month_value > 0:
                    month_values.append(month_value)

            avg_value = sum(month_values) / len(month_values) if month_values else 0
            seasonal_avg.append(round(avg_value, 2))

        seasonal_data['datasets'].append({
            'label': 'المتوسط الموسمي',
            'data': seasonal_avg,
            'backgroundColor': 'rgba(255, 205, 86, 0.8)',
            'borderColor': 'rgba(255, 205, 86, 1)',
            'borderWidth': 3,
            'borderDash': [5, 5],
            'tension': 0.4
        })

        return JsonResponse(seasonal_data)

    def get_performance_ranking(self):
        """ترتيب الأداء"""
        data_type = self.request.GET.get('data_type', 'production')
        year_param = self.request.GET.get('year', timezone.now().year)
        year = int(year_param) if year_param else timezone.now().year

        companies = Company.objects.filter(is_active=True)
        performance_data = []

        for company in companies:
            entries = StatisticalEntry.objects.filter(
                company=company,
                data_type=data_type,
                date__year=year
            )
            targets = Target.objects.filter(
                company=company,
                data_type=data_type,
                year=year
            )

            total_achieved = entries.aggregate(Sum('value'))['value__sum'] or 0
            total_target = targets.aggregate(Sum('target_value'))['target_value__sum'] or 0

            achievement_rate = (total_achieved / total_target * 100) if total_target > 0 else 0

            # حساب نقاط الأداء (مركب من عدة معايير)
            consistency_score = self.calculate_consistency_score(entries, targets)
            growth_score = self.calculate_growth_score(entries)
            efficiency_score = self.calculate_efficiency_score(company, year)

            overall_score = (achievement_rate * 0.4 + consistency_score * 0.3 +
                           float(growth_score) * 0.2 + float(efficiency_score) * 0.1)

            performance_data.append({
                'company': company.name,
                'achievement_rate': round(achievement_rate, 2),
                'consistency_score': round(consistency_score, 2),
                'growth_score': round(growth_score, 2),
                'efficiency_score': round(efficiency_score, 2),
                'overall_score': round(overall_score, 2),
                'total_achieved': float(total_achieved),
                'total_target': float(total_target)
            })

        # ترتيب حسب النقاط الإجمالية
        performance_data.sort(key=lambda x: x['overall_score'], reverse=True)

        # إضافة الترتيب
        for i, item in enumerate(performance_data):
            item['rank'] = i + 1

        return JsonResponse({
            'rankings': performance_data,
            'chart_data': {
                'labels': [item['company'] for item in performance_data],
                'datasets': [{
                    'label': 'نقاط الأداء الإجمالية',
                    'data': [item['overall_score'] for item in performance_data],
                    'backgroundColor': [
                        'rgba(255, 215, 0, 0.8)' if i == 0 else  # ذهبي للأول
                        'rgba(192, 192, 192, 0.8)' if i == 1 else  # فضي للثاني
                        'rgba(205, 127, 50, 0.8)' if i == 2 else  # برونزي للثالث
                        'rgba(54, 162, 235, 0.8)'  # أزرق للباقي
                        for i in range(len(performance_data))
                    ],
                    'borderWidth': 1
                }]
            }
        })

    def calculate_consistency_score(self, entries, targets):
        """حساب نقاط الثبات في الأداء"""
        monthly_rates = []
        for month in range(1, 13):
            month_achieved = entries.filter(date__month=month).aggregate(Sum('value'))['value__sum'] or 0
            month_target = targets.filter(month=month).aggregate(Sum('target_value'))['target_value__sum'] or 0

            if month_target > 0:
                rate = month_achieved / month_target * 100
                monthly_rates.append(rate)

        if len(monthly_rates) < 2:
            return 0

        import statistics
        std_dev = statistics.stdev(monthly_rates)
        mean_rate = statistics.mean(monthly_rates)

        # نقاط الثبات = 100 - معامل التباين
        coefficient_of_variation = (std_dev / mean_rate) if mean_rate > 0 else 0
        consistency_score = max(0, 100 - (coefficient_of_variation * 10))

        return consistency_score

    def calculate_growth_score(self, entries):
        """حساب نقاط النمو"""
        monthly_values = []
        for month in range(1, 13):
            month_value = entries.filter(date__month=month).aggregate(Sum('value'))['value__sum'] or 0
            monthly_values.append(month_value)

        # حساب معدل النمو الشهري
        growth_rates = []
        for i in range(1, len(monthly_values)):
            if monthly_values[i-1] > 0:
                growth_rate = (monthly_values[i] - monthly_values[i-1]) / monthly_values[i-1] * 100
                growth_rates.append(growth_rate)

        if not growth_rates:
            return 50  # نقاط محايدة

        avg_growth = sum(growth_rates) / len(growth_rates)
        # تحويل معدل النمو إلى نقاط (0-100)
        growth_score = min(100, max(0, 50 + avg_growth * 2))

        return growth_score

    def calculate_efficiency_score(self, company, year):
        """حساب نقاط الكفاءة"""
        entries = StatisticalEntry.objects.filter(company=company, date__year=year)

        # حساب كفاءة الإنتاج
        capacity_total = entries.filter(data_type='capacity').aggregate(Sum('quantity'))['quantity__sum'] or 0
        production_total = entries.filter(data_type='production').aggregate(Sum('quantity'))['quantity__sum'] or 0

        production_efficiency = (production_total / capacity_total * 100) if capacity_total > 0 else 0

        # حساب كفاءة المبيعات
        sales_total = entries.filter(data_type='sales').aggregate(Sum('quantity'))['quantity__sum'] or 0
        sales_efficiency = (sales_total / production_total * 100) if production_total > 0 else 0

        # متوسط الكفاءة
        efficiency_score = (production_efficiency + sales_efficiency) / 2

        return min(100, efficiency_score)

    def get_growth_analysis(self):
        """تحليل النمو"""
        company_id = self.request.GET.get('company')
        data_type = self.request.GET.get('data_type', 'production')

        # الحصول على بيانات آخر سنتين
        current_year = timezone.now().year
        previous_year = current_year - 1

        current_entries = StatisticalEntry.objects.filter(
            data_type=data_type,
            date__year=current_year
        )
        previous_entries = StatisticalEntry.objects.filter(
            data_type=data_type,
            date__year=previous_year
        )

        if company_id:
            current_entries = current_entries.filter(company_id=company_id)
            previous_entries = previous_entries.filter(company_id=company_id)

        growth_data = {
            'labels': [dict(Target.MONTHS)[i] for i in range(1, 13)],
            'datasets': [
                {
                    'label': f'سنة {current_year}',
                    'data': [],
                    'backgroundColor': 'rgba(54, 162, 235, 0.6)',
                    'borderColor': 'rgba(54, 162, 235, 1)',
                    'borderWidth': 2
                },
                {
                    'label': f'سنة {previous_year}',
                    'data': [],
                    'backgroundColor': 'rgba(255, 99, 132, 0.6)',
                    'borderColor': 'rgba(255, 99, 132, 1)',
                    'borderWidth': 2
                },
                {
                    'label': 'معدل النمو (%)',
                    'data': [],
                    'backgroundColor': 'rgba(75, 192, 192, 0.6)',
                    'borderColor': 'rgba(75, 192, 192, 1)',
                    'borderWidth': 2,
                    'type': 'line',
                    'yAxisID': 'y1'
                }
            ]
        }

        for month in range(1, 13):
            current_value = current_entries.filter(date__month=month).aggregate(Sum('value'))['value__sum'] or 0
            previous_value = previous_entries.filter(date__month=month).aggregate(Sum('value'))['value__sum'] or 0

            growth_rate = ((current_value - previous_value) / previous_value * 100) if previous_value > 0 else 0

            growth_data['datasets'][0]['data'].append(float(current_value))
            growth_data['datasets'][1]['data'].append(float(previous_value))
            growth_data['datasets'][2]['data'].append(round(growth_rate, 2))

        return JsonResponse(growth_data)

    def get_risk_assessment(self):
        """تقييم المخاطر"""
        company_id = self.request.GET.get('company')
        year_param = self.request.GET.get('year', timezone.now().year)
        year = int(year_param) if year_param else timezone.now().year

        entries = StatisticalEntry.objects.filter(date__year=year)
        targets = Target.objects.filter(year=year)

        if company_id:
            entries = entries.filter(company_id=company_id)
            targets = targets.filter(company_id=company_id)

        risk_metrics = {}

        for data_type, display_name in Target.DATA_TYPES:
            type_entries = entries.filter(data_type=data_type)
            type_targets = targets.filter(data_type=data_type)

            # حساب مؤشرات المخاطر
            monthly_achievement_rates = []
            for month in range(1, 13):
                month_target = type_targets.filter(month=month).aggregate(Sum('target_value'))['target_value__sum'] or 0
                month_achieved = type_entries.filter(date__month=month).aggregate(Sum('value'))['value__sum'] or 0

                if month_target > 0:
                    rate = month_achieved / month_target * 100
                    monthly_achievement_rates.append(rate)

            if monthly_achievement_rates:
                import statistics

                # التقلبات (الانحراف المعياري)
                volatility = statistics.stdev(monthly_achievement_rates) if len(monthly_achievement_rates) > 1 else 0

                # احتمالية عدم تحقيق الهدف
                below_target_months = len([rate for rate in monthly_achievement_rates if rate < 100])
                failure_probability = (below_target_months / len(monthly_achievement_rates)) * 100

                # مؤشر المخاطر المركب
                risk_score = (float(volatility) * 0.6 + float(failure_probability) * 0.4)

                # تصنيف المخاطر
                if risk_score < 20:
                    risk_level = 'منخفض'
                    risk_color = 'green'
                elif risk_score < 40:
                    risk_level = 'متوسط'
                    risk_color = 'yellow'
                elif risk_score < 60:
                    risk_level = 'عالي'
                    risk_color = 'orange'
                else:
                    risk_level = 'عالي جداً'
                    risk_color = 'red'

                risk_metrics[data_type] = {
                    'name': display_name,
                    'volatility': round(volatility, 2),
                    'failure_probability': round(failure_probability, 2),
                    'risk_score': round(risk_score, 2),
                    'risk_level': risk_level,
                    'risk_color': risk_color,
                    'monthly_rates': [round(rate, 2) for rate in monthly_achievement_rates]
                }

        return JsonResponse({'risk_metrics': risk_metrics})

    def get_forecasting_data(self):
        """بيانات التنبؤ"""
        company_id = self.request.GET.get('company')
        data_type = self.request.GET.get('data_type', 'production')

        # الحصول على بيانات آخر 12 شهر
        from datetime import datetime, timedelta
        from dateutil.relativedelta import relativedelta

        end_date = datetime.now().date()
        start_date = end_date - relativedelta(months=11)

        entries = StatisticalEntry.objects.filter(
            data_type=data_type,
            date__gte=start_date,
            date__lte=end_date
        )

        if company_id:
            entries = entries.filter(company_id=company_id)

        # جمع البيانات التاريخية
        historical_data = []
        historical_labels = []

        current_date = start_date
        while current_date <= end_date:
            month_value = entries.filter(
                date__year=current_date.year,
                date__month=current_date.month
            ).aggregate(Sum('value'))['value__sum'] or 0

            historical_data.append(float(month_value))
            historical_labels.append(current_date.strftime('%Y-%m'))
            current_date += relativedelta(months=1)

        # حساب التنبؤ باستخدام المتوسط المتحرك والاتجاه الخطي
        forecast_data = []
        forecast_labels = []

        # التنبؤ لـ 6 أشهر قادمة
        for i in range(6):
            future_date = end_date + relativedelta(months=i+1)
            forecast_labels.append(future_date.strftime('%Y-%m'))

            # متوسط متحرك بسيط لآخر 3 أشهر
            if len(historical_data) >= 3:
                recent_avg = sum(historical_data[-3:]) / 3

                # حساب الاتجاه
                if len(historical_data) >= 6:
                    first_half_avg = sum(historical_data[-6:-3]) / 3
                    second_half_avg = sum(historical_data[-3:]) / 3
                    trend = second_half_avg - first_half_avg
                else:
                    trend = 0

                # التنبؤ = المتوسط الحديث + الاتجاه * عدد الأشهر
                forecast_value = recent_avg + (trend * (i + 1))
                forecast_data.append(max(0, forecast_value))  # تجنب القيم السالبة
            else:
                forecast_data.append(0)

        # دمج البيانات
        all_labels = historical_labels + forecast_labels

        forecast_chart_data = {
            'labels': all_labels,
            'datasets': [
                {
                    'label': 'البيانات التاريخية',
                    'data': historical_data + [None] * 6,
                    'backgroundColor': 'rgba(54, 162, 235, 0.6)',
                    'borderColor': 'rgba(54, 162, 235, 1)',
                    'borderWidth': 2,
                    'tension': 0.4
                },
                {
                    'label': 'التنبؤ',
                    'data': [None] * 12 + forecast_data,
                    'backgroundColor': 'rgba(255, 99, 132, 0.6)',
                    'borderColor': 'rgba(255, 99, 132, 1)',
                    'borderWidth': 2,
                    'borderDash': [5, 5],
                    'tension': 0.4
                }
            ]
        }

        return JsonResponse(forecast_chart_data)

    def get_benchmark_analysis(self):
        """تحليل المعايير المرجعية"""
        data_type = self.request.GET.get('data_type', 'production')
        year_param = self.request.GET.get('year', timezone.now().year)
        year = int(year_param) if year_param else timezone.now().year

        companies = Company.objects.filter(is_active=True)

        # حساب الإحصائيات المرجعية
        all_achievement_rates = []
        company_data = []

        for company in companies:
            entries = StatisticalEntry.objects.filter(
                company=company,
                data_type=data_type,
                date__year=year
            )
            targets = Target.objects.filter(
                company=company,
                data_type=data_type,
                year=year
            )

            total_achieved = entries.aggregate(Sum('value'))['value__sum'] or 0
            total_target = targets.aggregate(Sum('target_value'))['target_value__sum'] or 0

            achievement_rate = (total_achieved / total_target * 100) if total_target > 0 else 0
            all_achievement_rates.append(achievement_rate)

            company_data.append({
                'name': company.name,
                'achievement_rate': achievement_rate,
                'total_achieved': float(total_achieved),
                'total_target': float(total_target)
            })

        if all_achievement_rates:
            import statistics

            # حساب المعايير المرجعية
            benchmark_stats = {
                'mean': statistics.mean(all_achievement_rates),
                'median': statistics.median(all_achievement_rates),
                'q1': statistics.quantiles(all_achievement_rates, n=4)[0] if len(all_achievement_rates) >= 4 else 0,
                'q3': statistics.quantiles(all_achievement_rates, n=4)[2] if len(all_achievement_rates) >= 4 else 0,
                'min': min(all_achievement_rates),
                'max': max(all_achievement_rates),
                'std_dev': statistics.stdev(all_achievement_rates) if len(all_achievement_rates) > 1 else 0
            }

            # تصنيف الشركات
            for company in company_data:
                rate = company['achievement_rate']
                if rate >= benchmark_stats['q3']:
                    company['performance_tier'] = 'ممتاز'
                    company['tier_color'] = 'green'
                elif rate >= benchmark_stats['median']:
                    company['performance_tier'] = 'جيد'
                    company['tier_color'] = 'blue'
                elif rate >= benchmark_stats['q1']:
                    company['performance_tier'] = 'متوسط'
                    company['tier_color'] = 'orange'
                else:
                    company['performance_tier'] = 'ضعيف'
                    company['tier_color'] = 'red'
        else:
            benchmark_stats = {}

        # إعداد بيانات الرسم البياني
        chart_data = {
            'labels': [company['name'] for company in company_data],
            'datasets': [
                {
                    'label': 'نسبة الإنجاز',
                    'data': [company['achievement_rate'] for company in company_data],
                    'backgroundColor': [
                        'rgba(75, 192, 192, 0.8)' if company['tier_color'] == 'green' else
                        'rgba(54, 162, 235, 0.8)' if company['tier_color'] == 'blue' else
                        'rgba(255, 205, 86, 0.8)' if company['tier_color'] == 'orange' else
                        'rgba(255, 99, 132, 0.8)'
                        for company in company_data
                    ],
                    'borderWidth': 1
                }
            ]
        }

        # إضافة خطوط المعايير المرجعية
        if benchmark_stats:
            chart_data['benchmark_lines'] = {
                'mean': round(benchmark_stats['mean'], 2),
                'median': round(benchmark_stats['median'], 2),
                'q3': round(benchmark_stats['q3'], 2),
                'q1': round(benchmark_stats['q1'], 2)
            }

        return JsonResponse({
            'chart_data': chart_data,
            'benchmark_stats': {k: round(v, 2) for k, v in benchmark_stats.items()} if benchmark_stats else {},
            'company_analysis': company_data
        })

    def get_correlation_matrix(self):
        """مصفوفة الارتباط بين أنواع البيانات"""
        company_id = self.request.GET.get('company')
        year_param = self.request.GET.get('year', timezone.now().year)
        year = int(year_param) if year_param else timezone.now().year

        entries = StatisticalEntry.objects.filter(date__year=year)
        if company_id:
            entries = entries.filter(company_id=company_id)

        # جمع البيانات الشهرية لكل نوع
        data_types = ['capacity', 'production', 'inventory', 'sales']
        monthly_data = {dt: [] for dt in data_types}

        for month in range(1, 13):
            for data_type in data_types:
                month_value = entries.filter(
                    data_type=data_type,
                    date__month=month
                ).aggregate(Sum('value'))['value__sum'] or 0
                monthly_data[data_type].append(month_value)

        # حساب معاملات الارتباط
        correlations = {}

        for i, dt1 in enumerate(data_types):
            correlations[dt1] = {}
            for j, dt2 in enumerate(data_types):
                if i == j:
                    correlation = 1.0
                else:
                    # حساب معامل الارتباط بيرسون
                    correlation = self.calculate_correlation(monthly_data[dt1], monthly_data[dt2])

                correlations[dt1][dt2] = round(correlation, 3)

        # إعداد بيانات الرسم البياني (heatmap)
        heatmap_data = []
        labels = [dict(Target.DATA_TYPES)[dt] for dt in data_types]

        for i, dt1 in enumerate(data_types):
            row_data = []
            for j, dt2 in enumerate(data_types):
                correlation_value = correlations[dt1][dt2]
                row_data.append({
                    'x': j,
                    'y': i,
                    'v': correlation_value
                })
            heatmap_data.extend(row_data)

        return JsonResponse({
            'correlations': correlations,
            'heatmap_data': heatmap_data,
            'labels': labels,
            'data_types_map': dict(Target.DATA_TYPES)
        })

    def calculate_correlation(self, x_values, y_values):
        """حساب معامل الارتباط بيرسون"""
        if len(x_values) != len(y_values) or len(x_values) < 2:
            return 0

        # إزالة الأزواج التي تحتوي على قيم صفر
        pairs = [(x, y) for x, y in zip(x_values, y_values) if x > 0 and y > 0]

        if len(pairs) < 2:
            return 0

        x_vals = [pair[0] for pair in pairs]
        y_vals = [pair[1] for pair in pairs]

        n = len(x_vals)
        sum_x = sum(x_vals)
        sum_y = sum(y_vals)
        sum_xy = sum(x * y for x, y in zip(x_vals, y_vals))
        sum_x2 = sum(x * x for x in x_vals)
        sum_y2 = sum(y * y for y in y_vals)

        numerator = n * sum_xy - sum_x * sum_y
        denominator_value = (n * sum_x2 - sum_x * sum_x) * (n * sum_y2 - sum_y * sum_y)
        denominator = float(denominator_value) ** 0.5

        if denominator == 0:
            return 0

        return numerator / denominator
