#!/usr/bin/env python
"""
سكريپت اختبار شامل لفصل البيانات
"""

import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'statistical_system.settings')
django.setup()

from django.contrib.auth.models import User
from stats_app.models import UserProfile, Company, StatisticalEntry, Target, Item
from django.test import Client
from django.urls import reverse

def create_test_data():
    """إنشاء بيانات اختبار"""
    print("📊 إنشاء بيانات اختبار...")
    
    # التأكد من وجود شركتين على الأقل
    company1, created1 = Company.objects.get_or_create(
        name="شركة الأسمنت السعودية",
        defaults={
            'sector': 'صناعة الأسمنت',
            'is_active': True
        }
    )

    company2, created2 = Company.objects.get_or_create(
        name="شركة الأسمنت الأردنية",
        defaults={
            'sector': 'صناعة الأسمنت',
            'is_active': True
        }
    )
    
    if created1:
        print(f"✅ تم إنشاء: {company1.name}")
    if created2:
        print(f"✅ تم إنشاء: {company2.name}")
    
    # التأكد من وجود عنصر
    item, created = Item.objects.get_or_create(
        name="الأسمنت العادي",
        defaults={
            'unit': 'طن',
            'is_active': True
        }
    )
    
    if created:
        print(f"✅ تم إنشاء العنصر: {item.name}")
    
    # إنشاء بيانات إحصائية للشركة الأولى
    entry1, created = StatisticalEntry.objects.get_or_create(
        company=company1,
        item=item,
        data_type='production',
        date='2024-01-01',
        defaults={
            'quantity': 1000,
            'value': 500000,
            'created_by': User.objects.get(username='admin')
        }
    )
    
    # إنشاء بيانات إحصائية للشركة الثانية
    entry2, created = StatisticalEntry.objects.get_or_create(
        company=company2,
        item=item,
        data_type='production',
        date='2024-01-01',
        defaults={
            'quantity': 800,
            'value': 400000,
            'created_by': User.objects.get(username='admin')
        }
    )
    
    print(f"📊 إجمالي الشركات: {Company.objects.count()}")
    print(f"📊 إجمالي الإدخالات: {StatisticalEntry.objects.count()}")
    
    return company1, company2

def test_user_data_access():
    """اختبار وصول المستخدمين للبيانات"""
    print("\n🧪 اختبار وصول المستخدمين للبيانات")
    print("="*50)
    
    # إنشاء بيانات الاختبار
    company1, company2 = create_test_data()
    
    # ربط المستخدمين بالشركات
    try:
        # مستخدم الشركة الأولى
        user1 = User.objects.get(username='alaa1')
        profile1 = user1.userprofile
        profile1.company = company1
        profile1.is_company_user = True
        profile1.is_system_admin = False
        profile1.save()
        
        # مستخدم الشركة الثانية
        user2 = User.objects.get(username='alaa2')
        profile2 = user2.userprofile
        profile2.company = company2
        profile2.is_company_user = True
        profile2.is_system_admin = False
        profile2.save()
        
        # مدير النظام
        admin_user = User.objects.get(username='alaa')
        admin_profile = admin_user.userprofile
        admin_profile.is_system_admin = True
        admin_profile.is_company_user = False
        admin_profile.company = None
        admin_profile.save()
        
        print("✅ تم ربط المستخدمين بالشركات")
        
    except Exception as e:
        print(f"❌ خطأ في ربط المستخدمين: {e}")
        return False
    
    # اختبار الوصول للبيانات
    test_results = []
    
    # اختبار مستخدم الشركة الأولى
    print(f"\n👤 اختبار المستخدم: {user1.username} (شركة: {company1.name})")
    
    # محاكاة طلب من المستخدم
    from stats_app.views import CompanyDataMixin
    
    class MockRequest:
        def __init__(self, user):
            self.user = user
    
    class TestView(CompanyDataMixin):
        def __init__(self, user):
            self.request = MockRequest(user)
    
    # اختبار مستخدم الشركة الأولى
    view1 = TestView(user1)
    allowed_companies1 = view1.get_user_companies()
    filtered_entries1 = view1.filter_queryset_by_company(StatisticalEntry.objects.all())
    
    print(f"   🏢 الشركات المسموحة: {allowed_companies1.count()}")
    print(f"   📊 الإدخالات المرئية: {filtered_entries1.count()}")
    
    if allowed_companies1.count() == 1 and allowed_companies1.first() == company1:
        print("   ✅ يرى شركته فقط")
        test_results.append(True)
    else:
        print("   ❌ يرى شركات أخرى")
        test_results.append(False)
    
    # اختبار مستخدم الشركة الثانية
    print(f"\n👤 اختبار المستخدم: {user2.username} (شركة: {company2.name})")
    
    view2 = TestView(user2)
    allowed_companies2 = view2.get_user_companies()
    filtered_entries2 = view2.filter_queryset_by_company(StatisticalEntry.objects.all())
    
    print(f"   🏢 الشركات المسموحة: {allowed_companies2.count()}")
    print(f"   📊 الإدخالات المرئية: {filtered_entries2.count()}")
    
    if allowed_companies2.count() == 1 and allowed_companies2.first() == company2:
        print("   ✅ يرى شركته فقط")
        test_results.append(True)
    else:
        print("   ❌ يرى شركات أخرى")
        test_results.append(False)
    
    # اختبار مدير النظام
    print(f"\n👤 اختبار المستخدم: {admin_user.username} (مدير نظام)")
    
    view_admin = TestView(admin_user)
    allowed_companies_admin = view_admin.get_user_companies()
    filtered_entries_admin = view_admin.filter_queryset_by_company(StatisticalEntry.objects.all())
    
    print(f"   🏢 الشركات المسموحة: {allowed_companies_admin.count()}")
    print(f"   📊 الإدخالات المرئية: {filtered_entries_admin.count()}")
    
    total_companies = Company.objects.count()
    total_entries = StatisticalEntry.objects.count()
    
    if allowed_companies_admin.count() == total_companies and filtered_entries_admin.count() == total_entries:
        print("   ✅ يرى جميع البيانات")
        test_results.append(True)
    else:
        print("   ❌ لا يرى جميع البيانات")
        test_results.append(False)
    
    return all(test_results)

def test_web_interface():
    """اختبار واجهة الويب"""
    print("\n🌐 اختبار واجهة الويب")
    print("="*30)
    
    client = Client()
    
    # اختبار تسجيل الدخول ومشاهدة البيانات
    test_users = [
        ('alaa1', 'alaa1123', 'مستخدم شركة'),
        ('alaa2', 'alaa2123', 'مستخدم شركة'),
        ('alaa', 'alaa123', 'مدير نظام'),
    ]
    
    web_results = []
    
    for username, password, role in test_users:
        print(f"\n👤 اختبار {role}: {username}")
        
        # تسجيل الدخول
        login_success = client.login(username=username, password=password)
        
        if login_success:
            print("   ✅ تسجيل الدخول ناجح")
            
            # اختبار الوصول للصفحات
            test_urls = [
                ('/', 'الصفحة الرئيسية'),
                ('/companies/', 'قائمة الشركات'),
                ('/entries/', 'الإدخالات الإحصائية'),
                ('/reports/', 'التقارير'),
            ]
            
            page_results = []
            
            for url, name in test_urls:
                try:
                    response = client.get(url)
                    if response.status_code == 200:
                        print(f"   ✅ {name}: يمكن الوصول")
                        page_results.append(True)
                    else:
                        print(f"   ❌ {name}: خطأ {response.status_code}")
                        page_results.append(False)
                except Exception as e:
                    print(f"   ❌ {name}: خطأ {e}")
                    page_results.append(False)
            
            web_results.append(all(page_results))
            
        else:
            print("   ❌ فشل في تسجيل الدخول")
            web_results.append(False)
        
        # تسجيل الخروج
        client.logout()
    
    return all(web_results)

def generate_test_report():
    """إنشاء تقرير الاختبار"""
    print("\n📋 تقرير اختبار فصل البيانات")
    print("="*50)
    
    # اختبار وصول البيانات
    data_access_success = test_user_data_access()
    
    # اختبار واجهة الويب
    web_interface_success = test_web_interface()
    
    print("\n" + "="*50)
    print("📊 ملخص النتائج:")
    print(f"   فصل البيانات: {'✅ يعمل' if data_access_success else '❌ لا يعمل'}")
    print(f"   واجهة الويب: {'✅ تعمل' if web_interface_success else '❌ لا تعمل'}")
    
    if data_access_success and web_interface_success:
        print("\n🎉 جميع الاختبارات نجحت!")
        print("✅ فصل البيانات يعمل بشكل مثالي")
        print("✅ كل مستخدم يرى بيانات شركته فقط")
        print("✅ مدير النظام يرى جميع البيانات")
        
        print("\n📋 التوصيات:")
        print("• يمكن الآن استخدام النظام في الإنتاج")
        print("• تأكد من إنشاء مستخدمين لكل شركة")
        print("• راجع الصلاحيات بانتظام")
        
    else:
        print("\n⚠️ بعض الاختبارات فشلت")
        print("يرجى مراجعة الأخطاء أعلاه وإصلاحها")
    
    print("="*50)
    
    return data_access_success and web_interface_success

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار شامل لفصل البيانات")
    print("="*60)
    
    success = generate_test_report()
    
    if success:
        print("\n🎯 النظام جاهز للاستخدام!")
    else:
        print("\n⚠️ النظام يحتاج إلى مراجعة")

if __name__ == '__main__':
    main()
