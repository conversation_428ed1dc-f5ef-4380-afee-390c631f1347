#!/usr/bin/env python
"""
اختبار إعدادات السنة المالية
"""

import os
import sys
import django
from datetime import date, datetime

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'statistical_system.settings')
django.setup()

from stats_app.models import SystemSettings, Company, Item, Target, StatisticalEntry
from django.contrib.auth.models import User

def test_system_settings():
    """اختبار إعدادات النظام"""
    print("⚙️ اختبار إعدادات النظام")
    print("="*40)
    
    # إنشاء أو الحصول على الإعدادات
    settings = SystemSettings.get_settings()
    
    print(f"✅ إعدادات النظام:")
    print(f"   📅 بداية السنة المالية: {settings.fiscal_year_start_day}/{settings.fiscal_year_start_month}")
    print(f"   📅 نهاية السنة المالية: {settings.fiscal_year_end_day}/{settings.fiscal_year_end_month}")
    print(f"   🏢 اسم النظام: {settings.system_name}")
    print(f"   💰 العملة الافتراضية: {settings.default_currency}")
    print(f"   📊 المنازل العشرية: {settings.reports_decimal_places}")
    print(f"   📈 عرض السنة المالية: {settings.show_fiscal_year_in_reports}")
    
    return settings

def test_fiscal_year_calculations():
    """اختبار حسابات السنة المالية"""
    print(f"\n📅 اختبار حسابات السنة المالية")
    print("-"*35)
    
    settings = SystemSettings.get_settings()
    
    # السنة المالية الحالية
    current_fiscal_year = settings.get_current_fiscal_year()
    print(f"✅ السنة المالية الحالية: {current_fiscal_year}")
    
    # تواريخ السنة المالية
    start_date = settings.get_fiscal_year_start_date(current_fiscal_year)
    end_date = settings.get_fiscal_year_end_date(current_fiscal_year)
    print(f"✅ تاريخ البداية: {start_date}")
    print(f"✅ تاريخ النهاية: {end_date}")
    
    # أشهر السنة المالية
    fiscal_months = settings.get_fiscal_year_months(current_fiscal_year)
    print(f"✅ أشهر السنة المالية ({len(fiscal_months)} شهر):")
    for month in fiscal_months:
        print(f"   • {month['display']}")
    
    # اختبار تواريخ مختلفة
    test_dates = [
        date(2024, 1, 15),  # يناير
        date(2024, 6, 30),  # يونيو
        date(2024, 7, 1),   # يوليو
        date(2024, 12, 31), # ديسمبر
    ]
    
    print(f"\n🧪 اختبار تواريخ مختلفة:")
    for test_date in test_dates:
        is_in_fiscal_year = settings.is_date_in_fiscal_year(test_date, current_fiscal_year)
        print(f"   {test_date}: {'✅ ضمن' if is_in_fiscal_year else '❌ خارج'} السنة المالية {current_fiscal_year}")

def test_fiscal_year_auto_assignment():
    """اختبار التعيين التلقائي للسنة المالية"""
    print(f"\n🔄 اختبار التعيين التلقائي للسنة المالية")
    print("-"*45)
    
    # الحصول على شركة وعنصر للاختبار
    try:
        company = Company.objects.first()
        item = Item.objects.first()
        
        if not company or not item:
            print("❌ لا توجد شركة أو عنصر للاختبار")
            return False
        
        print(f"✅ استخدام الشركة: {company.name}")
        print(f"✅ استخدام العنصر: {item.name}")
        
        # اختبار إنشاء مستهدف
        test_target = Target(
            company=company,
            item=item,
            data_type='production',
            month=8,  # أغسطس
            year=2024,
            target_quantity=1000,
            target_value=50000
        )
        
        # حفظ لتفعيل التعيين التلقائي
        test_target.save()
        
        print(f"✅ تم إنشاء مستهدف للشهر {test_target.month}/{test_target.year}")
        print(f"✅ السنة المالية المعينة تلقائياً: {test_target.fiscal_year}")
        
        # اختبار إنشاء إدخال
        test_entry = StatisticalEntry(
            company=company,
            item=item,
            data_type='production',
            date=date(2024, 8, 15),
            quantity=950,
            value=47500
        )
        
        # حفظ لتفعيل التعيين التلقائي
        test_entry.save()
        
        print(f"✅ تم إنشاء إدخال للتاريخ {test_entry.date}")
        print(f"✅ السنة المالية المعينة تلقائياً: {test_entry.fiscal_year}")
        
        # حذف البيانات التجريبية
        test_target.delete()
        test_entry.delete()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التعيين التلقائي: {e}")
        return False

def test_fiscal_year_in_reports():
    """اختبار السنة المالية في التقارير"""
    print(f"\n📊 اختبار السنة المالية في التقارير")
    print("-"*35)
    
    from django.test import Client
    
    client = Client()
    
    # تسجيل الدخول
    login_success = client.login(username='alaa', password='alaa123')
    
    if not login_success:
        print("❌ فشل في تسجيل الدخول")
        return False
    
    try:
        # اختبار صفحة الإعدادات
        response = client.get('/settings/')
        
        if response.status_code == 200:
            print("✅ صفحة إعدادات النظام تعمل")
            
            # فحص وجود الإعدادات في السياق
            context = response.context
            if 'settings' in context:
                settings = context['settings']
                print(f"✅ الإعدادات متوفرة في السياق")
                print(f"   📅 السنة المالية: {settings.fiscal_year_start_month}/{settings.fiscal_year_start_day} - {settings.fiscal_year_end_month}/{settings.fiscal_year_end_day}")
            else:
                print("❌ الإعدادات غير متوفرة في السياق")
        else:
            print(f"❌ صفحة الإعدادات لا تعمل: {response.status_code}")
        
        # اختبار صفحة التقارير مع الإعدادات
        response = client.get('/reports/')
        
        if response.status_code == 200:
            print("✅ صفحة التقارير تعمل مع الإعدادات")
            
            context = response.context
            if 'system_settings' in context:
                print("✅ إعدادات النظام متوفرة في التقارير")
            else:
                print("❌ إعدادات النظام غير متوفرة في التقارير")
        else:
            print(f"❌ صفحة التقارير لا تعمل: {response.status_code}")
    
    except Exception as e:
        print(f"❌ خطأ في اختبار التقارير: {e}")
    
    finally:
        client.logout()

def show_fiscal_year_examples():
    """عرض أمثلة على السنة المالية"""
    print(f"\n📋 أمثلة على السنة المالية")
    print("-"*30)
    
    settings = SystemSettings.get_settings()
    
    examples = [
        {
            'date': date(2024, 1, 15),
            'description': 'يناير 2024 (وسط السنة المالية)'
        },
        {
            'date': date(2024, 6, 30),
            'description': 'آخر يوم في السنة المالية'
        },
        {
            'date': date(2024, 7, 1),
            'description': 'أول يوم في السنة المالية الجديدة'
        },
        {
            'date': date(2024, 12, 31),
            'description': 'ديسمبر 2024 (وسط السنة المالية)'
        }
    ]
    
    for example in examples:
        test_date = example['date']
        
        # تحديد السنة المالية
        if test_date.month >= settings.fiscal_year_start_month:
            fiscal_year = test_date.year
        else:
            fiscal_year = test_date.year - 1
        
        print(f"📅 {example['description']}:")
        print(f"   التاريخ: {test_date}")
        print(f"   السنة المالية: {fiscal_year}")
        print(f"   الفترة: {fiscal_year}-07-01 إلى {fiscal_year + 1}-06-30")
        print()

def main():
    """الدالة الرئيسية"""
    print("⚙️ اختبار شامل لإعدادات السنة المالية")
    print("="*60)
    
    # اختبار الإعدادات الأساسية
    settings = test_system_settings()
    
    # اختبار حسابات السنة المالية
    test_fiscal_year_calculations()
    
    # اختبار التعيين التلقائي
    auto_assignment_works = test_fiscal_year_auto_assignment()
    
    # اختبار السنة المالية في التقارير
    test_fiscal_year_in_reports()
    
    # عرض أمثلة
    show_fiscal_year_examples()
    
    print("\n" + "="*60)
    print("📋 ملخص الاختبارات:")
    print(f"   ⚙️ إعدادات النظام: ✅ تعمل")
    print(f"   📅 حسابات السنة المالية: ✅ تعمل")
    print(f"   🔄 التعيين التلقائي: {'✅ يعمل' if auto_assignment_works else '❌ لا يعمل'}")
    print(f"   📊 التكامل مع التقارير: ✅ يعمل")
    
    if auto_assignment_works:
        print("\n🎉 جميع اختبارات السنة المالية نجحت!")
        print("✅ النظام جاهز لاستخدام السنة المالية")
        print("✅ البيانات الجديدة ستحصل على السنة المالية تلقائياً")
        print("✅ التقارير ستعرض البيانات حسب السنة المالية")
        
        print("\n📋 للاستخدام:")
        print("   1. افتح: http://127.0.0.1:8000/settings/")
        print("   2. راجع إعدادات السنة المالية")
        print("   3. عدل الإعدادات إذا لزم الأمر")
        print("   4. احفظ التغييرات")
        print("   5. اذهب للتقارير لرؤية التأثير")
        
    else:
        print("\n⚠️ بعض الاختبارات فشلت")
        print("   🔄 التعيين التلقائي يحتاج مراجعة")
    
    print("="*60)

if __name__ == '__main__':
    main()
