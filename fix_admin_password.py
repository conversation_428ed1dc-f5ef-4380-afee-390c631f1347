#!/usr/bin/env python
"""
سكريبت لإصلاح كلمة مرور المدير العام
"""

import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'statistical_system.settings')
django.setup()

from django.contrib.auth.models import User

def fix_admin_password():
    """إصلاح كلمة مرور المدير العام"""
    print("إصلاح كلمة مرور المدير العام...")
    
    try:
        admin_user = User.objects.get(username='admin')
        admin_user.set_password('admin')
        admin_user.save()
        print("✅ تم تحديث كلمة مرور المدير العام")
        print("   اسم المستخدم: admin")
        print("   كلمة المرور: admin")
        
        # اختبار تسجيل الدخول
        from django.contrib.auth import authenticate
        test_user = authenticate(username='admin', password='admin')
        if test_user:
            print("✅ تم اختبار تسجيل الدخول بنجاح")
        else:
            print("❌ فشل في اختبار تسجيل الدخول")
            
    except User.DoesNotExist:
        print("❌ المدير العام غير موجود")

if __name__ == '__main__':
    fix_admin_password()
