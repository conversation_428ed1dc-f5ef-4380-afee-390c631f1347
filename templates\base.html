<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام الإحصائيات الشهرية{% endblock %}</title>
    
    <!-- Bootstrap CSS RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .navbar-brand {
            font-weight: bold;
        }
        .sidebar {
            min-height: 100vh;
            background-color: #343a40;
        }
        .sidebar .nav-link {
            color: #adb5bd;
            padding: 0.75rem 1rem;
        }
        .sidebar .nav-link:hover {
            color: #fff;
            background-color: #495057;
        }
        .sidebar .nav-link.active {
            color: #fff;
            background-color: #007bff;
        }
        .main-content {
            margin-right: 250px;
            padding: 20px;
        }
        .card {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border: 1px solid rgba(0, 0, 0, 0.125);
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .stats-card .card-body {
            padding: 1.5rem;
        }
        .stats-number {
            font-size: 2rem;
            font-weight: bold;
        }
        @media (max-width: 768px) {
            .main-content {
                margin-right: 0;
            }
            .sidebar {
                display: none;
            }
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{% url 'stats_app:dashboard' %}">
                <i class="fas fa-chart-bar me-2"></i>
                نظام الإحصائيات الشهرية
            </a>
            
            <div class="navbar-nav ms-auto">
                {% if user.is_authenticated %}
                    <div class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>
                            {{ user.get_full_name|default:user.username }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/admin/">لوحة الإدارة</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'admin:logout' %}">تسجيل الخروج</a></li>
                        </ul>
                    </div>
                {% else %}
                    <a class="nav-link" href="{% url 'admin:login' %}">تسجيل الدخول</a>
                {% endif %}
            </div>
        </div>
    </nav>

    <div class="d-flex">
        <!-- Sidebar -->
        {% if user.is_authenticated %}
        <nav class="sidebar position-fixed">
            <div class="p-3">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'stats_app:dashboard' %}">
                            <i class="fas fa-tachometer-alt me-2"></i>
                            لوحة التحكم
                        </a>
                    </li>

                    <li class="nav-item">
                        <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                            إدارة البيانات
                        </h6>
                    </li>
                    
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'stats_app:company_list' %}">
                            <i class="fas fa-building me-2"></i>
                            الشركات
                        </a>
                    </li>
                    
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'stats_app:item_list' %}">
                            <i class="fas fa-boxes me-2"></i>
                            العناصر
                        </a>
                    </li>
                    
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'stats_app:target_list' %}">
                            <i class="fas fa-bullseye me-2"></i>
                            المستهدفات
                        </a>
                    </li>
                    
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'stats_app:entry_list' %}">
                            <i class="fas fa-edit me-2"></i>
                            الإدخالات الإحصائية
                        </a>
                    </li>
                    
                    <li class="nav-item">
                        <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                            التقارير والتحليل
                        </h6>
                    </li>
                    
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'stats_app:simple_reports' %}">
                            <i class="fas fa-chart-bar me-2"></i>
                            التقارير البسيطة
                        </a>
                    </li>

                    {% if user.is_superuser %}
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'stats_app:system_settings' %}">
                            <i class="fas fa-cog me-2"></i>
                            إعدادات النظام
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </nav>
        {% endif %}

        <!-- Main Content -->
        <main class="main-content flex-grow-1">
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}

            {% block content %}{% endblock %}
        </main>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
