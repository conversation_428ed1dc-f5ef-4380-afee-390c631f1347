#!/usr/bin/env python
"""
سكريبت لاختبار إضافة المستخدمين والتأكد من عمل النظام بشكل صحيح
"""

import os
import sys
import django
from datetime import datetime

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'statistical_system.settings')
django.setup()

from django.contrib.auth.models import User
from stats_app.models import UserProfile, Company
from django.db import transaction

def test_user_creation():
    """اختبار إنشاء مستخدم جديد"""
    print("=== اختبار إنشاء مستخدم جديد ===\n")
    
    # إنشاء مستخدم تجريبي
    timestamp = int(datetime.now().timestamp())
    test_username = f"test_user_{timestamp}"
    test_email = f"{test_username}@example.com"
    
    try:
        print(f"إنشاء مستخدم جديد: {test_username}")
        
        # إنشاء المستخدم
        test_user = User.objects.create_user(
            username=test_username,
            email=test_email,
            password="testpass123",
            first_name="مستخدم",
            last_name="تجريبي"
        )
        
        print(f"✅ تم إنشاء المستخدم {test_username} بنجاح")
        
        # التحقق من إنشاء UserProfile
        try:
            profile = test_user.userprofile
            print(f"✅ تم إنشاء ملف شخصي للمستخدم {test_username}")
            print(f"   - مدير النظام: {profile.is_system_admin}")
            print(f"   - مستخدم شركة: {profile.is_company_user}")
            print(f"   - الشركة: {profile.company or 'غير محدد'}")
            
            # تحديث الملف الشخصي
            if Company.objects.exists():
                first_company = Company.objects.first()
                profile.company = first_company
                profile.is_company_user = True
                profile.save()
                print(f"✅ تم تحديث الملف الشخصي - الشركة: {first_company.name}")
            
            return test_user, True
            
        except UserProfile.DoesNotExist:
            print(f"❌ لم يتم إنشاء ملف شخصي للمستخدم {test_username}")
            return test_user, False
            
    except Exception as e:
        print(f"❌ خطأ في إنشاء المستخدم: {e}")
        return None, False

def test_multiple_users():
    """اختبار إنشاء عدة مستخدمين"""
    print("\n=== اختبار إنشاء عدة مستخدمين ===\n")
    
    users_created = []
    success_count = 0
    
    for i in range(3):
        timestamp = int(datetime.now().timestamp()) + i
        username = f"bulk_user_{timestamp}"
        
        try:
            user = User.objects.create_user(
                username=username,
                email=f"{username}@example.com",
                password="testpass123"
            )
            
            # التحقق من UserProfile
            if hasattr(user, 'userprofile'):
                print(f"✅ المستخدم {username}: تم إنشاؤه مع ملف شخصي")
                success_count += 1
            else:
                print(f"⚠️ المستخدم {username}: تم إنشاؤه بدون ملف شخصي")
            
            users_created.append(user)
            
        except Exception as e:
            print(f"❌ فشل في إنشاء المستخدم {username}: {e}")
    
    print(f"\nنتائج الاختبار: {success_count}/{len(users_created)} مستخدم تم إنشاؤهم بنجاح")
    
    return users_created

def cleanup_test_users():
    """تنظيف المستخدمين التجريبيين"""
    print("\n=== تنظيف المستخدمين التجريبيين ===\n")
    
    test_users = User.objects.filter(username__startswith='test_user_')
    bulk_users = User.objects.filter(username__startswith='bulk_user_')
    
    total_deleted = 0
    
    for user in test_users:
        username = user.username
        user.delete()
        print(f"✓ تم حذف المستخدم التجريبي: {username}")
        total_deleted += 1
    
    for user in bulk_users:
        username = user.username
        user.delete()
        print(f"✓ تم حذف المستخدم التجريبي: {username}")
        total_deleted += 1
    
    print(f"\n✅ تم حذف {total_deleted} مستخدم تجريبي")

def verify_system_integrity():
    """التحقق من سلامة النظام"""
    print("\n=== التحقق من سلامة النظام ===\n")
    
    # إحصائيات عامة
    total_users = User.objects.count()
    total_profiles = UserProfile.objects.count()
    
    print(f"إجمالي المستخدمين: {total_users}")
    print(f"إجمالي الملفات الشخصية: {total_profiles}")
    
    # التحقق من التطابق
    if total_users == total_profiles:
        print("✅ جميع المستخدمين لديهم ملفات شخصية")
    else:
        print(f"⚠️ عدم تطابق: {total_users - total_profiles} مستخدم بدون ملف شخصي")
    
    # التحقق من الملفات المكررة
    duplicate_count = 0
    for user in User.objects.all():
        profile_count = UserProfile.objects.filter(user=user).count()
        if profile_count > 1:
            duplicate_count += 1
            print(f"⚠️ المستخدم {user.username} لديه {profile_count} ملفات شخصية")
    
    if duplicate_count == 0:
        print("✅ لا توجد ملفات شخصية مكررة")
    else:
        print(f"⚠️ {duplicate_count} مستخدم لديه ملفات مكررة")
    
    return total_users == total_profiles and duplicate_count == 0

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار نظام إدارة المستخدمين\n")
    print("="*50)
    
    # التحقق من سلامة النظام قبل الاختبار
    print("التحقق الأولي من سلامة النظام...")
    initial_integrity = verify_system_integrity()
    
    if not initial_integrity:
        print("\n❌ النظام يحتوي على مشاكل. يرجى تشغيل fix_user_profiles.py أولاً")
        return
    
    # اختبار إنشاء مستخدم واحد
    test_user, success = test_user_creation()
    
    if not success:
        print("\n❌ فشل اختبار إنشاء المستخدم الواحد")
        return
    
    # اختبار إنشاء عدة مستخدمين
    bulk_users = test_multiple_users()
    
    # التحقق النهائي
    final_integrity = verify_system_integrity()
    
    # تنظيف المستخدمين التجريبيين
    cleanup_test_users()
    
    # النتيجة النهائية
    print("\n" + "="*50)
    if success and final_integrity:
        print("🎉 جميع الاختبارات نجحت! النظام يعمل بشكل صحيح")
        print("✅ يمكنك الآن إضافة مستخدمين جدد بأمان من لوحة الإدارة")
    else:
        print("❌ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه")
    
    print("="*50)

if __name__ == '__main__':
    main()
