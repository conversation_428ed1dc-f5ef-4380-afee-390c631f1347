#!/usr/bin/env python
"""
اختبار نهائي للوحة مؤشرات الأداء
"""

import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'statistical_system.settings')
django.setup()

from django.test import Client
from django.contrib.auth.models import User
import time

def test_dashboard_functionality():
    """اختبار شامل للوحة المؤشرات"""
    print("🎯 اختبار شامل للوحة مؤشرات الأداء")
    print("="*45)
    
    client = Client()
    
    # تسجيل الدخول
    login_success = client.login(username='alaa1', password='alaa1123')
    
    if not login_success:
        print("❌ فشل في تسجيل الدخول")
        return False
    
    try:
        # اختبار الوصول للوحة المؤشرات
        print("📊 اختبار الوصول للوحة المؤشرات...")
        start_time = time.time()
        response = client.get('/dashboard/')
        end_time = time.time()
        
        response_time = end_time - start_time
        
        if response.status_code == 200:
            print(f"✅ لوحة المؤشرات تعمل (وقت الاستجابة: {response_time:.2f}s)")
            
            # فحص محتوى الصفحة
            content = response.content.decode()
            
            # فحص العناصر المهمة
            required_elements = [
                'لوحة مؤشرات الأداء',
                'الشركات',
                'العناصر', 
                'المستهدفات',
                'الإدخالات',
                'الأداء الإجمالي',
                'أداء أنواع البيانات',
                'إحصائيات العناصر',
                'إجراءات سريعة'
            ]
            
            all_elements_present = True
            for element in required_elements:
                if element in content:
                    print(f"   ✅ {element}: موجود")
                else:
                    print(f"   ❌ {element}: غير موجود")
                    all_elements_present = False
            
            # فحص CSS والتصميم
            css_elements = [
                'metric-card',
                'dashboard-card',
                'progress-ring',
                'performance-badge',
                'growth-indicator'
            ]
            
            css_ok = True
            for css_class in css_elements:
                if css_class in content:
                    print(f"   ✅ CSS {css_class}: موجود")
                else:
                    print(f"   ⚠️ CSS {css_class}: غير موجود")
                    css_ok = False
            
            return all_elements_present and css_ok
            
        else:
            print(f"❌ لوحة المؤشرات فشلت: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار لوحة المؤشرات: {e}")
        return False
    
    finally:
        client.logout()

def test_dashboard_responsiveness():
    """اختبار استجابة لوحة المؤشرات"""
    print("\n📱 اختبار الاستجابة")
    print("-"*20)
    
    client = Client()
    
    # تسجيل الدخول
    login_success = client.login(username='alaa1', password='alaa1123')
    
    if not login_success:
        print("❌ فشل في تسجيل الدخول")
        return False
    
    try:
        # اختبار مع user agents مختلفة
        user_agents = [
            ('Desktop', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'),
            ('Mobile', 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)'),
            ('Tablet', 'Mozilla/5.0 (iPad; CPU OS 14_0 like Mac OS X)')
        ]
        
        all_responsive = True
        
        for device_type, ua in user_agents:
            response = client.get('/dashboard/', HTTP_USER_AGENT=ua)
            
            if response.status_code == 200:
                print(f"   ✅ {device_type}: يعمل بشكل صحيح")
            else:
                print(f"   ❌ {device_type}: فشل ({response.status_code})")
                all_responsive = False
        
        return all_responsive
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الاستجابة: {e}")
        return False
    
    finally:
        client.logout()

def test_dashboard_data_integrity():
    """اختبار سلامة البيانات"""
    print("\n📊 اختبار سلامة البيانات")
    print("-"*25)
    
    try:
        from stats_app.models import Company, Item, Target, StatisticalEntry, SystemSettings
        
        # فحص البيانات الأساسية
        companies_count = Company.objects.count()
        items_count = Item.objects.filter(is_active=True).count()
        
        print(f"   📈 الشركات: {companies_count}")
        print(f"   📦 العناصر: {items_count}")
        
        # فحص السنة المالية
        settings = SystemSettings.get_settings()
        current_fiscal_year = settings.get_current_fiscal_year()
        
        print(f"   📅 السنة المالية الحالية: {current_fiscal_year}")
        
        # فحص المستهدفات والإدخالات
        targets_count = Target.objects.filter(fiscal_year=current_fiscal_year).count()
        entries_count = StatisticalEntry.objects.filter(fiscal_year=current_fiscal_year).count()
        
        print(f"   🎯 المستهدفات للسنة الحالية: {targets_count}")
        print(f"   📝 الإدخالات للسنة الحالية: {entries_count}")
        
        # فحص أنواع البيانات
        data_types = Target.DATA_TYPES
        print(f"   📊 أنواع البيانات المتاحة: {len(data_types)}")
        
        for data_type, name in data_types:
            type_targets = Target.objects.filter(
                fiscal_year=current_fiscal_year, 
                data_type=data_type
            ).count()
            type_entries = StatisticalEntry.objects.filter(
                fiscal_year=current_fiscal_year, 
                data_type=data_type
            ).count()
            
            print(f"      • {name}: {type_entries} إدخال من {type_targets} مستهدف")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص سلامة البيانات: {e}")
        return False

def test_dashboard_performance():
    """اختبار أداء لوحة المؤشرات"""
    print("\n⚡ اختبار الأداء")
    print("-"*15)
    
    client = Client()
    
    # تسجيل الدخول
    login_success = client.login(username='alaa1', password='alaa1123')
    
    if not login_success:
        print("❌ فشل في تسجيل الدخول")
        return False
    
    try:
        # اختبار متعدد للأداء
        response_times = []
        
        for i in range(5):
            start_time = time.time()
            response = client.get('/dashboard/')
            end_time = time.time()
            
            if response.status_code == 200:
                response_time = end_time - start_time
                response_times.append(response_time)
            else:
                print(f"❌ فشل في الطلب {i+1}")
                return False
        
        # حساب الإحصائيات
        avg_time = sum(response_times) / len(response_times)
        min_time = min(response_times)
        max_time = max(response_times)
        
        print(f"   ⏱️ متوسط وقت الاستجابة: {avg_time:.3f}s")
        print(f"   🚀 أسرع استجابة: {min_time:.3f}s")
        print(f"   🐌 أبطأ استجابة: {max_time:.3f}s")
        
        # تقييم الأداء
        if avg_time < 0.5:
            print("   ✅ الأداء ممتاز")
            return True
        elif avg_time < 1.0:
            print("   ✅ الأداء جيد")
            return True
        elif avg_time < 2.0:
            print("   ⚠️ الأداء مقبول")
            return True
        else:
            print("   ❌ الأداء بطيء")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الأداء: {e}")
        return False
    
    finally:
        client.logout()

def main():
    """الدالة الرئيسية"""
    print("🔍 اختبار نهائي شامل للوحة مؤشرات الأداء")
    print("="*50)
    
    # اختبار الوظائف
    functionality_ok = test_dashboard_functionality()
    
    # اختبار الاستجابة
    responsiveness_ok = test_dashboard_responsiveness()
    
    # اختبار سلامة البيانات
    data_integrity_ok = test_dashboard_data_integrity()
    
    # اختبار الأداء
    performance_ok = test_dashboard_performance()
    
    print("\n" + "="*50)
    print("📋 ملخص الاختبارات النهائية:")
    
    tests = [
        ("🎯 الوظائف الأساسية", functionality_ok),
        ("📱 الاستجابة", responsiveness_ok),
        ("📊 سلامة البيانات", data_integrity_ok),
        ("⚡ الأداء", performance_ok)
    ]
    
    all_working = True
    for name, result in tests:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"   {name}: {status}")
        if not result:
            all_working = False
    
    if all_working:
        print("\n🎉 جميع اختبارات لوحة المؤشرات نجحت!")
        print("✅ لوحة مؤشرات الأداء تعمل بشكل مثالي")
        
        print("\n🌐 للوصول:")
        print("   📊 لوحة المؤشرات: http://127.0.0.1:8000/dashboard/")
        
        print("\n💡 المميزات المتاحة:")
        print("   🎯 عدادات ديناميكية للعناصر والشركات")
        print("   📈 مؤشرات أداء رئيسية تفاعلية")
        print("   🏆 تحليل أفضل وأسوأ أداء")
        print("   📊 إحصائيات مفصلة لكل عنصر")
        print("   ⚡ إجراءات سريعة للمهام الشائعة")
        print("   🎨 واجهة عصرية ومتجاوبة")
        print("   📱 متوافق مع جميع الأجهزة")
        print("   🚀 أداء سريع ومحسن")
        
        print("\n🎯 الخلاصة:")
        print("تم إنشاء لوحة مؤشرات أداء متقدمة وشاملة")
        print("تعرض عدادات تفاعلية للعناصر ومؤشرات أداء")
        print("رئيسية مع تصميم عصري وأداء ممتاز!")
        
    else:
        print("\n⚠️ بعض الاختبارات فشلت")
        print("🔧 راجع التفاصيل أعلاه")
    
    print("="*50)

if __name__ == '__main__':
    main()
