# Generated by Django 5.0.1 on 2025-06-07 13:00

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Company',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم الشركة')),
                ('sector', models.CharField(blank=True, max_length=100, null=True, verbose_name='القطاع')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'شركة',
                'verbose_name_plural': 'الشركات',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Item',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم العنصر')),
                ('unit', models.CharField(max_length=50, verbose_name='وحدة القياس')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'عنصر',
                'verbose_name_plural': 'العناصر',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='UserProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='رقم الهاتف')),
                ('is_company_user', models.BooleanField(default=False, verbose_name='مستخدم شركة')),
                ('is_system_admin', models.BooleanField(default=False, verbose_name='مدير النظام')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('company', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='stats_app.company', verbose_name='الشركة المرتبطة')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'ملف المستخدم',
                'verbose_name_plural': 'ملفات المستخدمين',
            },
        ),
        migrations.CreateModel(
            name='StatisticalEntry',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(verbose_name='تاريخ الإدخال')),
                ('quantity', models.DecimalField(decimal_places=2, max_digits=15, validators=[django.core.validators.MinValueValidator(0)], verbose_name='الكمية المحققة')),
                ('value', models.DecimalField(decimal_places=2, max_digits=15, validators=[django.core.validators.MinValueValidator(0)], verbose_name='القيمة المحققة')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإدخال')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='stats_app.company', verbose_name='الشركة')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='أدخل بواسطة')),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='stats_app.item', verbose_name='العنصر')),
            ],
            options={
                'verbose_name': 'إدخال إحصائي',
                'verbose_name_plural': 'الإدخالات الإحصائية',
                'ordering': ['-date', 'company__name', 'item__name'],
                'unique_together': {('company', 'item', 'date')},
            },
        ),
        migrations.CreateModel(
            name='Target',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('month', models.IntegerField(choices=[(1, 'يناير'), (2, 'فبراير'), (3, 'مارس'), (4, 'أبريل'), (5, 'مايو'), (6, 'يونيو'), (7, 'يوليو'), (8, 'أغسطس'), (9, 'سبتمبر'), (10, 'أكتوبر'), (11, 'نوفمبر'), (12, 'ديسمبر')], verbose_name='الشهر')),
                ('year', models.IntegerField(verbose_name='السنة')),
                ('target_quantity', models.DecimalField(decimal_places=2, max_digits=15, validators=[django.core.validators.MinValueValidator(0)], verbose_name='الكمية المستهدفة')),
                ('target_value', models.DecimalField(decimal_places=2, max_digits=15, validators=[django.core.validators.MinValueValidator(0)], verbose_name='القيمة المستهدفة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='stats_app.company', verbose_name='الشركة')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='stats_app.item', verbose_name='العنصر')),
            ],
            options={
                'verbose_name': 'مستهدف',
                'verbose_name_plural': 'المستهدفات',
                'ordering': ['-year', '-month', 'company__name', 'item__name'],
                'unique_together': {('company', 'item', 'month', 'year')},
            },
        ),
    ]
