# نظام الإحصائيات الشهرية

نظام شامل لإدارة وتتبع الإحصائيات الشهرية للشركات مع مقارنة الأداء المحقق بالمستهدفات المحددة مسبقاً.

## 🎯 الهدف من النظام

تسجيل بيانات إحصائية شهرية من عدة شركات لمجموعة من العناصر، ومقارنتها بالمستهدفات المحددة مسبقاً (لكل شركة ولكل عنصر شهرياً)، مع إمكانية عرض تقارير المقارنة وتحليل الأداء.

## 🏗️ هيكل النظام

### النماذج الأساسية (Models)

1. **Company (الشركة)**
   - اسم الشركة
   - القطاع
   - حالة التفعيل

2. **Item (العنصر)**
   - اسم العنصر
   - وحدة القياس
   - الوصف

3. **Target (المستهدفات الشهرية)**
   - الشركة والعنصر
   - **نوع البيانات** (طاقة إنتاجية، إنتاج، مخزون، بيع)
   - الشهر والسنة
   - الكمية المستهدفة
   - القيمة المستهدفة

4. **StatisticalEntry (الإدخال الإحصائي)**
   - الشركة والعنصر
   - **نوع البيانات** (طاقة إنتاجية، إنتاج، مخزون، بيع)
   - تاريخ الإدخال
   - الكمية والقيمة المحققة
   - المستخدم الذي أدخل البيانات

5. **UserProfile (ملف المستخدم)**
   - ربط المستخدم بشركة معينة
   - تحديد صلاحيات النظام

## ✨ المميزات الرئيسية

### 🔐 نظام الصلاحيات
- **مدير النظام**: إدارة كاملة للنظام
- **مستخدم الشركة**: إدخال البيانات لشركته فقط

### 📊 إدارة البيانات المتقدمة
- إدارة الشركات والعناصر
- **تقسيم البيانات إلى 4 أنواع**:
  - 🏭 **الطاقة الإنتاجية المتاحة**: القدرة الإنتاجية القصوى
  - ⚙️ **الإنتاج**: الإنتاج الفعلي المحقق
  - 📦 **المخزون**: المخزون المتاح
  - 🛒 **البيع**: المبيعات المحققة
- تحديد المستهدفات الشهرية لكل نوع بيانات
- إدخال البيانات الإحصائية مصنفة حسب النوع
- منع الإدخالات المكررة

### 📈 التقارير والتحليل المتقدم
- مقارنة الأداء المحقق بالمستهدف
- حساب نسب الإنجاز تلقائياً
- **رسوم بيانية تفاعلية متقدمة**:
  - 📊 **التحليل الشهري التفصيلي**: مقارنة أنواع البيانات شهرياً
  - 📈 **تحليل نسب الإنجاز**: نسب الإنجاز الشهرية مع مؤشرات الأداء
  - 🏭 **تحليل استغلال الطاقة الإنتاجية**: نسبة استغلال الطاقة مع ألوان تحذيرية
  - 📉 **تحليل الاتجاهات**: اتجاهات آخر 12 شهر للكمية والقيمة
  - 🏢 **أداء الشركات**: مقارنة الشركات حسب نوع البيانات
  - 🔄 **مقارنة أنواع البيانات**: مقارنة شاملة للمستهدف مع المحقق
- تصفية متقدمة للبيانات مع خيارات تفاعلية
- تصدير التقارير والرسوم البيانية
- **إحصائيات تفصيلية** مع حسابات الإجمالي والمتوسط والحدود

### 🎨 واجهة المستخدم المتطورة
- تصميم عصري ومتجاوب
- دعم اللغة العربية (RTL)
- **لوحة تحكم رئيسية** مع إحصائيات عامة
- **لوحة تحكم أنواع البيانات** مع تفاصيل مصنفة
- رسوم بيانية تفاعلية ملونة حسب نوع البيانات
- **نظام ألوان مميز**:
  - 🔵 أزرق للطاقة الإنتاجية
  - 🟢 أخضر للإنتاج
  - 🟡 أصفر للمخزون
  - 🔴 أحمر للمبيعات

## 🛠️ التقنيات المستخدمة

- **Backend**: Django 5.0.1
- **Frontend**: Bootstrap 5 RTL
- **Database**: SQLite (قابل للتغيير)
- **Charts**: Chart.js
- **Forms**: Django Crispy Forms
- **Permissions**: Django Guardian
- **Icons**: Font Awesome

## 🚀 التثبيت والتشغيل

### المتطلبات
- Python 3.8+
- pip

### خطوات التثبيت

1. **إنشاء البيئة الافتراضية**
```bash
python -m venv venv
```

2. **تفعيل البيئة الافتراضية**
```bash
# Windows
.\venv\Scripts\activate

# Linux/Mac
source venv/bin/activate
```

3. **تثبيت المتطلبات**
```bash
pip install -r requirements.txt
```

4. **تطبيق الهجرات**
```bash
python manage.py migrate
```

5. **إنشاء مستخدم مدير**
```bash
python manage.py createsuperuser
```

6. **إنشاء بيانات تجريبية (اختياري)**
```bash
python create_sample_data.py
```

7. **تشغيل الخادم**
```bash
python manage.py runserver
```

8. **فتح النظام في المتصفح**
```
http://127.0.0.1:8000
```

## 👤 بيانات الدخول الافتراضية

- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

## 📱 الواجهات الرئيسية

### 🏠 لوحة التحكم الرئيسية
- إحصائيات سريعة عامة
- رسوم بيانية للأداء الشهري
- أحدث الإدخالات
- روابط سريعة

### 📊 لوحة أنواع البيانات
- **إحصائيات مفصلة** لكل نوع بيانات
- **رسم بياني مقارن** للمستهدفات والإدخالات
- **جدول تفصيلي** لكل شركة حسب نوع البيانات
- **روابط سريعة** لإدخال كل نوع بيانات

### 🏢 إدارة الشركات
- قائمة الشركات
- إضافة/تعديل/حذف الشركات
- تصفية وبحث

### 📦 إدارة العناصر
- قائمة العناصر
- إدارة وحدات القياس
- تصفية وبحث

### 🎯 إدارة المستهدفات
- تحديد المستهدفات الشهرية
- مراجعة وتعديل المستهدفات
- تصفية حسب الفترة

### 📝 الإدخالات الإحصائية المتقدمة
- إدخال البيانات الشهرية **مصنفة حسب النوع**
- عرض نسب الإنجاز لكل نوع بيانات
- **ألوان مميزة** لكل نوع في الجداول
- تتبع المدخلين والتواريخ
- **تصفية متقدمة** حسب نوع البيانات

### 📊 التقارير والرسوم البيانية المتقدمة
- **تقرير مقارنة الأداء** مع تصفية حسب نوع البيانات
- **رسوم بيانية أساسية** للمقارنات السريعة
- **رسوم بيانية تفصيلية متقدمة** مع 6 أنواع تحليل:
  - التحليل الشهري التفصيلي
  - مقارنة أنواع البيانات
  - تحليل نسب الإنجاز
  - تحليل استغلال الطاقة الإنتاجية
  - تحليل الاتجاهات
  - أداء الشركات حسب نوع البيانات
- **تصفية تفاعلية** حسب الشركة والعنصر ونوع البيانات والسنة
- **تحميل الرسوم البيانية** كصور PNG
- **إحصائيات تفصيلية** مع حسابات متقدمة
- تصدير البيانات إلى Excel

## 🔧 التخصيص والتطوير

### إضافة شركة جديدة
1. الدخول كمدير نظام
2. الذهاب لقائمة الشركات
3. النقر على "إضافة شركة جديدة"
4. ملء البيانات المطلوبة

### إضافة مستخدم شركة
1. إنشاء مستخدم جديد من لوحة الإدارة
2. إنشاء UserProfile وربطه بالشركة
3. تحديد صلاحيات "مستخدم شركة"

### تخصيص التقارير
يمكن تخصيص التقارير عبر تعديل ملفات:
- `views.py` - منطق التقارير
- `templates/stats_app/reports.html` - واجهة التقارير

## 📊 دليل الرسوم البيانية التفصيلية المتقدمة

### 🎯 التحليلات الأساسية

#### 1. **التحليل الشهري التفصيلي**
- **الوصف**: مقارنة جميع أنواع البيانات (طاقة إنتاجية، إنتاج، مخزون، مبيعات) شهرياً
- **نوع الرسم**: خطي متعدد الخطوط
- **الألوان**: أزرق للطاقة، أخضر للإنتاج، أصفر للمخزون، أحمر للمبيعات
- **الاستخدام**: فهم التوزيع الشهري لكل نوع بيانات

#### 2. **مقارنة أنواع البيانات**
- **الوصف**: مقارنة إجمالي المستهدف مع المحقق لكل نوع بيانات
- **نوع الرسم**: أعمدة مجمعة
- **الاستخدام**: تحديد أي نوع بيانات يحتاج تحسين

#### 3. **تحليل نسب الإنجاز**
- **الوصف**: نسب الإنجاز الشهرية مع خط الاتجاه
- **نوع الرسم**: خطي مع تعبئة
- **المقياس**: نسبة مئوية (0-120%)
- **الاستخدام**: تتبع تحسن الأداء عبر الزمن

#### 4. **تحليل استغلال الطاقة الإنتاجية**
- **الوصف**: نسبة استغلال الطاقة الإنتاجية شهرياً
- **نوع الرسم**: أعمدة ملونة حسب مستوى الاستغلال
- **الألوان التحذيرية**:
  - 🟢 أخضر (75-89%): استغلال جيد
  - 🟡 أصفر (50-74%): استغلال متوسط
  - 🔴 أحمر (90%+): استغلال عالي جداً (تحذير)
  - ⚫ رمادي (<50%): استغلال ضعيف
- **الاستخدام**: تحسين كفاءة استغلال الطاقة الإنتاجية

#### 5. **تحليل الاتجاهات**
- **الوصف**: اتجاهات الكمية والقيمة لآخر 12 شهر
- **نوع الرسم**: خطي مع محورين Y
- **الاستخدام**: التنبؤ بالاتجاهات المستقبلية

#### 6. **أداء الشركات حسب نوع البيانات**
- **الوصف**: مقارنة أداء الشركات لنوع بيانات محدد
- **نوع الرسم**: دائري أو أعمدة
- **الاستخدام**: تحديد الشركات الأفضل أداءً

### 🚀 التحليلات المتقدمة الجديدة

#### 7. **مؤشرات الأداء الرئيسية (KPIs)** ⭐
- **الوصف**: لوحة شاملة لمؤشرات الأداء الرئيسية
- **المؤشرات المحسوبة**:
  - نسبة الإنجاز
  - معدل النمو الشهري
  - التباين المطلق والنسبي
  - الانحراف المعياري
  - معامل التباين
- **العرض**: بطاقات ملونة تفاعلية مع مؤشرات بصرية
- **الاستخدام**: مراقبة الأداء الشامل بنظرة سريعة

#### 8. **تحليل التباين (Variance Analysis)** ⭐
- **الوصف**: تحليل التباين المطلق والنسبي بين المستهدف والمحقق
- **نوع الرسم**: أعمدة + خط (محورين Y)
- **المقاييس**: التباين المطلق (بالوحدات) والنسبي (بالنسبة المئوية)
- **الاستخدام**: تحديد الأشهر التي تحتاج تحسين

#### 9. **مقاييس الكفاءة** ⭐
- **الوصف**: تحليل متقدم للكفاءة التشغيلية
- **المقاييس المحسوبة**:
  - كفاءة الإنتاج (الإنتاج ÷ الطاقة الإنتاجية)
  - كفاءة المبيعات (المبيعات ÷ الإنتاج)
  - معدل دوران المخزون (المبيعات ÷ المخزون)
- **نوع الرسم**: خطي متعدد مع محورين Y
- **الاستخدام**: تحسين الكفاءة التشغيلية

#### 10. **التحليل الموسمي** ⭐
- **الوصف**: مقارنة الأداء عبر 3 سنوات لتحديد الأنماط الموسمية
- **نوع الرسم**: خطي متعدد مع خط المتوسط الموسمي
- **المميزات**: خط منقط للمتوسط الموسمي
- **الاستخدام**: التخطيط الموسمي والتنبؤ

#### 11. **ترتيب الأداء** ⭐
- **الوصف**: ترتيب الشركات حسب نقاط الأداء المركبة
- **معايير التقييم**:
  - نسبة الإنجاز (40%)
  - نقاط الثبات (30%)
  - نقاط النمو (20%)
  - نقاط الكفاءة (10%)
- **العرض**: جدول ترتيب مع ميداليات + رسم بياني
- **الاستخدام**: تحفيز المنافسة وتحديد أفضل الممارسات

#### 12. **تحليل النمو** ⭐
- **الوصف**: مقارنة الأداء بين السنة الحالية والسابقة
- **نوع الرسم**: أعمدة مقارنة + خط معدل النمو
- **المقاييس**: معدل النمو الشهري بالنسبة المئوية
- **الاستخدام**: تتبع النمو وتحديد الاتجاهات

#### 13. **تقييم المخاطر** ⭐
- **الوصف**: تحليل مخاطر عدم تحقيق الأهداف
- **المؤشرات المحسوبة**:
  - التقلبات (الانحراف المعياري)
  - احتمالية عدم تحقيق الهدف
  - مؤشر المخاطر المركب
- **التصنيفات**: منخفض، متوسط، عالي، عالي جداً
- **العرض**: بطاقات ملونة حسب مستوى المخاطر
- **الاستخدام**: إدارة المخاطر والتخطيط الاستباقي

#### 14. **التنبؤ (Forecasting)** ⭐
- **الوصف**: توقعات لـ 6 أشهر قادمة باستخدام المتوسط المتحرك والاتجاه
- **الخوارزمية**: متوسط متحرك بسيط + تحليل الاتجاه الخطي
- **نوع الرسم**: خطي مع خط منقط للتنبؤ
- **الاستخدام**: التخطيط المستقبلي ووضع الميزانيات

#### 15. **المعايير المرجعية (Benchmarking)** ⭐
- **الوصف**: مقارنة الأداء مع المعايير الإحصائية
- **المعايير المحسوبة**:
  - المتوسط والوسيط
  - الربع الأول والثالث
  - الحد الأقصى والأدنى
  - الانحراف المعياري
- **التصنيفات**: ممتاز، جيد، متوسط، ضعيف
- **العرض**: رسم بياني + إحصائيات مرجعية
- **الاستخدام**: تحديد موقع الشركة نسبة للسوق

#### 16. **مصفوفة الارتباط** ⭐
- **الوصف**: تحليل الارتباط بين أنواع البيانات المختلفة
- **المقياس**: معامل ارتباط بيرسون (-1 إلى +1)
- **العرض**: جدول ملون حسب قوة الارتباط
- **التفسير**:
  - 🟢 ارتباط قوي موجب (0.7-1.0)
  - 🔵 ارتباط متوسط موجب (0.3-0.7)
  - ⚫ ارتباط ضعيف (-0.3-0.3)
  - 🟡 ارتباط متوسط سالب (-0.7--0.3)
  - 🔴 ارتباط قوي سالب (-1.0--0.7)
- **الاستخدام**: فهم العلاقات بين المتغيرات واتخاذ قرارات مترابطة

### 🎛️ المميزات التقنية المتقدمة

#### **واجهة المستخدم التفاعلية**
- **اختيار نوع التحليل**: واجهة بصرية مع 16 نوع تحليل
- **تصفية تفاعلية**: حسب الشركة والعنصر ونوع البيانات والسنة
- **تحديث فوري**: تحديث الرسوم البيانية عند تغيير التصفية
- **تحميل الرسوم**: حفظ الرسوم البيانية كصور PNG عالية الجودة
- **إحصائيات مباشرة**: حساب الإجمالي والمتوسط والحدود تلقائياً

#### **عروض متخصصة لكل تحليل**
- **مؤشرات KPI**: بطاقات ملونة تفاعلية مع مؤشرات بصرية
- **تقييم المخاطر**: بطاقات ملونة حسب مستوى المخاطر
- **ترتيب الأداء**: جدول ترتيب مع ميداليات وألوان
- **المعايير المرجعية**: رسم بياني + إحصائيات مرجعية
- **مصفوفة الارتباط**: جدول ملون مع تفسير المعاملات

#### **حسابات إحصائية متقدمة**
- **معاملات الارتباط**: حساب معامل بيرسون بدقة
- **التحليل الإحصائي**: متوسط، وسيط، انحراف معياري، معامل التباين
- **نقاط الأداء المركبة**: خوارزمية متقدمة لحساب الأداء الشامل
- **تحليل المخاطر**: مؤشرات مخاطر مركبة مع تصنيف ذكي
- **التنبؤ الذكي**: خوارزمية متوسط متحرك مع تحليل الاتجاه

#### **تصميم متجاوب ومتقدم**
- **ألوان ذكية**: نظام ألوان متدرج حسب الأداء والمخاطر
- **تأثيرات بصرية**: انتقالات سلسة وتأثيرات تفاعلية
- **تخطيط متكيف**: يتكيف مع محتوى كل نوع تحليل
- **رسوم بيانية متقدمة**: محاور متعددة، خطوط مرجعية، تدرجات لونية

### 💼 حالات الاستخدام المتقدمة

#### **للمديرين التنفيذيين**
- **مؤشرات KPI**: مراقبة الأداء الشامل بنظرة سريعة
- **ترتيب الأداء**: مقارنة أداء الشركات وتحفيز المنافسة
- **تقييم المخاطر**: تحديد المخاطر المحتملة واتخاذ إجراءات وقائية
- **المعايير المرجعية**: مقارنة الأداء مع معايير السوق

#### **لمديري العمليات**
- **مقاييس الكفاءة**: تحسين الكفاءة التشغيلية
- **تحليل استغلال الطاقة**: تحسين استغلال الموارد
- **تحليل التباين**: تحديد الانحرافات وأسبابها
- **مصفوفة الارتباط**: فهم العلاقات بين العمليات

#### **للمخططين الماليين**
- **التنبؤ**: التخطيط المالي ووضع الميزانيات
- **تحليل النمو**: تتبع النمو ووضع أهداف واقعية
- **التحليل الموسمي**: التخطيط الموسمي وإدارة التدفق النقدي
- **تحليل الاتجاهات**: التنبؤ بالاتجاهات المستقبلية

#### **لمحللي البيانات**
- **تحليل إحصائي متقدم**: معاملات ارتباط ومؤشرات إحصائية
- **تحليل التباين**: تحليل عميق للانحرافات
- **نمذجة البيانات**: فهم العلاقات المعقدة بين المتغيرات
- **تقييم جودة البيانات**: تحديد الأنماط والشذوذ

### 🎯 فوائد النظام المتقدم

#### **تحسين اتخاذ القرارات**
- قرارات مبنية على بيانات دقيقة ومحللة
- رؤى عميقة من خلال 16 نوع تحليل مختلف
- مؤشرات تحذيرية مبكرة للمخاطر
- توقعات مستقبلية لتحسين التخطيط

#### **زيادة الكفاءة التشغيلية**
- تحديد نقاط الضعف والقوة بدقة
- تحسين استغلال الموارد والطاقات
- مراقبة الأداء في الوقت الفعلي
- مقارنات معيارية لتحديد أفضل الممارسات

#### **إدارة المخاطر الاستباقية**
- تحديد المخاطر قبل حدوثها
- تصنيف المخاطر حسب الأولوية
- مؤشرات تحذيرية بصرية
- خطط طوارئ مبنية على التحليل

#### **تحفيز الأداء والمنافسة**
- ترتيب شفاف للأداء
- مقارنات عادلة بين الشركات
- تحفيز التحسين المستمر
- تقدير الإنجازات المتميزة

## 📁 هيكل المشروع

```
statistical_system/
├── manage.py
├── requirements.txt
├── create_sample_data.py
├── statistical_system/
│   ├── settings.py
│   ├── urls.py
│   └── wsgi.py
├── stats_app/
│   ├── models.py
│   ├── views.py
│   ├── forms.py
│   ├── admin.py
│   ├── urls.py
│   └── signals.py
├── templates/
│   ├── base.html
│   ├── registration/
│   └── stats_app/
└── static/
```

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:
1. Fork المشروع
2. إنشاء branch جديد للميزة
3. Commit التغييرات
4. Push للـ branch
5. إنشاء Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT.

## 📊 البيانات التجريبية المحدثة

### 🏢 الشركات المتوفرة
- **شركة الأسمنت السعودية** (قطاع الصناعة)
- **شركة البتروكيماويات** (قطاع البتروكيماويات)
- **شركة الحديد والصلب** (قطاع الصناعة)
- **شركة الأغذية المتحدة** (قطاع الأغذية)
- **شركة النسيج الوطنية** (قطاع النسيج)

### 📦 العناصر المتوفرة
- الأسمنت (طن)
- البلاستيك (كجم)
- الحديد (طن)
- المواد الغذائية (كجم)
- الأقمشة (متر)
- الكيماويات (لتر)

### 📈 البيانات الإحصائية المتقدمة
- **720 مستهدف** للسنة الحالية (180 لكل نوع بيانات)
- **360 إدخال إحصائي** مصنف حسب أنواع البيانات الأربعة
- **بيانات متدرجة وواقعية** مع قيم مختلفة لكل نوع:
  - 🏭 **الطاقة الإنتاجية**: أعلى القيم (150% من الأساس)
  - ⚙️ **الإنتاج**: القيم الأساسية (100% من الأساس)
  - 📦 **المخزون**: قيم منخفضة (30% من الأساس)
  - 🛒 **المبيعات**: قيم متوسطة (80% من الأساس)
- **تغطية شاملة**: 5 شركات × 3 عناصر × 4 أنواع بيانات × 12 شهر
- **بيانات تاريخية**: 3 سنوات من البيانات للتحليل الموسمي والنمو

### 🎯 نسب الإنجاز المتنوعة
- نسب إنجاز متفاوتة (70-110% من المستهدف)
- بيانات واقعية تحاكي البيئة الفعلية
- تغطية شاملة لجميع الأشهر والشركات

## 📞 الدعم

للدعم والاستفسارات، يرجى إنشاء Issue في المستودع.

---

## 📊 إحصائيات النظام النهائية

### 🎯 **إجمالي التحليلات المتاحة: 16 نوع تحليل**

#### **التحليلات الأساسية (6)**
1. التحليل الشهري التفصيلي
2. مقارنة أنواع البيانات
3. تحليل نسب الإنجاز
4. تحليل استغلال الطاقة الإنتاجية
5. تحليل الاتجاهات
6. أداء الشركات حسب نوع البيانات

#### **التحليلات المتقدمة (10)**
7. مؤشرات الأداء الرئيسية (KPIs)
8. تحليل التباين (Variance Analysis)
9. مقاييس الكفاءة
10. التحليل الموسمي
11. ترتيب الأداء
12. تحليل النمو
13. تقييم المخاطر
14. التنبؤ (Forecasting)
15. المعايير المرجعية (Benchmarking)
16. مصفوفة الارتباط

### 📈 **المؤشرات المحسوبة: 25+ مؤشر**
- نسبة الإنجاز
- معدل النمو الشهري
- التباين المطلق والنسبي
- الانحراف المعياري
- معامل التباين
- كفاءة الإنتاج والمبيعات
- معدل دوران المخزون
- نقاط الثبات والنمو والكفاءة
- مؤشر المخاطر المركب
- معاملات الارتباط
- المعايير الإحصائية (متوسط، وسيط، أرباع)
- التنبؤات المستقبلية

### 🎨 **المميزات التقنية: 50+ ميزة**
- 16 نوع رسم بياني تفاعلي
- 4 أنواع عروض متخصصة (KPI، مخاطر، ترتيب، معايير)
- تصفية تفاعلية متقدمة
- حسابات إحصائية في الوقت الفعلي
- نظام ألوان ذكي ومتدرج
- تأثيرات بصرية متقدمة
- تحميل وتصدير الرسوم
- واجهة متجاوبة بالكامل

### 🏆 **النتيجة النهائية**
نظام إحصائي متكامل ومتقدم يوفر **أعمق مستوى من التحليل والتفاصيل** مع:
- **16 نوع تحليل** شامل ومتقدم
- **25+ مؤشر أداء** محسوب تلقائياً
- **50+ ميزة تقنية** متطورة
- **واجهة تفاعلية** عالية الجودة
- **حسابات إحصائية** دقيقة ومتقدمة

---

**تم تطوير هذا النظام باستخدام Django وأحدث التقنيات في تطوير تطبيقات الويب مع رسوم بيانية تفاعلية متقدمة وتحليلات إحصائية عميقة.**
