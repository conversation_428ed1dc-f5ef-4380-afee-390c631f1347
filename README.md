# نظام الإحصائيات الشهرية

نظام شامل لإدارة وتتبع الإحصائيات الشهرية للشركات مع مقارنة الأداء المحقق بالمستهدفات المحددة مسبقاً.

## 🎯 الهدف من النظام

تسجيل بيانات إحصائية شهرية من عدة شركات لمجموعة من العناصر، ومقارنتها بالمستهدفات المحددة مسبقاً (لكل شركة ولكل عنصر شهرياً)، مع إمكانية عرض تقارير المقارنة وتحليل الأداء.

## 🏗️ هيكل النظام

### النماذج الأساسية (Models)

1. **Company (الشركة)**
   - اسم الشركة
   - القطاع
   - حالة التفعيل

2. **Item (العنصر)**
   - اسم العنصر
   - وحدة القياس
   - الوصف

3. **Target (المستهدفات الشهرية)**
   - الشركة والعنصر
   - **نوع البيانات** (طاقة إنتاجية، إنتاج، مخزون، بيع)
   - الشهر والسنة
   - الكمية المستهدفة
   - القيمة المستهدفة

4. **StatisticalEntry (الإدخال الإحصائي)**
   - الشركة والعنصر
   - **نوع البيانات** (طاقة إنتاجية، إنتاج، مخزون، بيع)
   - تاريخ الإدخال
   - الكمية والقيمة المحققة
   - المستخدم الذي أدخل البيانات

5. **UserProfile (ملف المستخدم)**
   - ربط المستخدم بشركة معينة
   - تحديد صلاحيات النظام

## ✨ المميزات الرئيسية

### 🔐 نظام الصلاحيات
- **مدير النظام**: إدارة كاملة للنظام
- **مستخدم الشركة**: إدخال البيانات لشركته فقط

### 📊 إدارة البيانات المتقدمة
- إدارة الشركات والعناصر
- **تقسيم البيانات إلى 4 أنواع**:
  - 🏭 **الطاقة الإنتاجية المتاحة**: القدرة الإنتاجية القصوى
  - ⚙️ **الإنتاج**: الإنتاج الفعلي المحقق
  - 📦 **المخزون**: المخزون المتاح
  - 🛒 **البيع**: المبيعات المحققة
- تحديد المستهدفات الشهرية لكل نوع بيانات
- إدخال البيانات الإحصائية مصنفة حسب النوع
- منع الإدخالات المكررة

### 📈 التقارير والتحليل
- مقارنة الأداء المحقق بالمستهدف
- حساب نسب الإنجاز تلقائياً
- رسوم بيانية تفاعلية
- تصفية متقدمة للبيانات
- تصدير التقارير

### 🎨 واجهة المستخدم المتطورة
- تصميم عصري ومتجاوب
- دعم اللغة العربية (RTL)
- **لوحة تحكم رئيسية** مع إحصائيات عامة
- **لوحة تحكم أنواع البيانات** مع تفاصيل مصنفة
- رسوم بيانية تفاعلية ملونة حسب نوع البيانات
- **نظام ألوان مميز**:
  - 🔵 أزرق للطاقة الإنتاجية
  - 🟢 أخضر للإنتاج
  - 🟡 أصفر للمخزون
  - 🔴 أحمر للمبيعات

## 🛠️ التقنيات المستخدمة

- **Backend**: Django 5.0.1
- **Frontend**: Bootstrap 5 RTL
- **Database**: SQLite (قابل للتغيير)
- **Charts**: Chart.js
- **Forms**: Django Crispy Forms
- **Permissions**: Django Guardian
- **Icons**: Font Awesome

## 🚀 التثبيت والتشغيل

### المتطلبات
- Python 3.8+
- pip

### خطوات التثبيت

1. **إنشاء البيئة الافتراضية**
```bash
python -m venv venv
```

2. **تفعيل البيئة الافتراضية**
```bash
# Windows
.\venv\Scripts\activate

# Linux/Mac
source venv/bin/activate
```

3. **تثبيت المتطلبات**
```bash
pip install -r requirements.txt
```

4. **تطبيق الهجرات**
```bash
python manage.py migrate
```

5. **إنشاء مستخدم مدير**
```bash
python manage.py createsuperuser
```

6. **إنشاء بيانات تجريبية (اختياري)**
```bash
python create_sample_data.py
```

7. **تشغيل الخادم**
```bash
python manage.py runserver
```

8. **فتح النظام في المتصفح**
```
http://127.0.0.1:8000
```

## 👤 بيانات الدخول الافتراضية

- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

## 📱 الواجهات الرئيسية

### 🏠 لوحة التحكم الرئيسية
- إحصائيات سريعة عامة
- رسوم بيانية للأداء الشهري
- أحدث الإدخالات
- روابط سريعة

### 📊 لوحة أنواع البيانات
- **إحصائيات مفصلة** لكل نوع بيانات
- **رسم بياني مقارن** للمستهدفات والإدخالات
- **جدول تفصيلي** لكل شركة حسب نوع البيانات
- **روابط سريعة** لإدخال كل نوع بيانات

### 🏢 إدارة الشركات
- قائمة الشركات
- إضافة/تعديل/حذف الشركات
- تصفية وبحث

### 📦 إدارة العناصر
- قائمة العناصر
- إدارة وحدات القياس
- تصفية وبحث

### 🎯 إدارة المستهدفات
- تحديد المستهدفات الشهرية
- مراجعة وتعديل المستهدفات
- تصفية حسب الفترة

### 📝 الإدخالات الإحصائية المتقدمة
- إدخال البيانات الشهرية **مصنفة حسب النوع**
- عرض نسب الإنجاز لكل نوع بيانات
- **ألوان مميزة** لكل نوع في الجداول
- تتبع المدخلين والتواريخ
- **تصفية متقدمة** حسب نوع البيانات

### 📊 التقارير
- تقرير مقارنة الأداء
- رسوم بيانية تفاعلية
- تصفية متقدمة
- تصدير البيانات

## 🔧 التخصيص والتطوير

### إضافة شركة جديدة
1. الدخول كمدير نظام
2. الذهاب لقائمة الشركات
3. النقر على "إضافة شركة جديدة"
4. ملء البيانات المطلوبة

### إضافة مستخدم شركة
1. إنشاء مستخدم جديد من لوحة الإدارة
2. إنشاء UserProfile وربطه بالشركة
3. تحديد صلاحيات "مستخدم شركة"

### تخصيص التقارير
يمكن تخصيص التقارير عبر تعديل ملفات:
- `views.py` - منطق التقارير
- `templates/stats_app/reports.html` - واجهة التقارير

## 📁 هيكل المشروع

```
statistical_system/
├── manage.py
├── requirements.txt
├── create_sample_data.py
├── statistical_system/
│   ├── settings.py
│   ├── urls.py
│   └── wsgi.py
├── stats_app/
│   ├── models.py
│   ├── views.py
│   ├── forms.py
│   ├── admin.py
│   ├── urls.py
│   └── signals.py
├── templates/
│   ├── base.html
│   ├── registration/
│   └── stats_app/
└── static/
```

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:
1. Fork المشروع
2. إنشاء branch جديد للميزة
3. Commit التغييرات
4. Push للـ branch
5. إنشاء Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT.

## 📞 الدعم

للدعم والاستفسارات، يرجى إنشاء Issue في المستودع.

---

**تم تطوير هذا النظام باستخدام Django وأفضل الممارسات في تطوير تطبيقات الويب.**
