{% extends 'base.html' %}

{% block title %}حذف الشركة - نظام الإحصائيات الشهرية{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3">
                    <i class="fas fa-trash me-2"></i>
                    حذف الشركة
                </h1>
                <a href="{% url 'stats_app:company_list' %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>
                    العودة للقائمة
                </a>
            </div>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-6">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        تأكيد الحذف
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>تحذير:</strong> هذا الإجراء لا يمكن التراجع عنه!
                    </div>
                    
                    <p class="mb-3">هل أنت متأكد من رغبتك في حذف الشركة التالية؟</p>
                    
                    <div class="card bg-light">
                        <div class="card-body">
                            <h6><strong>اسم الشركة:</strong> {{ object.name }}</h6>
                            <p class="mb-1"><strong>القطاع:</strong> {{ object.sector|default:"غير محدد" }}</p>
                            <p class="mb-0"><strong>الحالة:</strong> 
                                {% if object.is_active %}
                                    <span class="badge bg-success">نشط</span>
                                {% else %}
                                    <span class="badge bg-secondary">غير نشط</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>

                    <form method="post" class="mt-4">
                        {% csrf_token %}
                        <div class="d-flex justify-content-end gap-2">
                            <a href="{% url 'stats_app:company_list' %}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-trash me-2"></i>
                                تأكيد الحذف
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
