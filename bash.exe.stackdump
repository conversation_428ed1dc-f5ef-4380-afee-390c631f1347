Stack trace:
Frame         Function      Args
0007FFFF8E70  00021005FEBA (000210285F48, 00021026AB6E, 0007FFFF8E70, 0007FFFF7D70) msys-2.0.dll+0x1FEBA
0007FFFF8E70  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF9148) msys-2.0.dll+0x67F9
0007FFFF8E70  000210046832 (000210285FF9, 0007FFFF8D28, 0007FFFF8E70, 000000000000) msys-2.0.dll+0x6832
0007FFFF8E70  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFF8E70  0002100690B4 (0007FFFF8E80, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFF9150  00021006A49D (0007FFFF8E80, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF82A080000 ntdll.dll
7FF8290F0000 KERNEL32.DLL
7FF827590000 KERNELBASE.dll
7FF81ED10000 apphelp.dll
7FF829E40000 USER32.dll
7FF827A80000 win32u.dll
7FF829DC0000 GDI32.dll
7FF827950000 gdi32full.dll
7FF8274E0000 msvcp_win.dll
000210040000 msys-2.0.dll
7FF827AB0000 ucrtbase.dll
7FF8293C0000 advapi32.dll
7FF828660000 msvcrt.dll
7FF827EB0000 sechost.dll
7FF828870000 RPCRT4.dll
7FF826A40000 CRYPTBASE.DLL
7FF827C00000 bcryptPrimitives.dll
7FF827F60000 IMM32.DLL
