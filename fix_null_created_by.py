#!/usr/bin/env python
"""
إصلاح البيانات التي لا تحتوي على منشئ
"""

import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'statistical_system.settings')
django.setup()

from django.contrib.auth.models import User
from stats_app.models import Target, StatisticalEntry

def fix_null_created_by():
    """إصلاح البيانات التي لا تحتوي على منشئ"""
    print("🔧 إصلاح البيانات التي لا تحتوي على منشئ")
    print("="*50)
    
    # الحصول على مستخدم افتراضي
    default_user = None
    
    # البحث عن مستخدم مناسب
    for username in ['admin', 'alaa', 'alaa1', 'manager']:
        try:
            default_user = User.objects.get(username=username)
            print(f"✅ سيتم استخدام المستخدم: {username}")
            break
        except User.DoesNotExist:
            continue
    
    if not default_user:
        print("❌ لم يتم العثور على مستخدم افتراضي")
        return False
    
    # إصلاح المستهدفات
    targets_without_creator = Target.objects.filter(created_by__isnull=True)
    targets_count = targets_without_creator.count()
    
    if targets_count > 0:
        print(f"\n🎯 إصلاح {targets_count} مستهدف بدون منشئ...")
        targets_without_creator.update(created_by=default_user)
        print(f"✅ تم إصلاح جميع المستهدفات")
    else:
        print(f"\n✅ جميع المستهدفات لها منشئ")
    
    # إصلاح الإدخالات
    entries_without_creator = StatisticalEntry.objects.filter(created_by__isnull=True)
    entries_count = entries_without_creator.count()
    
    if entries_count > 0:
        print(f"\n📈 إصلاح {entries_count} إدخال بدون منشئ...")
        entries_without_creator.update(created_by=default_user)
        print(f"✅ تم إصلاح جميع الإدخالات")
    else:
        print(f"\n✅ جميع الإدخالات لها منشئ")
    
    return True

def verify_fix():
    """التحقق من الإصلاح"""
    print(f"\n🔍 التحقق من الإصلاح")
    print("-"*25)
    
    # فحص المستهدفات
    targets_without_creator = Target.objects.filter(created_by__isnull=True).count()
    print(f"🎯 مستهدفات بدون منشئ: {targets_without_creator}")
    
    # فحص الإدخالات
    entries_without_creator = StatisticalEntry.objects.filter(created_by__isnull=True).count()
    print(f"📈 إدخالات بدون منشئ: {entries_without_creator}")
    
    if targets_without_creator == 0 and entries_without_creator == 0:
        print("✅ تم إصلاح جميع البيانات بنجاح")
        return True
    else:
        print("❌ لا تزال هناك بيانات تحتاج إصلاح")
        return False

def update_fiscal_years():
    """تحديث السنوات المالية للبيانات الموجودة"""
    print(f"\n📅 تحديث السنوات المالية")
    print("-"*25)
    
    from stats_app.models import SystemSettings
    
    settings = SystemSettings.get_settings()
    
    # تحديث المستهدفات
    targets_without_fiscal_year = Target.objects.filter(fiscal_year__isnull=True)
    targets_count = targets_without_fiscal_year.count()
    
    if targets_count > 0:
        print(f"🎯 تحديث {targets_count} مستهدف...")
        
        for target in targets_without_fiscal_year:
            # تحديد السنة المالية بناءً على الشهر والسنة
            if target.month >= settings.fiscal_year_start_month:
                target.fiscal_year = target.year
            else:
                target.fiscal_year = target.year - 1
            target.save()
        
        print(f"✅ تم تحديث جميع المستهدفات")
    else:
        print(f"✅ جميع المستهدفات لها سنة مالية")
    
    # تحديث الإدخالات
    entries_without_fiscal_year = StatisticalEntry.objects.filter(fiscal_year__isnull=True)
    entries_count = entries_without_fiscal_year.count()
    
    if entries_count > 0:
        print(f"📈 تحديث {entries_count} إدخال...")
        
        for entry in entries_without_fiscal_year:
            # تحديد السنة المالية بناءً على التاريخ
            if entry.date.month >= settings.fiscal_year_start_month:
                entry.fiscal_year = entry.date.year
            else:
                entry.fiscal_year = entry.date.year - 1
            entry.save()
        
        print(f"✅ تم تحديث جميع الإدخالات")
    else:
        print(f"✅ جميع الإدخالات لها سنة مالية")

def show_final_statistics():
    """عرض الإحصائيات النهائية"""
    print(f"\n📊 الإحصائيات النهائية")
    print("-"*25)
    
    # إحصائيات المستهدفات
    total_targets = Target.objects.count()
    targets_with_creator = Target.objects.filter(created_by__isnull=False).count()
    targets_with_fiscal_year = Target.objects.filter(fiscal_year__isnull=False).count()
    
    print(f"🎯 المستهدفات:")
    print(f"   📊 المجموع: {total_targets}")
    print(f"   👤 لها منشئ: {targets_with_creator}")
    print(f"   📅 لها سنة مالية: {targets_with_fiscal_year}")
    
    # إحصائيات الإدخالات
    total_entries = StatisticalEntry.objects.count()
    entries_with_creator = StatisticalEntry.objects.filter(created_by__isnull=False).count()
    entries_with_fiscal_year = StatisticalEntry.objects.filter(fiscal_year__isnull=False).count()
    
    print(f"\n📈 الإدخالات:")
    print(f"   📊 المجموع: {total_entries}")
    print(f"   👤 لها منشئ: {entries_with_creator}")
    print(f"   📅 لها سنة مالية: {entries_with_fiscal_year}")
    
    # إحصائيات السنوات المالية
    fiscal_years_targets = Target.objects.filter(fiscal_year__isnull=False).values_list('fiscal_year', flat=True).distinct().order_by('fiscal_year')
    fiscal_years_entries = StatisticalEntry.objects.filter(fiscal_year__isnull=False).values_list('fiscal_year', flat=True).distinct().order_by('fiscal_year')
    
    all_fiscal_years = set(fiscal_years_targets) | set(fiscal_years_entries)
    
    print(f"\n📅 السنوات المالية المتوفرة:")
    for year in sorted(all_fiscal_years):
        targets_count = Target.objects.filter(fiscal_year=year).count()
        entries_count = StatisticalEntry.objects.filter(fiscal_year=year).count()
        print(f"   {year}: {targets_count} مستهدف، {entries_count} إدخال")

def main():
    """الدالة الرئيسية"""
    print("🔧 إصلاح شامل للبيانات")
    print("="*30)
    
    # إصلاح البيانات بدون منشئ
    fix_success = fix_null_created_by()
    
    if fix_success:
        # التحقق من الإصلاح
        verify_success = verify_fix()
        
        # تحديث السنوات المالية
        update_fiscal_years()
        
        # عرض الإحصائيات النهائية
        show_final_statistics()
        
        print("\n" + "="*50)
        if verify_success:
            print("🎉 تم إصلاح جميع البيانات بنجاح!")
            print("✅ جميع المستهدفات والإدخالات لها منشئ")
            print("✅ جميع البيانات لها سنة مالية")
            print("✅ النظام جاهز للاستخدام بدون أخطاء")
            
            print("\n🌐 للاختبار:")
            print("   http://127.0.0.1:8000/entries/")
            print("   http://127.0.0.1:8000/targets/")
            print("   http://127.0.0.1:8000/reports/")
        else:
            print("⚠️ لا تزال هناك بيانات تحتاج إصلاح")
        
        print("="*50)
    else:
        print("❌ فشل في إصلاح البيانات")

if __name__ == '__main__':
    main()
