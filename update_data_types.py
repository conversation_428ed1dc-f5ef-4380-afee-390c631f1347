#!/usr/bin/env python
"""
سكريبت لتحديث البيانات الموجودة وإضافة بيانات جديدة لأنواع البيانات المختلفة
"""

import os
import sys
import django
from datetime import date, datetime
from decimal import Decimal
import random

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'statistical_system.settings')
django.setup()

from stats_app.models import Company, Item, Target, StatisticalEntry
from django.contrib.auth.models import User

def update_existing_data():
    """تحديث البيانات الموجودة لتتضمن أنواع البيانات المختلفة"""
    print("تحديث البيانات الموجودة...")
    
    # الحصول على المستخدم المدير
    admin_user = User.objects.get(username='admin')
    
    # الحصول على جميع الشركات والعناصر
    companies = Company.objects.filter(is_active=True)
    items = Item.objects.filter(is_active=True)
    
    current_year = date.today().year
    data_types = ['capacity', 'production', 'inventory', 'sales']
    
    # حذف البيانات الموجودة لإعادة إنشائها
    print("حذف البيانات القديمة...")
    Target.objects.all().delete()
    StatisticalEntry.objects.all().delete()
    
    # إنشاء مستهدفات جديدة لجميع أنواع البيانات
    targets_created = 0
    for company in companies:
        for item in items[:3]:  # أول 3 عناصر لكل شركة
            for data_type in data_types:
                for month in range(1, 13):  # من يناير إلى ديسمبر
                    # تحديد قيم مختلفة حسب نوع البيانات
                    base_quantity = 1000 + (month * 100)
                    base_value = 50000 + (month * 5000)
                    
                    if data_type == 'capacity':
                        # الطاقة الإنتاجية عادة أعلى
                        quantity_multiplier = 1.5
                        value_multiplier = 1.2
                    elif data_type == 'production':
                        # الإنتاج الفعلي
                        quantity_multiplier = 1.0
                        value_multiplier = 1.0
                    elif data_type == 'inventory':
                        # المخزون عادة أقل
                        quantity_multiplier = 0.3
                        value_multiplier = 0.8
                    elif data_type == 'sales':
                        # المبيعات
                        quantity_multiplier = 0.8
                        value_multiplier = 1.1
                    
                    target_quantity = Decimal(str(base_quantity * quantity_multiplier))
                    target_value = Decimal(str(base_value * value_multiplier))
                    
                    target, created = Target.objects.get_or_create(
                        company=company,
                        item=item,
                        data_type=data_type,
                        month=month,
                        year=current_year,
                        defaults={
                            'target_quantity': target_quantity,
                            'target_value': target_value,
                            'created_by': admin_user
                        }
                    )
                    if created:
                        targets_created += 1
    
    print(f"تم إنشاء {targets_created} مستهدف جديد")
    
    # إنشاء إدخالات إحصائية جديدة
    entries_created = 0
    for company in companies:
        for item in items[:3]:  # أول 3 عناصر لكل شركة
            for data_type in data_types:
                for month in range(1, 7):  # أول 6 أشهر
                    entry_date = date(current_year, month, 15)  # منتصف الشهر
                    
                    # الحصول على المستهدف المقابل
                    try:
                        target = Target.objects.get(
                            company=company,
                            item=item,
                            data_type=data_type,
                            month=month,
                            year=current_year
                        )
                        
                        # إنشاء قيم محققة (70-110% من المستهدف)
                        achievement_ratio = random.uniform(0.7, 1.1)
                        achieved_quantity = target.target_quantity * Decimal(str(achievement_ratio))
                        achieved_value = target.target_value * Decimal(str(achievement_ratio))
                        
                        # إضافة ملاحظات مختلفة حسب نوع البيانات
                        notes_map = {
                            'capacity': f'طاقة إنتاجية متاحة للشهر {month}',
                            'production': f'إنتاج فعلي للشهر {month}',
                            'inventory': f'مخزون نهاية الشهر {month}',
                            'sales': f'مبيعات الشهر {month}'
                        }
                        
                        entry, created = StatisticalEntry.objects.get_or_create(
                            company=company,
                            item=item,
                            data_type=data_type,
                            date=entry_date,
                            defaults={
                                'quantity': achieved_quantity,
                                'value': achieved_value,
                                'created_by': admin_user,
                                'notes': notes_map[data_type]
                            }
                        )
                        if created:
                            entries_created += 1
                            
                    except Target.DoesNotExist:
                        continue
    
    print(f"تم إنشاء {entries_created} إدخال إحصائي جديد")
    
    print("تم الانتهاء من تحديث البيانات بنجاح!")
    
    # طباعة ملخص
    print("\n=== ملخص البيانات المحدثة ===")
    print(f"الشركات: {Company.objects.count()}")
    print(f"العناصر: {Item.objects.count()}")
    print(f"المستهدفات: {Target.objects.count()}")
    print(f"الإدخالات الإحصائية: {StatisticalEntry.objects.count()}")
    
    # إحصائيات حسب نوع البيانات
    print("\n=== إحصائيات حسب نوع البيانات ===")
    for data_type, display_name in Target.DATA_TYPES:
        target_count = Target.objects.filter(data_type=data_type).count()
        entry_count = StatisticalEntry.objects.filter(data_type=data_type).count()
        print(f"{display_name}: {target_count} مستهدف، {entry_count} إدخال")

def create_sample_dashboard_data():
    """إنشاء بيانات إضافية لتحسين لوحة التحكم"""
    print("\nإنشاء بيانات إضافية للوحة التحكم...")
    
    admin_user = User.objects.get(username='admin')
    current_month = date.today().month
    current_year = date.today().year
    
    # إضافة بيانات للشهر الحالي
    companies = Company.objects.filter(is_active=True)
    items = Item.objects.filter(is_active=True)[:2]  # أول عنصرين فقط
    data_types = ['production', 'sales']  # التركيز على الإنتاج والمبيعات
    
    for company in companies:
        for item in items:
            for data_type in data_types:
                # إنشاء إدخال للشهر الحالي إذا لم يكن موجوداً
                entry_date = date(current_year, current_month, 15)
                
                try:
                    target = Target.objects.get(
                        company=company,
                        item=item,
                        data_type=data_type,
                        month=current_month,
                        year=current_year
                    )
                    
                    entry, created = StatisticalEntry.objects.get_or_create(
                        company=company,
                        item=item,
                        data_type=data_type,
                        date=entry_date,
                        defaults={
                            'quantity': target.target_quantity * Decimal(str(random.uniform(0.8, 1.2))),
                            'value': target.target_value * Decimal(str(random.uniform(0.8, 1.2))),
                            'created_by': admin_user,
                            'notes': f'بيانات {target.get_data_type_display()} للشهر الحالي'
                        }
                    )
                    
                except Target.DoesNotExist:
                    continue
    
    print("تم إنشاء بيانات إضافية للوحة التحكم")

if __name__ == '__main__':
    update_existing_data()
    create_sample_dashboard_data()
