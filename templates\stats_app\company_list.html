{% extends 'base.html' %}

{% block title %}قائمة الشركات - نظام الإحصائيات الشهرية{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3">
                    <i class="fas fa-building me-2"></i>
                    قائمة الشركات
                </h1>
                <a href="{% url 'stats_app:company_create' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    إضافة شركة جديدة
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    {% if companies %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>اسم الشركة</th>
                                        <th>القطاع</th>
                                        <th>الحالة</th>
                                        <th>تاريخ الإنشاء</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for company in companies %}
                                    <tr>
                                        <td>
                                            <strong>{{ company.name }}</strong>
                                        </td>
                                        <td>{{ company.sector|default:"غير محدد" }}</td>
                                        <td>
                                            {% if company.is_active %}
                                                <span class="badge bg-success">نشط</span>
                                            {% else %}
                                                <span class="badge bg-secondary">غير نشط</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ company.created_at|date:"Y-m-d" }}</td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{% url 'stats_app:company_update' company.pk %}" 
                                                   class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="{% url 'stats_app:company_delete' company.pk %}" 
                                                   class="btn btn-sm btn-outline-danger">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        {% if is_paginated %}
                            <nav aria-label="Page navigation">
                                <ul class="pagination justify-content-center">
                                    {% if page_obj.has_previous %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page=1">الأولى</a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}">السابقة</a>
                                        </li>
                                    {% endif %}

                                    <li class="page-item active">
                                        <span class="page-link">
                                            صفحة {{ page_obj.number }} من {{ page_obj.paginator.num_pages }}
                                        </span>
                                    </li>

                                    {% if page_obj.has_next %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.next_page_number }}">التالية</a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">الأخيرة</a>
                                        </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-building fa-3x text-muted mb-3"></i>
                            <h4 class="text-muted">لا توجد شركات مسجلة</h4>
                            <p class="text-muted">ابدأ بإضافة شركة جديدة للنظام</p>
                            <a href="{% url 'stats_app:company_create' %}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>
                                إضافة شركة جديدة
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
