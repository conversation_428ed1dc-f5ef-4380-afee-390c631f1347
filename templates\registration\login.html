<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام الإحصائيات الشهرية</title>
    
    <!-- Bootstrap CSS RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .login-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 400px;
            width: 100%;
        }
        .login-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .login-body {
            padding: 2rem;
        }
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .login-footer {
            background-color: #f8f9fa;
            padding: 1rem 2rem;
            text-align: center;
            border-top: 1px solid #e9ecef;
        }
        .input-group-text {
            background-color: #f8f9fa;
            border: 2px solid #e9ecef;
            border-left: none;
        }
        .form-control {
            border-right: none;
        }
        .input-group:focus-within .input-group-text {
            border-color: #667eea;
        }
        code {
            background-color: #f8f9fa;
            padding: 2px 6px;
            border-radius: 4px;
            color: #e83e8c;
            font-size: 0.85em;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <i class="fas fa-chart-bar fa-3x mb-3"></i>
            <h3 class="mb-0">نظام الإحصائيات الشهرية</h3>
            <p class="mb-0 mt-2 opacity-75">تسجيل الدخول للنظام</p>
        </div>
        
        <div class="login-body">
            {% if form.errors %}
                <div class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    {% for field, errors in form.errors.items %}
                        {% for error in errors %}
                            {{ error }}<br>
                        {% endfor %}
                    {% endfor %}
                </div>
            {% endif %}

            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }}" role="alert">
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}

            <form method="post">
                {% csrf_token %}
                
                <div class="mb-3">
                    <label for="{{ form.username.id_for_label }}" class="form-label">
                        <i class="fas fa-user me-2"></i>اسم المستخدم
                    </label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-user"></i>
                        </span>
                        <input type="text" 
                               class="form-control" 
                               id="{{ form.username.id_for_label }}"
                               name="{{ form.username.name }}"
                               placeholder="أدخل اسم المستخدم"
                               required>
                    </div>
                </div>

                <div class="mb-4">
                    <label for="{{ form.password.id_for_label }}" class="form-label">
                        <i class="fas fa-lock me-2"></i>كلمة المرور
                    </label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-lock"></i>
                        </span>
                        <input type="password" 
                               class="form-control" 
                               id="{{ form.password.id_for_label }}"
                               name="{{ form.password.name }}"
                               placeholder="أدخل كلمة المرور"
                               required>
                    </div>
                </div>

                <button type="submit" class="btn btn-primary btn-login w-100">
                    <i class="fas fa-sign-in-alt me-2"></i>
                    تسجيل الدخول
                </button>

                <input type="hidden" name="next" value="{{ next }}">
            </form>
        </div>

        <div class="login-footer">
            <div class="mb-3">
                <h6 class="text-primary mb-2">
                    <i class="fas fa-info-circle me-2"></i>
                    حسابات تجريبية للاختبار
                </h6>
                <div class="row text-start">
                    <div class="col-6">
                        <small><strong>مدير عام:</strong></small><br>
                        <small><code>admin / admin</code></small>
                    </div>
                    <div class="col-6">
                        <small><strong>مدير نظام:</strong></small><br>
                        <small><code>alaa / alaa123</code></small>
                    </div>
                </div>
                <div class="row text-start mt-2">
                    <div class="col-6">
                        <small><strong>مستخدم شركة:</strong></small><br>
                        <small><code>alaa1 / alaa1123</code></small>
                    </div>
                    <div class="col-6">
                        <small><strong>مستخدم شركة:</strong></small><br>
                        <small><code>alaa2 / alaa2123</code></small>
                    </div>
                </div>
                <div class="row text-start mt-2">
                    <div class="col-6">
                        <small><strong>مستخدم تجريبي:</strong></small><br>
                        <small><code>testuser / test123456</code></small>
                    </div>
                    <div class="col-6">
                        <small><strong>مدير نظام:</strong></small><br>
                        <small><code>manager / manager123</code></small>
                    </div>
                </div>
            </div>
            <small class="text-muted">
                <i class="fas fa-shield-alt me-1"></i>
                نظام آمن ومحمي
            </small>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // تركيز على حقل اسم المستخدم عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('{{ form.username.id_for_label }}').focus();
        });

        // إضافة تأثيرات بصرية
        document.querySelectorAll('.form-control').forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.classList.add('focused');
            });
            
            input.addEventListener('blur', function() {
                this.parentElement.classList.remove('focused');
            });
        });
    </script>
</body>
</html>
