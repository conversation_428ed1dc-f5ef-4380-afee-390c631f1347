{% extends 'base.html' %}
{% load static %}

{% block title %}إعدادات النظام{% endblock %}

{% block extra_css %}
<style>
.settings-card {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.fiscal-year-info {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    margin-top: 15px;
}

.current-fiscal-year {
    font-size: 1.2rem;
    font-weight: bold;
    color: #007bff;
}

.month-select {
    width: 100%;
}

.form-section {
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 20px;
    margin-bottom: 20px;
}

.form-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.section-title {
    color: #495057;
    font-weight: 600;
    margin-bottom: 15px;
    padding-bottom: 5px;
    border-bottom: 2px solid #007bff;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-cog me-2"></i>إعدادات النظام</h2>
                {% if not user.is_superuser %}
                <div class="alert alert-warning mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    يمكن لمدير النظام فقط تعديل هذه الإعدادات
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <form method="post" {% if not user.is_superuser %}style="pointer-events: none; opacity: 0.6;"{% endif %}>
        {% csrf_token %}
        
        <div class="row">
            <!-- إعدادات السنة المالية -->
            <div class="col-lg-8">
                <div class="settings-card">
                    <div class="form-section">
                        <h5 class="section-title">
                            <i class="fas fa-calendar-alt me-2"></i>إعدادات السنة المالية
                        </h5>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <h6><i class="fas fa-play me-2"></i>بداية السنة المالية</h6>
                                <div class="row">
                                    <div class="col-6">
                                        <label for="fiscal_year_start_day" class="form-label">اليوم</label>
                                        <input type="number" class="form-control" id="fiscal_year_start_day" 
                                               name="fiscal_year_start_day" value="{{ settings.fiscal_year_start_day }}"
                                               min="1" max="31" required>
                                    </div>
                                    <div class="col-6">
                                        <label for="fiscal_year_start_month" class="form-label">الشهر</label>
                                        <select class="form-select month-select" id="fiscal_year_start_month" 
                                                name="fiscal_year_start_month" required>
                                            <option value="1" {% if settings.fiscal_year_start_month == 1 %}selected{% endif %}>يناير</option>
                                            <option value="2" {% if settings.fiscal_year_start_month == 2 %}selected{% endif %}>فبراير</option>
                                            <option value="3" {% if settings.fiscal_year_start_month == 3 %}selected{% endif %}>مارس</option>
                                            <option value="4" {% if settings.fiscal_year_start_month == 4 %}selected{% endif %}>أبريل</option>
                                            <option value="5" {% if settings.fiscal_year_start_month == 5 %}selected{% endif %}>مايو</option>
                                            <option value="6" {% if settings.fiscal_year_start_month == 6 %}selected{% endif %}>يونيو</option>
                                            <option value="7" {% if settings.fiscal_year_start_month == 7 %}selected{% endif %}>يوليو</option>
                                            <option value="8" {% if settings.fiscal_year_start_month == 8 %}selected{% endif %}>أغسطس</option>
                                            <option value="9" {% if settings.fiscal_year_start_month == 9 %}selected{% endif %}>سبتمبر</option>
                                            <option value="10" {% if settings.fiscal_year_start_month == 10 %}selected{% endif %}>أكتوبر</option>
                                            <option value="11" {% if settings.fiscal_year_start_month == 11 %}selected{% endif %}>نوفمبر</option>
                                            <option value="12" {% if settings.fiscal_year_start_month == 12 %}selected{% endif %}>ديسمبر</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <h6><i class="fas fa-stop me-2"></i>نهاية السنة المالية</h6>
                                <div class="row">
                                    <div class="col-6">
                                        <label for="fiscal_year_end_day" class="form-label">اليوم</label>
                                        <input type="number" class="form-control" id="fiscal_year_end_day" 
                                               name="fiscal_year_end_day" value="{{ settings.fiscal_year_end_day }}"
                                               min="1" max="31" required>
                                    </div>
                                    <div class="col-6">
                                        <label for="fiscal_year_end_month" class="form-label">الشهر</label>
                                        <select class="form-select month-select" id="fiscal_year_end_month" 
                                                name="fiscal_year_end_month" required>
                                            <option value="1" {% if settings.fiscal_year_end_month == 1 %}selected{% endif %}>يناير</option>
                                            <option value="2" {% if settings.fiscal_year_end_month == 2 %}selected{% endif %}>فبراير</option>
                                            <option value="3" {% if settings.fiscal_year_end_month == 3 %}selected{% endif %}>مارس</option>
                                            <option value="4" {% if settings.fiscal_year_end_month == 4 %}selected{% endif %}>أبريل</option>
                                            <option value="5" {% if settings.fiscal_year_end_month == 5 %}selected{% endif %}>مايو</option>
                                            <option value="6" {% if settings.fiscal_year_end_month == 6 %}selected{% endif %}>يونيو</option>
                                            <option value="7" {% if settings.fiscal_year_end_month == 7 %}selected{% endif %}>يوليو</option>
                                            <option value="8" {% if settings.fiscal_year_end_month == 8 %}selected{% endif %}>أغسطس</option>
                                            <option value="9" {% if settings.fiscal_year_end_month == 9 %}selected{% endif %}>سبتمبر</option>
                                            <option value="10" {% if settings.fiscal_year_end_month == 10 %}selected{% endif %}>أكتوبر</option>
                                            <option value="11" {% if settings.fiscal_year_end_month == 11 %}selected{% endif %}>نوفمبر</option>
                                            <option value="12" {% if settings.fiscal_year_end_month == 12 %}selected{% endif %}>ديسمبر</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="fiscal-year-info">
                            <h6><i class="fas fa-info-circle me-2"></i>معلومات السنة المالية الحالية</h6>
                            <div class="current-fiscal-year" id="currentFiscalYear">
                                السنة المالية الحالية: {{ settings.get_current_fiscal_year }}
                            </div>
                            <small class="text-muted">
                                تبدأ من {{ settings.fiscal_year_start_day }}/{{ settings.fiscal_year_start_month }} 
                                وتنتهي في {{ settings.fiscal_year_end_day }}/{{ settings.fiscal_year_end_month }}
                            </small>
                        </div>
                    </div>
                    
                    <!-- إعدادات عامة -->
                    <div class="form-section">
                        <h5 class="section-title">
                            <i class="fas fa-sliders-h me-2"></i>الإعدادات العامة
                        </h5>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <label for="system_name" class="form-label">اسم النظام</label>
                                <input type="text" class="form-control" id="system_name" 
                                       name="system_name" value="{{ settings.system_name }}" required>
                            </div>
                            <div class="col-md-6">
                                <label for="default_currency" class="form-label">العملة الافتراضية</label>
                                <input type="text" class="form-control" id="default_currency" 
                                       name="default_currency" value="{{ settings.default_currency }}" required>
                            </div>
                        </div>
                    </div>
                    
                    <!-- إعدادات التقارير -->
                    <div class="form-section">
                        <h5 class="section-title">
                            <i class="fas fa-chart-bar me-2"></i>إعدادات التقارير
                        </h5>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <label for="reports_decimal_places" class="form-label">عدد المنازل العشرية</label>
                                <select class="form-select" id="reports_decimal_places" name="reports_decimal_places">
                                    <option value="0" {% if settings.reports_decimal_places == 0 %}selected{% endif %}>0</option>
                                    <option value="1" {% if settings.reports_decimal_places == 1 %}selected{% endif %}>1</option>
                                    <option value="2" {% if settings.reports_decimal_places == 2 %}selected{% endif %}>2</option>
                                    <option value="3" {% if settings.reports_decimal_places == 3 %}selected{% endif %}>3</option>
                                    <option value="4" {% if settings.reports_decimal_places == 4 %}selected{% endif %}>4</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" id="show_fiscal_year_in_reports" 
                                           name="show_fiscal_year_in_reports" 
                                           {% if settings.show_fiscal_year_in_reports %}checked{% endif %}>
                                    <label class="form-check-label" for="show_fiscal_year_in_reports">
                                        عرض السنة المالية في التقارير
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    {% if user.is_superuser %}
                    <div class="text-end">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>حفظ الإعدادات
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="location.reload()">
                            <i class="fas fa-undo me-2"></i>إلغاء
                        </button>
                    </div>
                    {% endif %}
                </div>
            </div>
            
            <!-- معلومات إضافية -->
            <div class="col-lg-4">
                <div class="settings-card">
                    <h5><i class="fas fa-info-circle me-2"></i>معلومات النظام</h5>
                    
                    <div class="mb-3">
                        <strong>آخر تحديث:</strong><br>
                        <small class="text-muted">{{ settings.updated_at|date:"d/m/Y H:i" }}</small>
                    </div>
                    
                    {% if settings.updated_by %}
                    <div class="mb-3">
                        <strong>محدث بواسطة:</strong><br>
                        <small class="text-muted">{{ settings.updated_by.username }}</small>
                    </div>
                    {% endif %}
                    
                    <div class="mb-3">
                        <strong>تاريخ الإنشاء:</strong><br>
                        <small class="text-muted">{{ settings.created_at|date:"d/m/Y H:i" }}</small>
                    </div>
                </div>
                
                <div class="settings-card">
                    <h5><i class="fas fa-lightbulb me-2"></i>نصائح</h5>
                    
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            السنة المالية تبدأ من 1 يوليو وتنتهي في 30 يونيو
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            يتم تحديد السنة المالية تلقائياً للبيانات الجديدة
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            التقارير ستعرض البيانات حسب السنة المالية
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                            تغيير السنة المالية لا يؤثر على البيانات الموجودة
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
// تحديث معلومات السنة المالية عند تغيير التواريخ
function updateFiscalYearInfo() {
    const startMonth = document.getElementById('fiscal_year_start_month').value;
    const startDay = document.getElementById('fiscal_year_start_day').value;
    const endMonth = document.getElementById('fiscal_year_end_month').value;
    const endDay = document.getElementById('fiscal_year_end_day').value;
    
    // حساب السنة المالية الحالية
    const today = new Date();
    const currentYear = today.getFullYear();
    const fiscalStart = new Date(currentYear, startMonth - 1, startDay);
    
    let fiscalYear;
    if (today >= fiscalStart) {
        fiscalYear = currentYear;
    } else {
        fiscalYear = currentYear - 1;
    }
    
    document.getElementById('currentFiscalYear').innerHTML = 
        `السنة المالية الحالية: ${fiscalYear}`;
}

// ربط الأحداث
document.addEventListener('DOMContentLoaded', function() {
    const fiscalInputs = [
        'fiscal_year_start_month', 'fiscal_year_start_day',
        'fiscal_year_end_month', 'fiscal_year_end_day'
    ];
    
    fiscalInputs.forEach(inputId => {
        document.getElementById(inputId).addEventListener('change', updateFiscalYearInfo);
    });
});
</script>
{% endblock %}
