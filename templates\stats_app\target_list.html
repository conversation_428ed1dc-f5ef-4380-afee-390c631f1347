{% extends 'base.html' %}

{% block title %}قائمة المستهدفات - نظام الإحصائيات الشهرية{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3">
                    <i class="fas fa-bullseye me-2"></i>
                    قائمة المستهدفات
                </h1>
                <a href="{% url 'stats_app:target_create' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    إضافة مستهدف جديد
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    {% if targets %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>الشركة</th>
                                        <th>العنصر</th>
                                        <th>الشهر</th>
                                        <th>السنة</th>
                                        <th>الكمية المستهدفة</th>
                                        <th>القيمة المستهدفة</th>
                                        <th>أنشئ بواسطة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for target in targets %}
                                    <tr>
                                        <td>
                                            <strong>{{ target.company.name }}</strong>
                                        </td>
                                        <td>{{ target.item.name }}</td>
                                        <td>
                                            <span class="badge bg-info">{{ target.get_month_display }}</span>
                                        </td>
                                        <td>{{ target.year }}</td>
                                        <td>{{ target.target_quantity|floatformat:2 }} {{ target.item.unit }}</td>
                                        <td>{{ target.target_value|floatformat:2 }}</td>
                                        <td>{{ target.created_by.get_full_name|default:target.created_by.username }}</td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{% url 'stats_app:target_update' target.pk %}" 
                                                   class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="{% url 'stats_app:target_delete' target.pk %}" 
                                                   class="btn btn-sm btn-outline-danger">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        {% if is_paginated %}
                            <nav aria-label="Page navigation">
                                <ul class="pagination justify-content-center">
                                    {% if page_obj.has_previous %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page=1">الأولى</a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}">السابقة</a>
                                        </li>
                                    {% endif %}

                                    <li class="page-item active">
                                        <span class="page-link">
                                            صفحة {{ page_obj.number }} من {{ page_obj.paginator.num_pages }}
                                        </span>
                                    </li>

                                    {% if page_obj.has_next %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.next_page_number }}">التالية</a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">الأخيرة</a>
                                        </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-bullseye fa-3x text-muted mb-3"></i>
                            <h4 class="text-muted">لا توجد مستهدفات مسجلة</h4>
                            <p class="text-muted">ابدأ بإضافة مستهدف جديد للنظام</p>
                            <a href="{% url 'stats_app:target_create' %}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>
                                إضافة مستهدف جديد
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
