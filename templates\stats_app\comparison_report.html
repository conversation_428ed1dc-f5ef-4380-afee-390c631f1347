{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}تقرير مقارنة الأداء - نظام الإحصائيات الشهرية{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3">
                    <i class="fas fa-balance-scale me-2"></i>
                    تقرير مقارنة الأداء
                </h1>
                <a href="{% url 'stats_app:reports' %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>
                    العودة للتقارير
                </a>
            </div>
        </div>
    </div>

    <!-- نموذج التصفية -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-filter me-2"></i>
                        تصفية البيانات
                    </h5>
                </div>
                <div class="card-body">
                    <form method="get">
                        {% crispy form %}
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- نتائج المقارنة -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-table me-2"></i>
                        نتائج المقارنة
                    </h5>
                    {% if comparison_data %}
                        <button class="btn btn-sm btn-success" onclick="exportToExcel()">
                            <i class="fas fa-file-excel me-2"></i>
                            تصدير Excel
                        </button>
                    {% endif %}
                </div>
                <div class="card-body">
                    {% if comparison_data %}
                        <div class="table-responsive">
                            <table class="table table-hover" id="comparisonTable">
                                <thead class="table-light">
                                    <tr>
                                        <th>الشركة</th>
                                        <th>العنصر</th>
                                        <th>نوع البيانات</th>
                                        <th>الفترة</th>
                                        <th>الكمية المستهدفة</th>
                                        <th>الكمية المحققة</th>
                                        <th>نسبة إنجاز الكمية</th>
                                        <th>القيمة المستهدفة</th>
                                        <th>القيمة المحققة</th>
                                        <th>نسبة إنجاز القيمة</th>
                                        <th>الحالة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for data in comparison_data %}
                                    <tr>
                                        <td><strong>{{ data.target.company.name }}</strong></td>
                                        <td>{{ data.target.item.name }}</td>
                                        <td>
                                            <span class="badge
                                                {% if data.target.data_type == 'capacity' %}bg-primary
                                                {% elif data.target.data_type == 'production' %}bg-success
                                                {% elif data.target.data_type == 'inventory' %}bg-warning
                                                {% elif data.target.data_type == 'sales' %}bg-danger
                                                {% endif %}">
                                                {{ data.target.get_data_type_display }}
                                            </span>
                                        </td>
                                        <td>{{ data.target.get_month_display }} {{ data.target.year }}</td>
                                        <td>{{ data.target.target_quantity|floatformat:2 }} {{ data.target.item.unit }}</td>
                                        <td>{{ data.entry.quantity|floatformat:2 }} {{ data.entry.item.unit }}</td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <span class="badge {% if data.quantity_achievement >= 100 %}bg-success{% elif data.quantity_achievement >= 75 %}bg-warning{% else %}bg-danger{% endif %} me-2">
                                                    {{ data.quantity_achievement }}%
                                                </span>
                                                <div class="progress flex-grow-1" style="height: 8px;">
                                                    <div class="progress-bar {% if data.quantity_achievement >= 100 %}bg-success{% elif data.quantity_achievement >= 75 %}bg-warning{% else %}bg-danger{% endif %}" 
                                                         style="width: {{ data.quantity_achievement|floatformat:0 }}%"></div>
                                                </div>
                                            </div>
                                        </td>
                                        <td>{{ data.target.target_value|floatformat:2 }}</td>
                                        <td>{{ data.entry.value|floatformat:2 }}</td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <span class="badge {% if data.value_achievement >= 100 %}bg-success{% elif data.value_achievement >= 75 %}bg-warning{% else %}bg-danger{% endif %} me-2">
                                                    {{ data.value_achievement }}%
                                                </span>
                                                <div class="progress flex-grow-1" style="height: 8px;">
                                                    <div class="progress-bar {% if data.value_achievement >= 100 %}bg-success{% elif data.value_achievement >= 75 %}bg-warning{% else %}bg-danger{% endif %}" 
                                                         style="width: {{ data.value_achievement|floatformat:0 }}%"></div>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            {% if data.quantity_achievement >= 100 and data.value_achievement >= 100 %}
                                                <span class="badge bg-success">
                                                    <i class="fas fa-check me-1"></i>
                                                    مكتمل
                                                </span>
                                            {% elif data.quantity_achievement >= 75 or data.value_achievement >= 75 %}
                                                <span class="badge bg-warning">
                                                    <i class="fas fa-clock me-1"></i>
                                                    جيد
                                                </span>
                                            {% else %}
                                                <span class="badge bg-danger">
                                                    <i class="fas fa-exclamation me-1"></i>
                                                    يحتاج تحسين
                                                </span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- ملخص الإحصائيات -->
                        <div class="row mt-4">
                            <div class="col-md-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body text-center">
                                        <h4>{{ comparison_data|length }}</h4>
                                        <small>إجمالي المقارنات</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-success text-white">
                                    <div class="card-body text-center">
                                        <h4 id="completedCount">0</h4>
                                        <small>مكتملة</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-warning text-white">
                                    <div class="card-body text-center">
                                        <h4 id="goodCount">0</h4>
                                        <small>جيدة</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-danger text-white">
                                    <div class="card-body text-center">
                                        <h4 id="needsImprovementCount">0</h4>
                                        <small>تحتاج تحسين</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-search fa-3x text-muted mb-3"></i>
                            <h4 class="text-muted">لا توجد بيانات للمقارنة</h4>
                            <p class="text-muted">
                                {% if request.GET %}
                                    لا توجد نتائج تطابق معايير البحث المحددة
                                {% else %}
                                    استخدم نموذج التصفية أعلاه لعرض البيانات
                                {% endif %}
                            </p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// حساب الإحصائيات
document.addEventListener('DOMContentLoaded', function() {
    const badges = document.querySelectorAll('.badge');
    let completed = 0, good = 0, needsImprovement = 0;
    
    badges.forEach(badge => {
        if (badge.textContent.includes('مكتمل')) completed++;
        else if (badge.textContent.includes('جيد')) good++;
        else if (badge.textContent.includes('يحتاج تحسين')) needsImprovement++;
    });
    
    document.getElementById('completedCount').textContent = completed;
    document.getElementById('goodCount').textContent = good;
    document.getElementById('needsImprovementCount').textContent = needsImprovement;
});

// تصدير إلى Excel
function exportToExcel() {
    const table = document.getElementById('comparisonTable');
    const wb = XLSX.utils.table_to_book(table, {sheet: "تقرير المقارنة"});
    XLSX.writeFile(wb, 'تقرير_مقارنة_الأداء.xlsx');
}
</script>

<!-- مكتبة XLSX للتصدير -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
{% endblock %}
