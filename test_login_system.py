#!/usr/bin/env python
"""
سكريبت شامل لاختبار نظام تسجيل الدخول
"""

import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'statistical_system.settings')
django.setup()

from django.contrib.auth.models import User
from stats_app.models import UserProfile, Company
from django.contrib.auth import authenticate
from django.test import Client
from django.urls import reverse

def test_authentication():
    """اختبار المصادقة للمستخدمين"""
    print("=== اختبار المصادقة ===\n")
    
    test_users = [
        ("admin", "admin", "مدير عام"),
        ("testuser", "test123456", "مستخدم شركة"),
        ("manager", "manager123", "مدير نظام"),
    ]
    
    success_count = 0
    
    for username, password, role in test_users:
        print(f"اختبار {role}: {username}")
        
        # اختبار المصادقة
        user = authenticate(username=username, password=password)
        
        if user:
            print(f"  ✅ المصادقة ناجحة")
            
            # التحقق من UserProfile
            try:
                profile = user.userprofile
                print(f"  ✅ ملف شخصي موجود")
                print(f"     - مدير النظام: {profile.is_system_admin}")
                print(f"     - مستخدم شركة: {profile.is_company_user}")
                print(f"     - الشركة: {profile.company.name if profile.company else 'غير محدد'}")
                success_count += 1
            except UserProfile.DoesNotExist:
                print(f"  ❌ ملف شخصي غير موجود")
        else:
            print(f"  ❌ فشل في المصادقة")
            
            # التحقق من وجود المستخدم
            if User.objects.filter(username=username).exists():
                print(f"     السبب: كلمة مرور خاطئة")
            else:
                print(f"     السبب: المستخدم غير موجود")
        
        print()
    
    print(f"النتيجة: {success_count}/{len(test_users)} مستخدم نجح في المصادقة\n")
    return success_count == len(test_users)

def test_login_views():
    """اختبار صفحات تسجيل الدخول"""
    print("=== اختبار صفحات تسجيل الدخول ===\n")
    
    client = Client()
    
    # اختبار صفحة تسجيل الدخول
    print("اختبار صفحة تسجيل الدخول...")
    response = client.get('/accounts/login/')
    
    if response.status_code == 200:
        print("✅ صفحة تسجيل الدخول تعمل بشكل صحيح")
    else:
        print(f"❌ خطأ في صفحة تسجيل الدخول: {response.status_code}")
        return False
    
    # اختبار تسجيل الدخول الفعلي
    print("\nاختبار تسجيل الدخول الفعلي...")
    
    login_data = {
        'username': 'testuser',
        'password': 'test123456'
    }
    
    response = client.post('/accounts/login/', login_data)
    
    if response.status_code == 302:  # إعادة توجيه بعد تسجيل الدخول الناجح
        print("✅ تسجيل الدخول ناجح")
        
        # اختبار الوصول إلى صفحة محمية
        response = client.get('/')
        if response.status_code == 200:
            print("✅ الوصول إلى الصفحة الرئيسية ناجح")
        else:
            print(f"❌ فشل في الوصول إلى الصفحة الرئيسية: {response.status_code}")
            return False
            
    else:
        print(f"❌ فشل في تسجيل الدخول: {response.status_code}")
        return False
    
    print()
    return True

def test_permissions():
    """اختبار الصلاحيات"""
    print("=== اختبار الصلاحيات ===\n")
    
    client = Client()
    
    # اختبار مستخدم شركة
    print("اختبار صلاحيات مستخدم الشركة...")
    client.login(username='testuser', password='test123456')
    
    # اختبار الوصول إلى صفحات مختلفة
    test_urls = [
        ('/', 'الصفحة الرئيسية'),
        ('/companies/', 'قائمة الشركات'),
        ('/items/', 'قائمة العناصر'),
        ('/entries/', 'الإدخالات الإحصائية'),
        ('/reports/', 'التقارير'),
    ]
    
    accessible_count = 0
    
    for url, name in test_urls:
        response = client.get(url)
        if response.status_code == 200:
            print(f"  ✅ {name}: يمكن الوصول إليها")
            accessible_count += 1
        else:
            print(f"  ❌ {name}: لا يمكن الوصول إليها ({response.status_code})")
    
    client.logout()
    
    print(f"\nالنتيجة: {accessible_count}/{len(test_urls)} صفحة يمكن الوصول إليها")
    print()
    
    return accessible_count > 0

def test_user_profiles():
    """اختبار ملفات المستخدمين"""
    print("=== اختبار ملفات المستخدمين ===\n")
    
    users = User.objects.all()
    profiles = UserProfile.objects.all()
    
    print(f"إجمالي المستخدمين: {users.count()}")
    print(f"إجمالي الملفات الشخصية: {profiles.count()}")
    
    # التحقق من التطابق
    if users.count() == profiles.count():
        print("✅ جميع المستخدمين لديهم ملفات شخصية")
    else:
        print(f"⚠️ عدم تطابق: {users.count() - profiles.count()} مستخدم بدون ملف شخصي")
    
    # التحقق من الملفات المكررة
    duplicate_count = 0
    for user in users:
        profile_count = UserProfile.objects.filter(user=user).count()
        if profile_count > 1:
            duplicate_count += 1
            print(f"⚠️ المستخدم {user.username} لديه {profile_count} ملفات شخصية")
    
    if duplicate_count == 0:
        print("✅ لا توجد ملفات شخصية مكررة")
    else:
        print(f"⚠️ {duplicate_count} مستخدم لديه ملفات مكررة")
    
    print()
    return users.count() == profiles.count() and duplicate_count == 0

def create_missing_users():
    """إنشاء المستخدمين المفقودين"""
    print("=== إنشاء المستخدمين المفقودين ===\n")
    
    required_users = [
        ("admin", "admin", "<EMAIL>", True, False, False),
        ("testuser", "test123456", "<EMAIL>", False, True, False),
        ("manager", "manager123", "<EMAIL>", False, False, True),
    ]
    
    created_count = 0
    
    for username, password, email, is_superuser, is_company_user, is_system_admin in required_users:
        if not User.objects.filter(username=username).exists():
            # إنشاء المستخدم
            user = User.objects.create_user(
                username=username,
                password=password,
                email=email,
                is_superuser=is_superuser,
                is_staff=is_superuser or is_system_admin
            )
            
            # تحديث الملف الشخصي
            profile = user.userprofile
            profile.is_company_user = is_company_user
            profile.is_system_admin = is_system_admin
            
            if is_company_user and Company.objects.exists():
                profile.company = Company.objects.first()
            
            profile.save()
            
            print(f"✅ تم إنشاء المستخدم: {username}")
            created_count += 1
        else:
            print(f"✓ المستخدم موجود: {username}")
    
    print(f"\nتم إنشاء {created_count} مستخدم جديد")
    print()

def main():
    """الدالة الرئيسية"""
    print("🔐 اختبار شامل لنظام تسجيل الدخول")
    print("="*60)
    
    # إنشاء المستخدمين المفقودين
    create_missing_users()
    
    # اختبار ملفات المستخدمين
    profiles_ok = test_user_profiles()
    
    # اختبار المصادقة
    auth_ok = test_authentication()
    
    # اختبار صفحات تسجيل الدخول
    views_ok = test_login_views()
    
    # اختبار الصلاحيات
    permissions_ok = test_permissions()
    
    # النتيجة النهائية
    print("="*60)
    print("📊 ملخص النتائج:")
    print(f"  ملفات المستخدمين: {'✅' if profiles_ok else '❌'}")
    print(f"  المصادقة: {'✅' if auth_ok else '❌'}")
    print(f"  صفحات تسجيل الدخول: {'✅' if views_ok else '❌'}")
    print(f"  الصلاحيات: {'✅' if permissions_ok else '❌'}")
    
    if all([profiles_ok, auth_ok, views_ok, permissions_ok]):
        print("\n🎉 جميع الاختبارات نجحت! نظام تسجيل الدخول يعمل بشكل مثالي")
        print("\n📋 يمكنك الآن تسجيل الدخول باستخدام:")
        print("🌐 الرابط: http://127.0.0.1:8000/accounts/login/")
        print("👤 المستخدمين:")
        print("   • admin / admin (مدير عام)")
        print("   • testuser / test123456 (مستخدم شركة)")
        print("   • manager / manager123 (مدير نظام)")
    else:
        print("\n❌ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه")
    
    print("="*60)

if __name__ == '__main__':
    main()
