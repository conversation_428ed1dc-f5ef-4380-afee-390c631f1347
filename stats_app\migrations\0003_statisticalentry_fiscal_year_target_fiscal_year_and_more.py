# Generated by Django 5.0.1 on 2025-06-10 07:50

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('stats_app', '0002_alter_statisticalentry_options_alter_target_options_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='statisticalentry',
            name='fiscal_year',
            field=models.IntegerField(blank=True, null=True, verbose_name='السنة المالية'),
        ),
        migrations.AddField(
            model_name='target',
            name='fiscal_year',
            field=models.IntegerField(blank=True, null=True, verbose_name='السنة المالية'),
        ),
        migrations.CreateModel(
            name='SystemSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('fiscal_year_start_month', models.IntegerField(default=7, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(12)], verbose_name='شهر بداية السنة المالية')),
                ('fiscal_year_start_day', models.IntegerField(default=1, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(31)], verbose_name='يوم بداية السنة المالية')),
                ('fiscal_year_end_month', models.IntegerField(default=6, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(12)], verbose_name='شهر نهاية السنة المالية')),
                ('fiscal_year_end_day', models.IntegerField(default=30, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(31)], verbose_name='يوم نهاية السنة المالية')),
                ('system_name', models.CharField(default='نظام الإحصائيات الشهرية', max_length=200, verbose_name='اسم النظام')),
                ('default_currency', models.CharField(default='ريال', max_length=10, verbose_name='العملة الافتراضية')),
                ('reports_decimal_places', models.IntegerField(default=2, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(6)], verbose_name='عدد المنازل العشرية في التقارير')),
                ('default_report_year', models.IntegerField(blank=True, null=True, verbose_name='السنة الافتراضية للتقارير')),
                ('show_fiscal_year_in_reports', models.BooleanField(default=True, verbose_name='عرض السنة المالية في التقارير')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='محدث بواسطة')),
            ],
            options={
                'verbose_name': 'إعدادات النظام',
                'verbose_name_plural': 'إعدادات النظام',
            },
        ),
    ]
