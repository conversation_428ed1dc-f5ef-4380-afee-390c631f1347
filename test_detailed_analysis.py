#!/usr/bin/env python
"""
سكريپت اختبار شامل للتحليل التفصيلي
"""

import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'statistical_system.settings')
django.setup()

from django.test import Client
from django.contrib.auth.models import User
import json

def test_detailed_analysis_apis():
    """اختبار جميع دوال التحليل التفصيلي"""
    print("🧪 اختبار دوال التحليل التفصيلي")
    print("="*50)
    
    client = Client()
    
    # قائمة المستخدمين للاختبار
    test_users = [
        ('alaa1', 'alaa1123', 'مستخدم شركة'),
        ('alaa', 'alaa123', 'مدير نظام'),
    ]
    
    # قائمة دوال API للاختبار
    api_functions = [
        ('monthly_comparison', 'مقارنة شهرية'),
        ('company_performance', 'أداء الشركات'),
        ('detailed_monthly_data', 'بيانات شهرية تفصيلية'),
        ('data_types_comparison', 'مقارنة أنواع البيانات'),
        ('company_data_types', 'أنواع بيانات الشركة'),
        ('achievement_analysis', 'تحليل الإنجاز'),
        ('trend_analysis', 'تحليل الاتجاه'),
        ('capacity_utilization', 'استخدام الطاقة'),
        ('kpi_dashboard', 'لوحة المؤشرات'),
        ('variance_analysis', 'تحليل التباين'),
        ('efficiency_metrics', 'مقاييس الكفاءة'),
        ('seasonal_analysis', 'التحليل الموسمي'),
        ('performance_ranking', 'ترتيب الأداء'),
        ('growth_analysis', 'تحليل النمو'),
        ('risk_assessment', 'تقييم المخاطر'),
        ('forecasting_data', 'بيانات التنبؤ'),
        ('benchmark_analysis', 'تحليل المعايير'),
        ('correlation_matrix', 'مصفوفة الارتباط'),
    ]
    
    results = {}
    
    for username, password, role in test_users:
        print(f"\n👤 اختبار {role}: {username}")
        print("-" * 30)
        
        # تسجيل الدخول
        login_success = client.login(username=username, password=password)
        
        if not login_success:
            print(f"❌ فشل في تسجيل الدخول للمستخدم {username}")
            continue
        
        user_results = {}
        
        for api_function, description in api_functions:
            print(f"   🔍 اختبار {description}...")
            
            try:
                # استدعاء API
                url = f'/reports/charts/api/?chart_type={api_function}'
                response = client.get(url)
                
                if response.status_code == 200:
                    try:
                        # محاولة تحليل JSON
                        data = json.loads(response.content)
                        
                        # التحقق من وجود بيانات
                        has_data = False
                        if isinstance(data, dict):
                            if 'datasets' in data and data['datasets']:
                                has_data = any(dataset.get('data') for dataset in data['datasets'])
                            elif 'data' in data:
                                has_data = bool(data['data'])
                            elif any(key for key in data.keys() if key not in ['labels', 'chart_data']):
                                has_data = True
                        
                        status = "✅ يعمل" if has_data else "⚠️ لا توجد بيانات"
                        user_results[api_function] = {
                            'status': 'success',
                            'has_data': has_data,
                            'response_size': len(str(data))
                        }
                        
                    except json.JSONDecodeError:
                        status = "❌ خطأ في JSON"
                        user_results[api_function] = {
                            'status': 'json_error',
                            'has_data': False,
                            'response_size': 0
                        }
                else:
                    status = f"❌ خطأ {response.status_code}"
                    user_results[api_function] = {
                        'status': f'error_{response.status_code}',
                        'has_data': False,
                        'response_size': 0
                    }
                
                print(f"      {status}")
                
            except Exception as e:
                print(f"      ❌ خطأ: {str(e)[:50]}...")
                user_results[api_function] = {
                    'status': 'exception',
                    'has_data': False,
                    'response_size': 0,
                    'error': str(e)
                }
        
        results[username] = user_results
        client.logout()
    
    return results

def analyze_test_results(results):
    """تحليل نتائج الاختبار"""
    print("\n📊 تحليل نتائج الاختبار")
    print("="*40)
    
    total_tests = 0
    successful_tests = 0
    tests_with_data = 0
    
    for username, user_results in results.items():
        print(f"\n👤 {username}:")
        
        user_total = len(user_results)
        user_success = sum(1 for result in user_results.values() if result['status'] == 'success')
        user_with_data = sum(1 for result in user_results.values() if result['has_data'])
        
        print(f"   📈 إجمالي الاختبارات: {user_total}")
        print(f"   ✅ الناجحة: {user_success}")
        print(f"   📊 التي تحتوي على بيانات: {user_with_data}")
        print(f"   📊 نسبة النجاح: {(user_success/user_total*100):.1f}%")
        
        total_tests += user_total
        successful_tests += user_success
        tests_with_data += user_with_data
        
        # عرض الاختبارات الفاشلة
        failed_tests = [name for name, result in user_results.items() if result['status'] != 'success']
        if failed_tests:
            print(f"   ❌ الاختبارات الفاشلة: {', '.join(failed_tests[:3])}{'...' if len(failed_tests) > 3 else ''}")
    
    print(f"\n📋 الملخص العام:")
    print(f"   📈 إجمالي الاختبارات: {total_tests}")
    print(f"   ✅ الناجحة: {successful_tests}")
    print(f"   📊 التي تحتوي على بيانات: {tests_with_data}")
    print(f"   📊 نسبة النجاح الإجمالية: {(successful_tests/total_tests*100):.1f}%")
    
    return {
        'total_tests': total_tests,
        'successful_tests': successful_tests,
        'tests_with_data': tests_with_data,
        'success_rate': successful_tests/total_tests*100 if total_tests > 0 else 0
    }

def test_data_separation_in_apis():
    """اختبار فصل البيانات في APIs"""
    print("\n🔒 اختبار فصل البيانات في APIs")
    print("="*40)
    
    client = Client()
    
    # اختبار مستخدم شركة
    print("👤 اختبار مستخدم الشركة (alaa1)...")
    client.login(username='alaa1', password='alaa1123')
    
    # اختبار API مع معرف شركة غير مسموح
    response1 = client.get('/reports/charts/api/?chart_type=company_performance&company=999')
    
    # اختبار API بدون معرف شركة
    response2 = client.get('/reports/charts/api/?chart_type=company_performance')
    
    client.logout()
    
    # اختبار مدير النظام
    print("👤 اختبار مدير النظام (alaa)...")
    client.login(username='alaa', password='alaa123')
    
    # اختبار API مع معرف شركة
    response3 = client.get('/reports/charts/api/?chart_type=company_performance&company=1')
    
    client.logout()
    
    # تحليل النتائج
    separation_working = True
    
    if response1.status_code == 200 and response2.status_code == 200:
        try:
            data1 = json.loads(response1.content)
            data2 = json.loads(response2.content)
            
            # يجب أن تكون البيانات متشابهة (لأن معرف الشركة غير المسموح يتم تجاهله)
            if data1 != data2:
                print("⚠️ قد لا يعمل فصل البيانات بشكل صحيح")
                separation_working = False
            else:
                print("✅ فصل البيانات يعمل - معرف الشركة غير المسموح تم تجاهله")
        except:
            print("⚠️ خطأ في تحليل استجابة API")
            separation_working = False
    
    if response3.status_code == 200:
        print("✅ مدير النظام يمكنه الوصول لجميع البيانات")
    else:
        print("❌ مدير النظام لا يمكنه الوصول للبيانات")
        separation_working = False
    
    return separation_working

def generate_final_report():
    """إنشاء التقرير النهائي"""
    print("\n📋 تقرير إصلاح التحليل التفصيلي")
    print("="*60)
    
    # اختبار دوال API
    api_results = test_detailed_analysis_apis()
    
    # تحليل النتائج
    summary = analyze_test_results(api_results)
    
    # اختبار فصل البيانات
    separation_working = test_data_separation_in_apis()
    
    print("\n" + "="*60)
    print("🎯 النتيجة النهائية:")
    
    if summary['success_rate'] >= 80 and separation_working:
        print("🎉 تم إصلاح التحليل التفصيلي بنجاح!")
        print("✅ جميع دوال API تعمل بشكل صحيح")
        print("✅ فصل البيانات يعمل بشكل مثالي")
        print("✅ النظام جاهز للاستخدام")
        
        print("\n📋 التوصيات:")
        print("• يمكن الآن استخدام جميع الرسوم البيانية والتحليلات")
        print("• كل مستخدم يرى بيانات شركته فقط")
        print("• مدير النظام يرى جميع البيانات")
        
    elif summary['success_rate'] >= 60:
        print("⚠️ تم إصلاح معظم مشاكل التحليل التفصيلي")
        print(f"✅ {summary['successful_tests']}/{summary['total_tests']} دالة تعمل")
        print("⚠️ قد تحتاج بعض الدوال إلى مراجعة")
        
    else:
        print("❌ لا تزال هناك مشاكل في التحليل التفصيلي")
        print("يرجى مراجعة الأخطاء أعلاه")
    
    print("\n📊 إحصائيات التحليل:")
    print(f"   📈 إجمالي دوال API: {summary['total_tests']}")
    print(f"   ✅ الدوال العاملة: {summary['successful_tests']}")
    print(f"   📊 الدوال التي تحتوي على بيانات: {summary['tests_with_data']}")
    print(f"   📊 نسبة النجاح: {summary['success_rate']:.1f}%")
    print(f"   🔒 فصل البيانات: {'يعمل' if separation_working else 'لا يعمل'}")
    
    print("="*60)
    
    return summary['success_rate'] >= 80 and separation_working

def main():
    """الدالة الرئيسية"""
    print("🔧 اختبار إصلاح التحليل التفصيلي")
    print("="*60)
    
    success = generate_final_report()
    
    if success:
        print("\n🎯 التحليل التفصيلي يعمل بشكل مثالي!")
    else:
        print("\n⚠️ التحليل التفصيلي يحتاج إلى مراجعة")

if __name__ == '__main__':
    main()
