from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
from datetime import date, datetime


class SystemSettings(models.Model):
    """إعدادات النظام العامة"""

    # إعدادات السنة المالية
    fiscal_year_start_month = models.IntegerField(
        default=7,  # يوليو
        validators=[MinValueValidator(1), MaxValueValidator(12)],
        verbose_name="شهر بداية السنة المالية"
    )
    fiscal_year_start_day = models.IntegerField(
        default=1,
        validators=[MinValueValidator(1), MaxValueValidator(31)],
        verbose_name="يوم بداية السنة المالية"
    )
    fiscal_year_end_month = models.IntegerField(
        default=6,  # يونيو
        validators=[MinValueValidator(1), MaxValueValidator(12)],
        verbose_name="شهر نهاية السنة المالية"
    )
    fiscal_year_end_day = models.IntegerField(
        default=30,
        validators=[MinValueValidator(1), MaxValueValidator(31)],
        verbose_name="يوم نهاية السنة المالية"
    )

    # إعدادات عامة أخرى
    system_name = models.CharField(
        max_length=200,
        default="نظام الإحصائيات الشهرية",
        verbose_name="اسم النظام"
    )
    default_currency = models.CharField(
        max_length=10,
        default="ريال",
        verbose_name="العملة الافتراضية"
    )
    reports_decimal_places = models.IntegerField(
        default=2,
        validators=[MinValueValidator(0), MaxValueValidator(6)],
        verbose_name="عدد المنازل العشرية في التقارير"
    )

    # إعدادات التقارير
    default_report_year = models.IntegerField(
        null=True, blank=True,
        verbose_name="السنة الافتراضية للتقارير"
    )
    show_fiscal_year_in_reports = models.BooleanField(
        default=True,
        verbose_name="عرض السنة المالية في التقارير"
    )

    # معلومات التحديث
    updated_by = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True,
        verbose_name="محدث بواسطة"
    )
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")

    class Meta:
        verbose_name = "إعدادات النظام"
        verbose_name_plural = "إعدادات النظام"

    def __str__(self):
        return f"إعدادات النظام - السنة المالية: {self.fiscal_year_start_day}/{self.fiscal_year_start_month} - {self.fiscal_year_end_day}/{self.fiscal_year_end_month}"

    @classmethod
    def get_settings(cls):
        """الحصول على إعدادات النظام (إنشاء افتراضية إذا لم توجد)"""
        settings, created = cls.objects.get_or_create(
            id=1,
            defaults={
                'fiscal_year_start_month': 7,
                'fiscal_year_start_day': 1,
                'fiscal_year_end_month': 6,
                'fiscal_year_end_day': 30,
            }
        )
        return settings

    def get_current_fiscal_year(self):
        """الحصول على السنة المالية الحالية"""
        today = date.today()

        # إذا كنا بعد بداية السنة المالية في نفس السنة
        fiscal_start_this_year = date(today.year, self.fiscal_year_start_month, self.fiscal_year_start_day)

        if today >= fiscal_start_this_year:
            # السنة المالية الحالية تبدأ هذا العام
            return today.year
        else:
            # السنة المالية الحالية بدأت العام الماضي
            return today.year - 1

    def get_fiscal_year_start_date(self, fiscal_year):
        """الحصول على تاريخ بداية السنة المالية"""
        return date(fiscal_year, self.fiscal_year_start_month, self.fiscal_year_start_day)

    def get_fiscal_year_end_date(self, fiscal_year):
        """الحصول على تاريخ نهاية السنة المالية"""
        return date(fiscal_year + 1, self.fiscal_year_end_month, self.fiscal_year_end_day)

    def get_fiscal_year_months(self, fiscal_year):
        """الحصول على أشهر السنة المالية مرتبة"""
        start_date = self.get_fiscal_year_start_date(fiscal_year)
        end_date = self.get_fiscal_year_end_date(fiscal_year)

        months = []
        current_date = start_date.replace(day=1)

        while current_date <= end_date:
            months.append({
                'month': current_date.month,
                'year': current_date.year,
                'name': self.get_month_name(current_date.month),
                'display': f"{self.get_month_name(current_date.month)} {current_date.year}"
            })

            # الانتقال للشهر التالي
            if current_date.month == 12:
                current_date = current_date.replace(year=current_date.year + 1, month=1)
            else:
                current_date = current_date.replace(month=current_date.month + 1)

        return months

    def get_month_name(self, month_number):
        """الحصول على اسم الشهر"""
        months = {
            1: 'يناير', 2: 'فبراير', 3: 'مارس', 4: 'أبريل',
            5: 'مايو', 6: 'يونيو', 7: 'يوليو', 8: 'أغسطس',
            9: 'سبتمبر', 10: 'أكتوبر', 11: 'نوفمبر', 12: 'ديسمبر'
        }
        return months.get(month_number, f'شهر {month_number}')

    def is_date_in_fiscal_year(self, check_date, fiscal_year):
        """التحقق من أن التاريخ ضمن السنة المالية المحددة"""
        start_date = self.get_fiscal_year_start_date(fiscal_year)
        end_date = self.get_fiscal_year_end_date(fiscal_year)
        return start_date <= check_date <= end_date


class Company(models.Model):
    """نموذج الشركة"""
    name = models.CharField(max_length=200, verbose_name="اسم الشركة")
    sector = models.CharField(max_length=100, blank=True, null=True, verbose_name="القطاع")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "شركة"
        verbose_name_plural = "الشركات"
        ordering = ['name']

    def __str__(self):
        return self.name


class Item(models.Model):
    """نموذج العنصر"""
    name = models.CharField(max_length=200, verbose_name="اسم العنصر")
    unit = models.CharField(max_length=50, verbose_name="وحدة القياس")
    description = models.TextField(blank=True, null=True, verbose_name="الوصف")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "عنصر"
        verbose_name_plural = "العناصر"
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.unit})"


class Target(models.Model):
    """نموذج المستهدفات الشهرية"""
    MONTHS = [
        (1, 'يناير'), (2, 'فبراير'), (3, 'مارس'), (4, 'أبريل'),
        (5, 'مايو'), (6, 'يونيو'), (7, 'يوليو'), (8, 'أغسطس'),
        (9, 'سبتمبر'), (10, 'أكتوبر'), (11, 'نوفمبر'), (12, 'ديسمبر')
    ]

    DATA_TYPES = [
        ('capacity', 'الطاقة الإنتاجية المتاحة'),
        ('production', 'الإنتاج'),
        ('inventory', 'المخزون'),
        ('sales', 'البيع'),
    ]

    company = models.ForeignKey(Company, on_delete=models.CASCADE, verbose_name="الشركة")
    item = models.ForeignKey(Item, on_delete=models.CASCADE, verbose_name="العنصر")
    data_type = models.CharField(max_length=20, choices=DATA_TYPES, verbose_name="نوع البيانات")
    month = models.IntegerField(choices=MONTHS, verbose_name="الشهر")
    year = models.IntegerField(verbose_name="السنة")
    fiscal_year = models.IntegerField(null=True, blank=True, verbose_name="السنة المالية")
    target_quantity = models.DecimalField(
        max_digits=15, decimal_places=2,
        validators=[MinValueValidator(0)],
        verbose_name="الكمية المستهدفة"
    )
    target_value = models.DecimalField(
        max_digits=15, decimal_places=2,
        validators=[MinValueValidator(0)],
        verbose_name="القيمة المستهدفة"
    )
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, verbose_name="أنشئ بواسطة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "مستهدف"
        verbose_name_plural = "المستهدفات"
        unique_together = ['company', 'item', 'data_type', 'month', 'year']
        ordering = ['-year', '-month', 'company__name', 'item__name', 'data_type']

    def save(self, *args, **kwargs):
        """حفظ مع تحديد السنة المالية تلقائياً"""
        if not self.fiscal_year:
            settings = SystemSettings.get_settings()
            target_date = date(self.year, self.month, 1)

            # تحديد السنة المالية بناءً على التاريخ
            if self.month >= settings.fiscal_year_start_month:
                self.fiscal_year = self.year
            else:
                self.fiscal_year = self.year - 1

        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.company.name} - {self.item.name} - {self.get_data_type_display()} - {self.get_month_display()} {self.year}"

    def get_month_name(self):
        return dict(self.MONTHS)[self.month]

    def get_data_type_name(self):
        return dict(self.DATA_TYPES)[self.data_type]


class StatisticalEntry(models.Model):
    """نموذج الإدخال الإحصائي الشهري"""
    DATA_TYPES = [
        ('capacity', 'الطاقة الإنتاجية المتاحة'),
        ('production', 'الإنتاج'),
        ('inventory', 'المخزون'),
        ('sales', 'البيع'),
    ]

    company = models.ForeignKey(Company, on_delete=models.CASCADE, verbose_name="الشركة")
    item = models.ForeignKey(Item, on_delete=models.CASCADE, verbose_name="العنصر")
    data_type = models.CharField(max_length=20, choices=DATA_TYPES, verbose_name="نوع البيانات")
    date = models.DateField(verbose_name="تاريخ الإدخال")
    fiscal_year = models.IntegerField(null=True, blank=True, verbose_name="السنة المالية")
    quantity = models.DecimalField(
        max_digits=15, decimal_places=2,
        validators=[MinValueValidator(0)],
        verbose_name="الكمية المحققة"
    )
    value = models.DecimalField(
        max_digits=15, decimal_places=2,
        validators=[MinValueValidator(0)],
        verbose_name="القيمة المحققة"
    )
    notes = models.TextField(blank=True, null=True, verbose_name="ملاحظات")
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, verbose_name="أدخل بواسطة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإدخال")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "إدخال إحصائي"
        verbose_name_plural = "الإدخالات الإحصائية"
        unique_together = ['company', 'item', 'data_type', 'date']
        ordering = ['-date', 'company__name', 'item__name', 'data_type']

    def save(self, *args, **kwargs):
        """حفظ مع تحديد السنة المالية تلقائياً"""
        if not self.fiscal_year:
            settings = SystemSettings.get_settings()

            # تحديد السنة المالية بناءً على التاريخ
            if self.date.month >= settings.fiscal_year_start_month:
                self.fiscal_year = self.date.year
            else:
                self.fiscal_year = self.date.year - 1

        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.company.name} - {self.item.name} - {self.get_data_type_display()} - {self.date}"

    def get_data_type_name(self):
        return dict(self.DATA_TYPES)[self.data_type]

    @property
    def month(self):
        """استخراج الشهر من التاريخ"""
        return self.date.month

    @property
    def year(self):
        """استخراج السنة من التاريخ"""
        return self.date.year

    def get_target(self):
        """الحصول على المستهدف المقابل لهذا الإدخال"""
        try:
            return Target.objects.get(
                company=self.company,
                item=self.item,
                data_type=self.data_type,
                month=self.month,
                year=self.year
            )
        except Target.DoesNotExist:
            return None

    def get_quantity_achievement_percentage(self):
        """حساب نسبة إنجاز الكمية"""
        target = self.get_target()
        if target and target.target_quantity > 0:
            return round((self.quantity / target.target_quantity) * 100, 2)
        return 0

    def get_value_achievement_percentage(self):
        """حساب نسبة إنجاز القيمة"""
        target = self.get_target()
        if target and target.target_value > 0:
            return round((self.value / target.target_value) * 100, 2)
        return 0


class UserProfile(models.Model):
    """ملف المستخدم الموسع"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, verbose_name="المستخدم")
    company = models.ForeignKey(
        Company, on_delete=models.SET_NULL, null=True, blank=True,
        verbose_name="الشركة المرتبطة"
    )
    phone = models.CharField(max_length=20, blank=True, null=True, verbose_name="رقم الهاتف")
    is_company_user = models.BooleanField(default=False, verbose_name="مستخدم شركة")
    is_system_admin = models.BooleanField(default=False, verbose_name="مدير النظام")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "ملف المستخدم"
        verbose_name_plural = "ملفات المستخدمين"

    def __str__(self):
        return f"{self.user.username} - {self.company.name if self.company else 'بدون شركة'}"

    def can_access_company_data(self, company):
        """التحقق من إمكانية الوصول لبيانات شركة معينة"""
        if self.is_system_admin:
            return True
        if self.is_company_user and self.company == company:
            return True
        return False
