from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator
from django.utils import timezone
from datetime import date


class Company(models.Model):
    """نموذج الشركة"""
    name = models.Char<PERSON>ield(max_length=200, verbose_name="اسم الشركة")
    sector = models.CharField(max_length=100, blank=True, null=True, verbose_name="القطاع")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "شركة"
        verbose_name_plural = "الشركات"
        ordering = ['name']

    def __str__(self):
        return self.name


class Item(models.Model):
    """نموذج العنصر"""
    name = models.Char<PERSON><PERSON>(max_length=200, verbose_name="اسم العنصر")
    unit = models.Char<PERSON>ield(max_length=50, verbose_name="وحدة القياس")
    description = models.TextField(blank=True, null=True, verbose_name="الوصف")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "عنصر"
        verbose_name_plural = "العناصر"
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.unit})"


class Target(models.Model):
    """نموذج المستهدفات الشهرية"""
    MONTHS = [
        (1, 'يناير'), (2, 'فبراير'), (3, 'مارس'), (4, 'أبريل'),
        (5, 'مايو'), (6, 'يونيو'), (7, 'يوليو'), (8, 'أغسطس'),
        (9, 'سبتمبر'), (10, 'أكتوبر'), (11, 'نوفمبر'), (12, 'ديسمبر')
    ]

    DATA_TYPES = [
        ('capacity', 'الطاقة الإنتاجية المتاحة'),
        ('production', 'الإنتاج'),
        ('inventory', 'المخزون'),
        ('sales', 'البيع'),
    ]

    company = models.ForeignKey(Company, on_delete=models.CASCADE, verbose_name="الشركة")
    item = models.ForeignKey(Item, on_delete=models.CASCADE, verbose_name="العنصر")
    data_type = models.CharField(max_length=20, choices=DATA_TYPES, verbose_name="نوع البيانات")
    month = models.IntegerField(choices=MONTHS, verbose_name="الشهر")
    year = models.IntegerField(verbose_name="السنة")
    target_quantity = models.DecimalField(
        max_digits=15, decimal_places=2,
        validators=[MinValueValidator(0)],
        verbose_name="الكمية المستهدفة"
    )
    target_value = models.DecimalField(
        max_digits=15, decimal_places=2,
        validators=[MinValueValidator(0)],
        verbose_name="القيمة المستهدفة"
    )
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, verbose_name="أنشئ بواسطة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "مستهدف"
        verbose_name_plural = "المستهدفات"
        unique_together = ['company', 'item', 'data_type', 'month', 'year']
        ordering = ['-year', '-month', 'company__name', 'item__name', 'data_type']

    def __str__(self):
        return f"{self.company.name} - {self.item.name} - {self.get_data_type_display()} - {self.get_month_display()} {self.year}"

    def get_month_name(self):
        return dict(self.MONTHS)[self.month]

    def get_data_type_name(self):
        return dict(self.DATA_TYPES)[self.data_type]


class StatisticalEntry(models.Model):
    """نموذج الإدخال الإحصائي الشهري"""
    DATA_TYPES = [
        ('capacity', 'الطاقة الإنتاجية المتاحة'),
        ('production', 'الإنتاج'),
        ('inventory', 'المخزون'),
        ('sales', 'البيع'),
    ]

    company = models.ForeignKey(Company, on_delete=models.CASCADE, verbose_name="الشركة")
    item = models.ForeignKey(Item, on_delete=models.CASCADE, verbose_name="العنصر")
    data_type = models.CharField(max_length=20, choices=DATA_TYPES, verbose_name="نوع البيانات")
    date = models.DateField(verbose_name="تاريخ الإدخال")
    quantity = models.DecimalField(
        max_digits=15, decimal_places=2,
        validators=[MinValueValidator(0)],
        verbose_name="الكمية المحققة"
    )
    value = models.DecimalField(
        max_digits=15, decimal_places=2,
        validators=[MinValueValidator(0)],
        verbose_name="القيمة المحققة"
    )
    notes = models.TextField(blank=True, null=True, verbose_name="ملاحظات")
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, verbose_name="أدخل بواسطة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإدخال")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "إدخال إحصائي"
        verbose_name_plural = "الإدخالات الإحصائية"
        unique_together = ['company', 'item', 'data_type', 'date']
        ordering = ['-date', 'company__name', 'item__name', 'data_type']

    def __str__(self):
        return f"{self.company.name} - {self.item.name} - {self.get_data_type_display()} - {self.date}"

    def get_data_type_name(self):
        return dict(self.DATA_TYPES)[self.data_type]

    @property
    def month(self):
        """استخراج الشهر من التاريخ"""
        return self.date.month

    @property
    def year(self):
        """استخراج السنة من التاريخ"""
        return self.date.year

    def get_target(self):
        """الحصول على المستهدف المقابل لهذا الإدخال"""
        try:
            return Target.objects.get(
                company=self.company,
                item=self.item,
                data_type=self.data_type,
                month=self.month,
                year=self.year
            )
        except Target.DoesNotExist:
            return None

    def get_quantity_achievement_percentage(self):
        """حساب نسبة إنجاز الكمية"""
        target = self.get_target()
        if target and target.target_quantity > 0:
            return round((self.quantity / target.target_quantity) * 100, 2)
        return 0

    def get_value_achievement_percentage(self):
        """حساب نسبة إنجاز القيمة"""
        target = self.get_target()
        if target and target.target_value > 0:
            return round((self.value / target.target_value) * 100, 2)
        return 0


class UserProfile(models.Model):
    """ملف المستخدم الموسع"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, verbose_name="المستخدم")
    company = models.ForeignKey(
        Company, on_delete=models.SET_NULL, null=True, blank=True,
        verbose_name="الشركة المرتبطة"
    )
    phone = models.CharField(max_length=20, blank=True, null=True, verbose_name="رقم الهاتف")
    is_company_user = models.BooleanField(default=False, verbose_name="مستخدم شركة")
    is_system_admin = models.BooleanField(default=False, verbose_name="مدير النظام")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "ملف المستخدم"
        verbose_name_plural = "ملفات المستخدمين"

    def __str__(self):
        return f"{self.user.username} - {self.company.name if self.company else 'بدون شركة'}"

    def can_access_company_data(self, company):
        """التحقق من إمكانية الوصول لبيانات شركة معينة"""
        if self.is_system_admin:
            return True
        if self.is_company_user and self.company == company:
            return True
        return False
