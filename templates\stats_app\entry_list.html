{% extends 'base.html' %}

{% block title %}قائمة الإدخالات الإحصائية - نظام الإحصائيات الشهرية{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3">
                    <i class="fas fa-edit me-2"></i>
                    قائمة الإدخالات الإحصائية
                </h1>
                <a href="{% url 'stats_app:entry_create' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    إضافة إدخال جديد
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    {% if entries %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>الشركة</th>
                                        <th>العنصر</th>
                                        <th>نوع البيانات</th>
                                        <th>التاريخ</th>
                                        <th>الكمية المحققة</th>
                                        <th>القيمة المحققة</th>
                                        <th>نسبة إنجاز الكمية</th>
                                        <th>نسبة إنجاز القيمة</th>
                                        <th>أدخل بواسطة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for entry in entries %}
                                    <tr>
                                        <td>
                                            <strong>{{ entry.company.name }}</strong>
                                        </td>
                                        <td>{{ entry.item.name }}</td>
                                        <td>
                                            <span class="badge
                                                {% if entry.data_type == 'capacity' %}bg-primary
                                                {% elif entry.data_type == 'production' %}bg-success
                                                {% elif entry.data_type == 'inventory' %}bg-warning
                                                {% elif entry.data_type == 'sales' %}bg-danger
                                                {% endif %}">
                                                {{ entry.get_data_type_display }}
                                            </span>
                                        </td>
                                        <td>{{ entry.date }}</td>
                                        <td>{{ entry.quantity|floatformat:2 }} {{ entry.item.unit }}</td>
                                        <td>{{ entry.value|floatformat:2 }}</td>
                                        <td>
                                            {% with percentage=entry.get_quantity_achievement_percentage %}
                                                {% if percentage > 0 %}
                                                    <span class="badge {% if percentage >= 100 %}bg-success{% elif percentage >= 75 %}bg-warning{% else %}bg-danger{% endif %}">
                                                        {{ percentage }}%
                                                    </span>
                                                {% else %}
                                                    <span class="badge bg-secondary">لا يوجد مستهدف</span>
                                                {% endif %}
                                            {% endwith %}
                                        </td>
                                        <td>
                                            {% with percentage=entry.get_value_achievement_percentage %}
                                                {% if percentage > 0 %}
                                                    <span class="badge {% if percentage >= 100 %}bg-success{% elif percentage >= 75 %}bg-warning{% else %}bg-danger{% endif %}">
                                                        {{ percentage }}%
                                                    </span>
                                                {% else %}
                                                    <span class="badge bg-secondary">لا يوجد مستهدف</span>
                                                {% endif %}
                                            {% endwith %}
                                        </td>
                                        <td>
                                            {% if entry.created_by %}
                                                {{ entry.created_by.get_full_name|default:entry.created_by.username }}
                                            {% else %}
                                                <span class="text-muted">غير محدد</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{% url 'stats_app:entry_update' entry.pk %}" 
                                                   class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="{% url 'stats_app:entry_delete' entry.pk %}" 
                                                   class="btn btn-sm btn-outline-danger">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        {% if is_paginated %}
                            <nav aria-label="Page navigation">
                                <ul class="pagination justify-content-center">
                                    {% if page_obj.has_previous %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page=1">الأولى</a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}">السابقة</a>
                                        </li>
                                    {% endif %}

                                    <li class="page-item active">
                                        <span class="page-link">
                                            صفحة {{ page_obj.number }} من {{ page_obj.paginator.num_pages }}
                                        </span>
                                    </li>

                                    {% if page_obj.has_next %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.next_page_number }}">التالية</a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">الأخيرة</a>
                                        </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-edit fa-3x text-muted mb-3"></i>
                            <h4 class="text-muted">لا توجد إدخالات إحصائية مسجلة</h4>
                            <p class="text-muted">ابدأ بإضافة إدخال إحصائي جديد للنظام</p>
                            <a href="{% url 'stats_app:entry_create' %}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>
                                إضافة إدخال جديد
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
