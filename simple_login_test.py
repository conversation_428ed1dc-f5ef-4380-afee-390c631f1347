#!/usr/bin/env python
"""
اختبار بسيط لتسجيل الدخول
"""

import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'statistical_system.settings')
django.setup()

from django.contrib.auth.models import User
from stats_app.models import UserProfile
from django.contrib.auth import authenticate

def test_simple_login():
    """اختبار تسجيل الدخول البسيط"""
    print("🔐 اختبار تسجيل الدخول البسيط")
    print("="*50)
    
    # قائمة المستخدمين للاختبار
    test_users = [
        ("admin", "admin"),
        ("testuser", "test123456"),
        ("manager", "manager123"),
    ]
    
    print("📋 نتائج اختبار تسجيل الدخول:\n")
    
    for username, password in test_users:
        print(f"🧪 اختبار: {username}")
        
        # اختبار المصادقة
        user = authenticate(username=username, password=password)
        
        if user is not None:
            print(f"   ✅ تسجيل الدخول ناجح")
            
            # معلومات المستخدم
            print(f"   👤 الاسم: {user.first_name} {user.last_name}")
            print(f"   📧 البريد: {user.email}")
            print(f"   🔑 مدير عام: {'نعم' if user.is_superuser else 'لا'}")
            print(f"   🔑 موظف: {'نعم' if user.is_staff else 'لا'}")
            
            # معلومات الملف الشخصي
            try:
                profile = user.userprofile
                print(f"   📁 ملف شخصي: موجود")
                print(f"   🏢 مدير النظام: {'نعم' if profile.is_system_admin else 'لا'}")
                print(f"   🏭 مستخدم شركة: {'نعم' if profile.is_company_user else 'لا'}")
                if profile.company:
                    print(f"   🏢 الشركة: {profile.company.name}")
                else:
                    print(f"   🏢 الشركة: غير محدد")
            except UserProfile.DoesNotExist:
                print(f"   ❌ ملف شخصي: غير موجود")
            
        else:
            print(f"   ❌ فشل في تسجيل الدخول")
            
            # التحقق من سبب الفشل
            if User.objects.filter(username=username).exists():
                print(f"   🔍 السبب: كلمة مرور خاطئة")
            else:
                print(f"   🔍 السبب: المستخدم غير موجود")
        
        print()
    
    print("="*50)
    print("📝 ملاحظات:")
    print("• يمكنك استخدام أي من المستخدمين أعلاه لتسجيل الدخول")
    print("• تأكد من أن الخادم يعمل على: http://127.0.0.1:8000")
    print("• رابط تسجيل الدخول: http://127.0.0.1:8000/accounts/login/")
    print("="*50)

def show_all_users():
    """عرض جميع المستخدمين في النظام"""
    print("\n👥 جميع المستخدمين في النظام:")
    print("-" * 50)
    
    users = User.objects.all().order_by('username')
    
    for user in users:
        print(f"👤 {user.username}")
        print(f"   📧 {user.email or 'لا يوجد بريد'}")
        print(f"   📅 تاريخ الإنشاء: {user.date_joined.strftime('%Y-%m-%d %H:%M')}")
        print(f"   🔑 مدير عام: {'نعم' if user.is_superuser else 'لا'}")
        
        try:
            profile = user.userprofile
            print(f"   🏢 مدير النظام: {'نعم' if profile.is_system_admin else 'لا'}")
            print(f"   🏭 مستخدم شركة: {'نعم' if profile.is_company_user else 'لا'}")
            if profile.company:
                print(f"   🏢 الشركة: {profile.company.name}")
        except UserProfile.DoesNotExist:
            print(f"   ❌ لا يوجد ملف شخصي")
        
        print()

def main():
    """الدالة الرئيسية"""
    test_simple_login()
    show_all_users()

if __name__ == '__main__':
    main()
