#!/usr/bin/env python
"""
إدخال البيانات الشهرية لثلاث سنوات لشركة الأسمنت السعودية
"""

import os
import sys
import django
from datetime import date
import random

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'statistical_system.settings')
django.setup()

from stats_app.models import Company, Item, Target, StatisticalEntry
from django.contrib.auth.models import User

def create_saudi_cement_data():
    """إنشاء البيانات الشهرية لشركة الأسمنت السعودية"""
    print("🏭 إدخال البيانات الشهرية لشركة الأسمنت السعودية")
    print("="*60)
    
    # الحصول على الشركة
    try:
        saudi_cement = Company.objects.get(name="شركة الأسمنت السعودية")
        print(f"✅ تم العثور على الشركة: {saudi_cement.name}")
    except Company.DoesNotExist:
        print("❌ شركة الأسمنت السعودية غير موجودة")
        return False
    
    # الحصول على العناصر
    items = list(Item.objects.filter(is_active=True))
    if not items:
        print("❌ لا توجد عناصر نشطة")
        return False
    
    print(f"✅ تم العثور على {len(items)} عنصر")
    
    # أنواع البيانات
    data_types = ['capacity', 'production', 'inventory', 'sales']
    data_type_names = {
        'capacity': 'الطاقة الإنتاجية المتاحة',
        'production': 'الإنتاج',
        'inventory': 'المخزون',
        'sales': 'البيع'
    }
    
    # السنوات
    years = [2022, 2023, 2024]
    
    # إحصائيات
    total_targets = 0
    total_entries = 0
    
    for year in years:
        print(f"\n📅 إنشاء بيانات سنة {year}")
        print("-" * 30)
        
        for item in items:
            print(f"  📦 العنصر: {item.name}")
            
            for data_type in data_types:
                print(f"    📊 نوع البيانات: {data_type_names[data_type]}")
                
                # إنشاء المستهدفات الشهرية
                for month in range(1, 13):
                    # قيم أساسية حسب نوع البيانات
                    base_values = {
                        'capacity': random.randint(8000, 12000),
                        'production': random.randint(6000, 9000),
                        'inventory': random.randint(2000, 4000),
                        'sales': random.randint(5500, 8500)
                    }
                    
                    # إضافة تباين موسمي
                    seasonal_factor = 1.0
                    if month in [6, 7, 8]:  # الصيف - زيادة في الطلب
                        seasonal_factor = 1.15
                    elif month in [12, 1, 2]:  # الشتاء - انخفاض نسبي
                        seasonal_factor = 0.9
                    elif month in [3, 4, 5, 9, 10, 11]:  # الربيع والخريف
                        seasonal_factor = 1.05
                    
                    target_value = int(base_values[data_type] * seasonal_factor)
                    
                    # إنشاء المستهدف
                    target_quantity = target_value / random.uniform(50, 100)  # سعر وهمي
                    target, created = Target.objects.get_or_create(
                        company=saudi_cement,
                        item=item,
                        year=year,
                        month=month,
                        data_type=data_type,
                        defaults={
                            'target_value': target_value,
                            'target_quantity': target_quantity
                        }
                    )
                    
                    if created:
                        total_targets += 1
                        print(f"      ✅ مستهدف {month:2d}: {target_value:,}")
                    
                    # إنشاء البيانات الفعلية (مع تباين واقعي)
                    # نسبة الإنجاز تتراوح بين 85% - 115%
                    achievement_rate = random.uniform(0.85, 1.15)
                    actual_value = int(target_value * achievement_rate)
                    
                    # تاريخ عشوائي في الشهر
                    day = random.randint(1, 28)  # تجنب مشاكل الأيام في الأشهر المختلفة
                    entry_date = date(year, month, day)
                    
                    # إنشاء الإدخال
                    actual_quantity = actual_value / random.uniform(50, 100)  # سعر وهمي
                    entry, created = StatisticalEntry.objects.get_or_create(
                        company=saudi_cement,
                        item=item,
                        date=entry_date,
                        data_type=data_type,
                        defaults={
                            'value': actual_value,
                            'quantity': actual_quantity
                        }
                    )
                    
                    if created:
                        total_entries += 1
                        achievement_percent = (actual_value / target_value * 100)
                        print(f"      📈 فعلي {month:2d}: {actual_value:,} ({achievement_percent:.1f}%)")
    
    print(f"\n📊 ملخص البيانات المُدخلة:")
    print(f"   🎯 إجمالي المستهدفات: {total_targets}")
    print(f"   📈 إجمالي الإدخالات: {total_entries}")
    print(f"   📅 السنوات: {', '.join(map(str, years))}")
    print(f"   📦 العناصر: {len(items)}")
    print(f"   📊 أنواع البيانات: {len(data_types)}")
    
    return True

def create_additional_realistic_data():
    """إنشاء بيانات إضافية واقعية"""
    print("\n🔄 إنشاء بيانات إضافية واقعية")
    print("-" * 40)
    
    saudi_cement = Company.objects.get(name="شركة الأسمنت السعودية")
    items = list(Item.objects.filter(is_active=True))
    
    # إضافة بيانات متنوعة لعام 2024 (البيانات الحالية)
    current_year = 2024
    data_types = ['capacity', 'production', 'inventory', 'sales']
    
    additional_entries = 0
    
    for month in range(1, 13):
        for item in items[:2]:  # أول عنصرين فقط
            for data_type in data_types:
                # إضافة عدة إدخالات في الشهر الواحد
                for week in range(1, 5):  # 4 أسابيع
                    day = min(week * 7, 28)
                    entry_date = date(current_year, month, day)
                    
                    # قيم متنوعة
                    base_value = random.randint(1500, 3000)
                    
                    quantity_value = base_value / random.uniform(50, 100)
                    entry, created = StatisticalEntry.objects.get_or_create(
                        company=saudi_cement,
                        item=item,
                        date=entry_date,
                        data_type=data_type,
                        defaults={
                            'value': base_value,
                            'quantity': quantity_value
                        }
                    )
                    
                    if created:
                        additional_entries += 1
    
    print(f"✅ تم إضافة {additional_entries} إدخال إضافي")

def verify_data():
    """التحقق من البيانات المُدخلة"""
    print("\n🔍 التحقق من البيانات المُدخلة")
    print("-" * 30)
    
    saudi_cement = Company.objects.get(name="شركة الأسمنت السعودية")
    
    # إحصائيات المستهدفات
    targets_by_year = {}
    entries_by_year = {}
    
    for year in [2022, 2023, 2024]:
        targets_count = Target.objects.filter(
            company=saudi_cement,
            year=year
        ).count()
        
        entries_count = StatisticalEntry.objects.filter(
            company=saudi_cement,
            date__year=year
        ).count()
        
        targets_by_year[year] = targets_count
        entries_by_year[year] = entries_count
        
        print(f"📅 سنة {year}:")
        print(f"   🎯 المستهدفات: {targets_count}")
        print(f"   📈 الإدخالات: {entries_count}")
    
    # إحصائيات حسب نوع البيانات
    print(f"\n📊 إحصائيات حسب نوع البيانات:")
    data_types = ['capacity', 'production', 'inventory', 'sales']
    data_type_names = {
        'capacity': 'الطاقة الإنتاجية',
        'production': 'الإنتاج',
        'inventory': 'المخزون',
        'sales': 'البيع'
    }
    
    for data_type in data_types:
        count = StatisticalEntry.objects.filter(
            company=saudi_cement,
            data_type=data_type
        ).count()
        print(f"   📊 {data_type_names[data_type]}: {count} إدخال")
    
    # إحصائيات حسب العناصر
    print(f"\n📦 إحصائيات حسب العناصر:")
    items = Item.objects.filter(is_active=True)
    for item in items:
        count = StatisticalEntry.objects.filter(
            company=saudi_cement,
            item=item
        ).count()
        print(f"   📦 {item.name}: {count} إدخال")
    
    return True

def show_sample_data():
    """عرض عينة من البيانات"""
    print("\n📋 عينة من البيانات المُدخلة")
    print("-" * 35)
    
    saudi_cement = Company.objects.get(name="شركة الأسمنت السعودية")
    
    # عرض آخر 10 إدخالات
    recent_entries = StatisticalEntry.objects.filter(
        company=saudi_cement
    ).order_by('-date')[:10]
    
    print("📈 آخر 10 إدخالات:")
    for entry in recent_entries:
        print(f"   {entry.date} | {entry.item.name} | {entry.get_data_type_display()} | {entry.value:,}")
    
    # عرض مستهدفات 2024
    targets_2024 = Target.objects.filter(
        company=saudi_cement,
        year=2024
    ).order_by('month', 'item', 'data_type')[:10]
    
    print(f"\n🎯 عينة من مستهدفات 2024:")
    for target in targets_2024:
        month_name = target.get_month_display()
        print(f"   {month_name} | {target.item.name} | {target.get_data_type_display()} | {target.target_value:,}")

def main():
    """الدالة الرئيسية"""
    print("🏭 إدخال البيانات الشهرية لشركة الأسمنت السعودية")
    print("="*70)
    
    # إنشاء البيانات الأساسية
    success = create_saudi_cement_data()
    
    if success:
        # إنشاء بيانات إضافية
        create_additional_realistic_data()
        
        # التحقق من البيانات
        verify_data()
        
        # عرض عينة من البيانات
        show_sample_data()
        
        print("\n" + "="*70)
        print("🎉 تم إدخال البيانات بنجاح!")
        print("="*70)
        
        print("\n📊 ملخص البيانات:")
        print("   🏭 الشركة: شركة الأسمنت السعودية")
        print("   📅 السنوات: 2022, 2023, 2024")
        print("   📦 العناصر: جميع العناصر النشطة")
        print("   📊 أنواع البيانات: الطاقة الإنتاجية، الإنتاج، المخزون، البيع")
        print("   📈 البيانات: مستهدفات شهرية + إدخالات فعلية")
        
        print("\n🎯 للاستخدام:")
        print("   1. افتح: http://127.0.0.1:8000/accounts/login/")
        print("   2. سجل الدخول بـ: alaa1 / alaa1123")
        print("   3. اذهب إلى: http://127.0.0.1:8000/reports/")
        print("   4. اختر شركة الأسمنت السعودية من الفلاتر")
        print("   5. اعرض التقارير للسنوات المختلفة")
        
    else:
        print("\n❌ فشل في إدخال البيانات")
    
    print("="*70)

if __name__ == '__main__':
    main()
