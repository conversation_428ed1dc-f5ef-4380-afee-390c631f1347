{% extends 'base.html' %}
{% load static %}
{% load dashboard_extras %}

{% block title %}لوحة مؤشرات الأداء{% endblock %}

{% block extra_css %}
<style>
.dashboard-card {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.dashboard-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0,0,0,0.15);
}

.metric-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 20px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.metric-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255,255,255,0.1);
    transform: skewY(-5deg);
    transform-origin: top left;
}

.metric-value {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 5px;
    position: relative;
    z-index: 1;
}

.metric-label {
    font-size: 1rem;
    opacity: 0.9;
    position: relative;
    z-index: 1;
}

.metric-icon {
    position: absolute;
    top: 15px;
    right: 15px;
    font-size: 2rem;
    opacity: 0.3;
    z-index: 1;
}

.performance-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
}

.performance-excellent {
    background-color: #d4edda;
    color: #155724;
}

.performance-good {
    background-color: #d1ecf1;
    color: #0c5460;
}

.performance-average {
    background-color: #fff3cd;
    color: #856404;
}

.performance-poor {
    background-color: #f8d7da;
    color: #721c24;
}

.growth-indicator {
    display: inline-flex;
    align-items: center;
    padding: 6px 12px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.9rem;
}

.growth-positive {
    background-color: #d4edda;
    color: #155724;
}

.growth-negative {
    background-color: #f8d7da;
    color: #721c24;
}

.growth-stable {
    background-color: #e2e3e5;
    color: #495057;
}

.item-stats-card {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    background: #f8f9fa;
}

.data-type-mini-chart {
    display: flex;
    gap: 5px;
    margin-top: 10px;
}

.data-type-bar {
    height: 20px;
    border-radius: 3px;
    position: relative;
    min-width: 30px;
}

.progress-ring {
    width: 80px;
    height: 80px;
    margin: 0 auto;
}

.progress-ring-circle {
    stroke: #e9ecef;
    stroke-width: 8;
    fill: transparent;
    r: 30;
    cx: 40;
    cy: 40;
}

.progress-ring-progress {
    stroke: #28a745;
    stroke-width: 8;
    fill: transparent;
    r: 30;
    cx: 40;
    cy: 40;
    stroke-dasharray: 188.5;
    stroke-dashoffset: 188.5;
    transform: rotate(-90deg);
    transform-origin: 40px 40px;
    transition: stroke-dashoffset 0.5s ease;
}

.section-title {
    color: #495057;
    font-weight: 600;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #007bff;
    display: flex;
    align-items: center;
    gap: 10px;
}

.quick-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 30px;
}

.chart-container {
    height: 300px;
    position: relative;
}

.mini-chart {
    height: 150px;
}

@media (max-width: 768px) {
    .metric-value {
        font-size: 2rem;
    }

    .quick-stats {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-tachometer-alt me-2"></i>لوحة مؤشرات الأداء</h2>
                <div class="text-muted">
                    <i class="fas fa-calendar-alt me-2"></i>
                    السنة المالية: {{ current_fiscal_year }}
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="quick-stats">
        <div class="metric-card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
            <i class="fas fa-building metric-icon"></i>
            <div class="metric-value">{{ total_companies }}</div>
            <div class="metric-label">الشركات</div>
        </div>

        <div class="metric-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
            <i class="fas fa-boxes metric-icon"></i>
            <div class="metric-value">{{ total_items }}</div>
            <div class="metric-label">العناصر</div>
        </div>

        <div class="metric-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
            <i class="fas fa-bullseye metric-icon"></i>
            <div class="metric-value">{{ current_fiscal_year_targets }}</div>
            <div class="metric-label">المستهدفات</div>
        </div>

        <div class="metric-card" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);">
            <i class="fas fa-chart-line metric-icon"></i>
            <div class="metric-value">{{ current_fiscal_year_entries }}</div>
            <div class="metric-label">الإدخالات</div>
        </div>
    </div>

    <!-- Overall Performance -->
    <div class="row">
        <div class="col-lg-8">
            <div class="dashboard-card">
                <h5 class="section-title">
                    <i class="fas fa-chart-bar"></i>
                    الأداء الإجمالي للسنة المالية {{ current_fiscal_year }}
                </h5>

                <div class="row">
                    <div class="col-md-4 text-center">
                        <div class="progress-ring">
                            <svg class="progress-ring">
                                <circle class="progress-ring-circle"></circle>
                                <circle class="progress-ring-progress"
                                        style="stroke-dashoffset: {{ overall_achievement|percentage_to_stroke_offset }}"></circle>
                            </svg>
                        </div>
                        <h4 class="mt-2">{{ overall_achievement }}%</h4>
                        <p class="text-muted">نسبة الإنجاز الإجمالية</p>
                    </div>

                    <div class="col-md-8">
                        <div class="row">
                            <div class="col-6">
                                <h6>إجمالي المستهدف</h6>
                                <h4 class="text-primary">{{ total_target_value|format_number }}</h4>
                            </div>
                            <div class="col-6">
                                <h6>إجمالي المحقق</h6>
                                <h4 class="text-success">{{ total_actual_value|format_number }}</h4>
                            </div>
                        </div>

                        <div class="mt-3">
                            <h6>النمو مقارنة بالسنة السابقة</h6>
                            <div class="growth-indicator {{ growth_trend|growth_trend_class }}">
                                <i class="{{ growth_trend|growth_trend_icon }} me-2"></i>
                                {{ growth_rate }}%
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="dashboard-card">
                <h5 class="section-title">
                    <i class="fas fa-trophy"></i>
                    أفضل وأسوأ أداء
                </h5>

                <div class="mb-3">
                    <h6><i class="fas fa-medal text-warning me-2"></i>أفضل أداء</h6>
                    <div class="d-flex justify-content-between">
                        <span>{{ best_performance.name }}</span>
                        <span class="performance-badge performance-excellent">
                            {{ best_performance.achievement }}%
                        </span>
                    </div>
                </div>

                <div class="mb-3">
                    <h6><i class="fas fa-exclamation-triangle text-danger me-2"></i>يحتاج تحسين</h6>
                    <div class="d-flex justify-content-between">
                        <span>{{ worst_performance.name }}</span>
                        <span class="performance-badge performance-poor">
                            {{ worst_performance.achievement }}%
                        </span>
                    </div>
                </div>

                {% if latest_entry %}
                <div class="mt-4 pt-3 border-top">
                    <h6><i class="fas fa-clock me-2"></i>آخر إدخال</h6>
                    <small class="text-muted">
                        {{ latest_entry.company.name }} - {{ latest_entry.item.name }}<br>
                        {{ latest_entry.created_at|date:"d/m/Y H:i" }}
                    </small>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Data Types Performance -->
    <div class="row">
        <div class="col-12">
            <div class="dashboard-card">
                <h5 class="section-title">
                    <i class="fas fa-chart-pie"></i>
                    أداء أنواع البيانات
                </h5>

                <div class="row">
                    {% for data_type, performance in data_types_performance.items %}
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="item-stats-card">
                            <h6>{{ performance.name }}</h6>

                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="text-muted">الإنجاز</span>
                                <span class="performance-badge {{ performance.achievement|performance_class }}">
                                    {{ performance.achievement }}%
                                </span>
                            </div>

                            <div class="progress mb-2" style="height: 8px;">
                                <div class="progress-bar {{ performance.achievement|progress_color }}"
                                    style="width: {{ performance.achievement }}%"></div>
                            </div>

                            <small class="text-muted">
                                {{ performance.entries_count }} إدخال من {{ performance.targets_count }} مستهدف
                            </small>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <!-- Items Statistics -->
    <div class="row">
        <div class="col-12">
            <div class="dashboard-card">
                <h5 class="section-title">
                    <i class="fas fa-cubes"></i>
                    إحصائيات العناصر
                </h5>

                <div class="row">
                    {% for item_stat in items_statistics %}
                    <div class="col-lg-6 col-xl-4 mb-3">
                        <div class="item-stats-card">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h6 class="mb-0">{{ item_stat.item.name }}</h6>
                                <span class="performance-badge {{ item_stat.achievement|performance_class }}">
                                    {{ item_stat.achievement }}%
                                </span>
                            </div>

                            <div class="row text-center mb-2">
                                <div class="col-6">
                                    <small class="text-muted">المستهدف</small>
                                    <div class="fw-bold">{{ item_stat.target_total|format_number }}</div>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">المحقق</small>
                                    <div class="fw-bold">{{ item_stat.actual_total|format_number }}</div>
                                </div>
                            </div>

                            <div class="data-type-mini-chart">
                                {% for dt_key, dt_data in item_stat.data_types_breakdown.items %}
                                <div class="data-type-bar {{ dt_data.achievement|progress_color }}"
                                    style="flex: 1;"
                                    title="{{ dt_data.name }}: {{ dt_data.achievement }}%">
                                </div>
                                {% endfor %}
                            </div>

                            <small class="text-muted">
                                {{ item_stat.entries_count }} إدخال من {{ item_stat.targets_count }} مستهدف
                            </small>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row">
        <div class="col-12">
            <div class="dashboard-card">
                <h5 class="section-title">
                    <i class="fas fa-bolt"></i>
                    إجراءات سريعة
                </h5>

                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'stats_app:entry_create' %}" class="btn btn-success w-100">
                            <i class="fas fa-plus me-2"></i>
                            إدخال إحصائي جديد
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'stats_app:target_create' %}" class="btn btn-info w-100">
                            <i class="fas fa-bullseye me-2"></i>
                            مستهدف جديد
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'stats_app:yearly_comparison' %}" class="btn btn-warning w-100">
                            <i class="fas fa-chart-line me-2"></i>
                            المقارنة السنوية
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'stats_app:simple_reports' %}" class="btn btn-primary w-100">
                            <i class="fas fa-chart-bar me-2"></i>
                            التقارير البسيطة
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تحديث دوائر التقدم
    updateProgressRings();

    // تحديث كل 30 ثانية
    setInterval(function() {
        // يمكن إضافة تحديث تلقائي للبيانات هنا
    }, 30000);
});

function updateProgressRings() {
    const progressRings = document.querySelectorAll('.progress-ring-progress');

    progressRings.forEach(ring => {
        const percentage = parseFloat(ring.style.strokeDashoffset) || 0;
        // تحديث الرسوم المتحركة
        ring.style.transition = 'stroke-dashoffset 1s ease-in-out';
    });
}

// تحديث ألوان دوائر التقدم بناءً على النسبة
function updateProgressColor(percentage) {
    if (percentage >= 100) return '#28a745';
    if (percentage >= 80) return '#17a2b8';
    if (percentage >= 60) return '#ffc107';
    return '#dc3545';
}

// تأثيرات تفاعلية للكروت
document.querySelectorAll('.metric-card').forEach(card => {
    card.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-5px) scale(1.02)';
    });

    card.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0) scale(1)';
    });
});

// تحديث الوقت الحقيقي
function updateRealTimeData() {
    // يمكن إضافة استدعاءات AJAX هنا لتحديث البيانات
    console.log('تحديث البيانات...');
}

// تحديث كل دقيقة
setInterval(updateRealTimeData, 60000);
</script>
{% endblock %}
