{% extends 'base.html' %}

{% block title %}لوحة التحكم - نظام الإحصائيات الشهرية{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1 class="h3 mb-4">
                <i class="fas fa-tachometer-alt me-2"></i>
                لوحة التحكم
            </h1>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-uppercase mb-1">
                                إجمالي الشركات
                            </div>
                            <div class="stats-number">{{ total_companies }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-building fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-uppercase mb-1">
                                إجمالي العناصر
                            </div>
                            <div class="stats-number">{{ total_items }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-boxes fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-uppercase mb-1">
                                إجمالي الإدخالات
                            </div>
                            <div class="stats-number">{{ total_entries }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-edit fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-uppercase mb-1">
                                إدخالات هذا الشهر
                            </div>
                            <div class="stats-number">{{ current_month_entries }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الرسوم البيانية -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-line me-2"></i>
                        مقارنة الأداء الشهري
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="monthlyChart" height="100"></canvas>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-pie me-2"></i>
                        أداء الشركات
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="companyChart" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- أحدث الإدخالات -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-clock me-2"></i>
                        أحدث الإدخالات الإحصائية
                    </h5>
                    <a href="{% url 'stats_app:entry_list' %}" class="btn btn-sm btn-outline-primary">
                        عرض الكل
                    </a>
                </div>
                <div class="card-body">
                    {% if recent_entries %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>الشركة</th>
                                        <th>العنصر</th>
                                        <th>التاريخ</th>
                                        <th>الكمية</th>
                                        <th>القيمة</th>
                                        <th>أدخل بواسطة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for entry in recent_entries %}
                                    <tr>
                                        <td>{{ entry.company.name }}</td>
                                        <td>{{ entry.item.name }}</td>
                                        <td>{{ entry.date }}</td>
                                        <td>{{ entry.quantity }} {{ entry.item.unit }}</td>
                                        <td>{{ entry.value|floatformat:2 }}</td>
                                        <td>{{ entry.created_by.get_full_name|default:entry.created_by.username }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <p class="text-muted">لا توجد إدخالات إحصائية بعد</p>
                            <a href="{% url 'stats_app:entry_create' %}" class="btn btn-primary">
                                إضافة إدخال جديد
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- روابط سريعة -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bolt me-2"></i>
                        إجراءات سريعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="{% url 'stats_app:entry_create' %}" class="btn btn-success w-100">
                                <i class="fas fa-plus me-2"></i>
                                إدخال إحصائي جديد
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{% url 'stats_app:target_create' %}" class="btn btn-info w-100">
                                <i class="fas fa-bullseye me-2"></i>
                                مستهدف جديد
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{% url 'stats_app:comparison_report' %}" class="btn btn-warning w-100">
                                <i class="fas fa-balance-scale me-2"></i>
                                مقارنة الأداء
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{% url 'stats_app:charts' %}" class="btn btn-primary w-100">
                                <i class="fas fa-chart-pie me-2"></i>
                                الرسوم البيانية
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// رسم بياني للمقارنة الشهرية
fetch('{% url "stats_app:chart_data_api" %}?type=monthly_comparison')
    .then(response => response.json())
    .then(data => {
        const ctx = document.getElementById('monthlyChart').getContext('2d');
        new Chart(ctx, {
            type: 'line',
            data: data,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    });

// رسم بياني لأداء الشركات
fetch('{% url "stats_app:chart_data_api" %}?type=company_performance')
    .then(response => response.json())
    .then(data => {
        const ctx = document.getElementById('companyChart').getContext('2d');
        new Chart(ctx, {
            type: 'doughnut',
            data: data,
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });
    });
</script>
{% endblock %}
