{% extends 'base.html' %}

{% block title %}الرسوم البيانية - نظام الإحصائيات الشهرية{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3">
                    <i class="fas fa-chart-pie me-2"></i>
                    الرسوم البيانية التفاعلية
                </h1>
                <a href="{% url 'stats_app:reports' %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>
                    العودة للتقارير
                </a>
            </div>
        </div>
    </div>

    <!-- أزرار التحكم -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-primary active" onclick="showChart('monthly')">
                            <i class="fas fa-calendar me-2"></i>
                            المقارنة الشهرية
                        </button>
                        <button type="button" class="btn btn-outline-primary" onclick="showChart('company')">
                            <i class="fas fa-building me-2"></i>
                            أداء الشركات
                        </button>
                        <button type="button" class="btn btn-outline-primary" onclick="showChart('trend')">
                            <i class="fas fa-chart-line me-2"></i>
                            اتجاه الأداء
                        </button>
                        <button type="button" class="btn btn-outline-primary" onclick="showChart('items')">
                            <i class="fas fa-boxes me-2"></i>
                            أداء العناصر
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الرسوم البيانية -->
    <div class="row">
        <!-- الرسم البياني الرئيسي -->
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0" id="mainChartTitle">
                        <i class="fas fa-chart-line me-2"></i>
                        مقارنة الأداء الشهري
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="mainChart" height="100"></canvas>
                </div>
            </div>
        </div>

        <!-- الرسم البياني الثانوي -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0" id="secondaryChartTitle">
                        <i class="fas fa-chart-pie me-2"></i>
                        توزيع الأداء
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="secondaryChart" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات تفصيلية -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        إحصائيات تفصيلية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row" id="detailedStats">
                        <!-- سيتم ملؤها بواسطة JavaScript -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- معلومات إضافية -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-lightbulb me-2"></i>
                        نصائح لقراءة الرسوم البيانية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-chart-line text-primary me-2"></i>الرسوم الخطية</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>تظهر الاتجاهات عبر الزمن</li>
                                <li><i class="fas fa-check text-success me-2"></i>مفيدة لمقارنة الأداء الشهري</li>
                                <li><i class="fas fa-check text-success me-2"></i>تساعد في التنبؤ بالاتجاهات</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-chart-pie text-info me-2"></i>الرسوم الدائرية</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>تظهر النسب والتوزيعات</li>
                                <li><i class="fas fa-check text-success me-2"></i>مفيدة لمقارنة الشركات</li>
                                <li><i class="fas fa-check text-success me-2"></i>سهلة القراءة والفهم</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let mainChart, secondaryChart;

// تهيئة الرسوم البيانية
document.addEventListener('DOMContentLoaded', function() {
    showChart('monthly');
});

function showChart(type) {
    // تحديث الأزرار
    document.querySelectorAll('.btn-group .btn').forEach(btn => {
        btn.classList.remove('active');
        btn.classList.add('btn-outline-primary');
        btn.classList.remove('btn-primary');
    });
    event.target.classList.add('active', 'btn-primary');
    event.target.classList.remove('btn-outline-primary');

    switch(type) {
        case 'monthly':
            loadMonthlyChart();
            break;
        case 'company':
            loadCompanyChart();
            break;
        case 'trend':
            loadTrendChart();
            break;
        case 'items':
            loadItemsChart();
            break;
    }
}

function loadMonthlyChart() {
    document.getElementById('mainChartTitle').innerHTML = '<i class="fas fa-chart-line me-2"></i>مقارنة الأداء الشهري';
    document.getElementById('secondaryChartTitle').innerHTML = '<i class="fas fa-chart-pie me-2"></i>توزيع الأداء';

    fetch('{% url "stats_app:chart_data_api" %}?type=monthly_comparison')
        .then(response => response.json())
        .then(data => {
            if (mainChart) mainChart.destroy();
            
            const ctx = document.getElementById('mainChart').getContext('2d');
            mainChart = new Chart(ctx, {
                type: 'line',
                data: data,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'القيمة'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'الشهر'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top',
                        },
                        title: {
                            display: true,
                            text: 'مقارنة المستهدف مقابل المحقق شهرياً'
                        }
                    }
                }
            });
        });

    // رسم ثانوي للتوزيع
    loadPerformanceDistribution();
}

function loadCompanyChart() {
    document.getElementById('mainChartTitle').innerHTML = '<i class="fas fa-building me-2"></i>أداء الشركات';
    
    fetch('{% url "stats_app:chart_data_api" %}?type=company_performance')
        .then(response => response.json())
        .then(data => {
            if (mainChart) mainChart.destroy();
            
            const ctx = document.getElementById('mainChart').getContext('2d');
            mainChart = new Chart(ctx, {
                type: 'bar',
                data: data,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            title: {
                                display: true,
                                text: 'نسبة الإنجاز (%)'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        },
                        title: {
                            display: true,
                            text: 'نسبة إنجاز الشركات للشهر الحالي'
                        }
                    }
                }
            });
        });
}

function loadTrendChart() {
    document.getElementById('mainChartTitle').innerHTML = '<i class="fas fa-chart-line me-2"></i>اتجاه الأداء';
    
    // بيانات وهمية لاتجاه الأداء
    const trendData = {
        labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
        datasets: [{
            label: 'متوسط نسبة الإنجاز',
            data: [65, 72, 78, 85, 82, 88],
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            tension: 0.4
        }]
    };

    if (mainChart) mainChart.destroy();
    
    const ctx = document.getElementById('mainChart').getContext('2d');
    mainChart = new Chart(ctx, {
        type: 'line',
        data: trendData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100,
                    title: {
                        display: true,
                        text: 'نسبة الإنجاز (%)'
                    }
                }
            },
            plugins: {
                title: {
                    display: true,
                    text: 'اتجاه الأداء العام عبر الأشهر'
                }
            }
        }
    });
}

function loadItemsChart() {
    document.getElementById('mainChartTitle').innerHTML = '<i class="fas fa-boxes me-2"></i>أداء العناصر';
    
    // بيانات وهمية لأداء العناصر
    const itemsData = {
        labels: ['العنصر 1', 'العنصر 2', 'العنصر 3', 'العنصر 4', 'العنصر 5'],
        datasets: [{
            label: 'نسبة الإنجاز',
            data: [92, 78, 85, 67, 94],
            backgroundColor: [
                'rgba(255, 99, 132, 0.8)',
                'rgba(54, 162, 235, 0.8)',
                'rgba(255, 205, 86, 0.8)',
                'rgba(75, 192, 192, 0.8)',
                'rgba(153, 102, 255, 0.8)'
            ]
        }]
    };

    if (mainChart) mainChart.destroy();
    
    const ctx = document.getElementById('mainChart').getContext('2d');
    mainChart = new Chart(ctx, {
        type: 'doughnut',
        data: itemsData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: 'أداء العناصر المختلفة'
                }
            }
        }
    });
}

function loadPerformanceDistribution() {
    const distributionData = {
        labels: ['ممتاز (90%+)', 'جيد (70-89%)', 'متوسط (50-69%)', 'ضعيف (<50%)'],
        datasets: [{
            data: [25, 35, 30, 10],
            backgroundColor: [
                'rgba(40, 167, 69, 0.8)',
                'rgba(255, 193, 7, 0.8)',
                'rgba(255, 152, 0, 0.8)',
                'rgba(220, 53, 69, 0.8)'
            ]
        }]
    };

    if (secondaryChart) secondaryChart.destroy();
    
    const ctx = document.getElementById('secondaryChart').getContext('2d');
    secondaryChart = new Chart(ctx, {
        type: 'pie',
        data: distributionData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}
</script>
{% endblock %}
