from django import template

register = template.Library()

@register.filter
def multiply(value, arg):
    """ضرب قيمة في رقم آخر"""
    try:
        return float(value) * float(arg)
    except (ValueError, TypeError):
        return 0

@register.filter
def subtract(value, arg):
    """طرح رقم من قيمة"""
    try:
        return float(value) - float(arg)
    except (ValueError, TypeError):
        return 0

@register.filter
def percentage_to_stroke_offset(percentage):
    """تحويل النسبة المئوية إلى stroke-dashoffset للدائرة"""
    try:
        # محيط الدائرة = 2 * π * r = 2 * 3.14159 * 30 = 188.5
        circumference = 188.5
        # حساب المسافة المطلوبة
        offset = circumference - (float(percentage) / 100 * circumference)
        return offset
    except (ValueError, TypeError):
        return 188.5  # دائرة فارغة

@register.filter
def performance_class(percentage):
    """إرجاع CSS class بناءً على نسبة الأداء"""
    try:
        percentage = float(percentage)
        if percentage >= 100:
            return 'performance-excellent'
        elif percentage >= 80:
            return 'performance-good'
        elif percentage >= 60:
            return 'performance-average'
        else:
            return 'performance-poor'
    except (ValueError, TypeError):
        return 'performance-poor'

@register.filter
def progress_color(percentage):
    """إرجاع لون شريط التقدم بناءً على النسبة"""
    try:
        percentage = float(percentage)
        if percentage >= 100:
            return 'bg-success'
        elif percentage >= 80:
            return 'bg-info'
        elif percentage >= 60:
            return 'bg-warning'
        else:
            return 'bg-danger'
    except (ValueError, TypeError):
        return 'bg-danger'

@register.filter
def format_number(value):
    """تنسيق الأرقام بفواصل الآلاف"""
    try:
        return "{:,.0f}".format(float(value))
    except (ValueError, TypeError):
        return "0"

@register.filter
def growth_trend_class(trend):
    """إرجاع CSS class لاتجاه النمو"""
    if trend == 'positive':
        return 'growth-positive'
    elif trend == 'negative':
        return 'growth-negative'
    else:
        return 'growth-stable'

@register.filter
def growth_trend_icon(trend):
    """إرجاع أيقونة اتجاه النمو"""
    if trend == 'positive':
        return 'fas fa-arrow-up'
    elif trend == 'negative':
        return 'fas fa-arrow-down'
    else:
        return 'fas fa-minus'
