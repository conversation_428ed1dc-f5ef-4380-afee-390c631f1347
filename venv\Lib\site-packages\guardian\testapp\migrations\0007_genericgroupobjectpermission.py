# Generated by Django 5.1.4 on 2024-12-28 09:31

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
        ('contenttypes', '0002_remove_content_type_name'),
        ('testapp', '0006_auto_20230727_0658'),
    ]

    operations = [
        migrations.CreateModel(
            name='GenericGroupObjectPermission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('object_pk', models.CharField(max_length=255, verbose_name='object ID')),
                ('content_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype')),
                ('group', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='auth.group')),
                ('permission', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='auth.permission')),
            ],
            options={
                'abstract': False,
                'indexes': [models.Index(fields=['content_type', 'object_pk'], name='testapp_gen_content_75e2d2_idx')],
                'unique_together': {('group', 'permission', 'object_pk')},
            },
        ),
    ]
