#!/usr/bin/env python
"""
سكريبت شامل لإصلاح جميع المستخدمين وتوحيد كلمات المرور
"""

import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'statistical_system.settings')
django.setup()

from django.contrib.auth.models import User
from stats_app.models import UserProfile, Company
from django.contrib.auth import authenticate

def fix_all_users():
    """إصلاح جميع المستخدمين وتوحيد كلمات المرور"""
    print("🔧 إصلاح جميع المستخدمين")
    print("="*50)
    
    # قائمة المستخدمين المطلوبين مع كلمات المرور الموحدة
    required_users = [
        {
            'username': 'admin',
            'password': 'admin',
            'email': '<EMAIL>',
            'first_name': 'مدير',
            'last_name': 'عام',
            'is_superuser': True,
            'is_staff': True,
            'is_system_admin': True,
            'is_company_user': False,
            'company': None
        },
        {
            'username': 'alaa',
            'password': 'alaa123',
            'email': '<EMAIL>',
            'first_name': 'علاء',
            'last_name': 'المستخدم',
            'is_superuser': False,
            'is_staff': True,
            'is_system_admin': True,
            'is_company_user': False,
            'company': None
        },
        {
            'username': 'alaa1',
            'password': 'alaa1123',
            'email': '<EMAIL>',
            'first_name': 'علاء',
            'last_name': '1',
            'is_superuser': False,
            'is_staff': False,
            'is_system_admin': False,
            'is_company_user': True,
            'company': 'first'
        },
        {
            'username': 'alaa2',
            'password': 'alaa2123',
            'email': '<EMAIL>',
            'first_name': 'علاء',
            'last_name': '2',
            'is_superuser': False,
            'is_staff': False,
            'is_system_admin': False,
            'is_company_user': True,
            'company': 'first'
        },
        {
            'username': 'testuser',
            'password': 'test123456',
            'email': '<EMAIL>',
            'first_name': 'مستخدم',
            'last_name': 'تجريبي',
            'is_superuser': False,
            'is_staff': False,
            'is_system_admin': False,
            'is_company_user': True,
            'company': 'first'
        },
        {
            'username': 'manager',
            'password': 'manager123',
            'email': '<EMAIL>',
            'first_name': 'مدير',
            'last_name': 'النظام',
            'is_superuser': False,
            'is_staff': True,
            'is_system_admin': True,
            'is_company_user': False,
            'company': None
        }
    ]
    
    updated_count = 0
    created_count = 0
    
    for user_data in required_users:
        username = user_data['username']
        print(f"\n🔧 معالجة المستخدم: {username}")
        
        try:
            # محاولة الحصول على المستخدم
            user = User.objects.get(username=username)
            print(f"   ✅ المستخدم موجود")
            
            # تحديث كلمة المرور
            user.set_password(user_data['password'])
            user.email = user_data['email']
            user.first_name = user_data['first_name']
            user.last_name = user_data['last_name']
            user.is_superuser = user_data['is_superuser']
            user.is_staff = user_data['is_staff']
            user.save()
            
            print(f"   ✅ تم تحديث كلمة المرور: {user_data['password']}")
            updated_count += 1
            
        except User.DoesNotExist:
            # إنشاء المستخدم الجديد
            user = User.objects.create_user(
                username=username,
                password=user_data['password'],
                email=user_data['email'],
                first_name=user_data['first_name'],
                last_name=user_data['last_name']
            )
            user.is_superuser = user_data['is_superuser']
            user.is_staff = user_data['is_staff']
            user.save()
            
            print(f"   ✅ تم إنشاء المستخدم مع كلمة المرور: {user_data['password']}")
            created_count += 1
        
        # إصلاح الملف الشخصي
        try:
            profile = user.userprofile
        except UserProfile.DoesNotExist:
            profile = UserProfile.objects.create(user=user)
            print(f"   ✅ تم إنشاء ملف شخصي")
        
        # تحديث الملف الشخصي
        profile.is_system_admin = user_data['is_system_admin']
        profile.is_company_user = user_data['is_company_user']
        
        # تعيين الشركة
        if user_data['company'] == 'first' and Company.objects.exists():
            profile.company = Company.objects.first()
            print(f"   ✅ تم ربط المستخدم بالشركة: {profile.company.name}")
        elif user_data['company'] is None:
            profile.company = None
        
        profile.save()
        print(f"   ✅ تم تحديث الملف الشخصي")
    
    print(f"\n📊 ملخص العملية:")
    print(f"   تم تحديث: {updated_count} مستخدم")
    print(f"   تم إنشاء: {created_count} مستخدم")

def test_all_logins():
    """اختبار تسجيل الدخول لجميع المستخدمين"""
    print("\n🧪 اختبار تسجيل الدخول لجميع المستخدمين")
    print("="*50)
    
    test_users = [
        ('admin', 'admin'),
        ('alaa', 'alaa123'),
        ('alaa1', 'alaa1123'),
        ('alaa2', 'alaa2123'),
        ('testuser', 'test123456'),
        ('manager', 'manager123'),
    ]
    
    success_count = 0
    
    for username, password in test_users:
        print(f"\n🔐 اختبار: {username}")
        
        user = authenticate(username=username, password=password)
        
        if user:
            print(f"   ✅ تسجيل الدخول ناجح")
            print(f"   👤 الاسم: {user.first_name} {user.last_name}")
            print(f"   📧 البريد: {user.email}")
            
            try:
                profile = user.userprofile
                print(f"   🏢 مدير النظام: {'نعم' if profile.is_system_admin else 'لا'}")
                print(f"   🏭 مستخدم شركة: {'نعم' if profile.is_company_user else 'لا'}")
                if profile.company:
                    print(f"   🏢 الشركة: {profile.company.name}")
            except:
                print(f"   ⚠️ مشكلة في الملف الشخصي")
            
            success_count += 1
        else:
            print(f"   ❌ فشل في تسجيل الدخول")
    
    print(f"\n📊 النتيجة: {success_count}/{len(test_users)} مستخدم نجح في تسجيل الدخول")
    return success_count == len(test_users)

def show_login_info():
    """عرض معلومات تسجيل الدخول"""
    print("\n📋 معلومات تسجيل الدخول المحدثة")
    print("="*50)
    
    login_info = [
        ('admin', 'admin', 'مدير عام'),
        ('alaa', 'alaa123', 'مدير نظام'),
        ('alaa1', 'alaa1123', 'مستخدم شركة'),
        ('alaa2', 'alaa2123', 'مستخدم شركة'),
        ('testuser', 'test123456', 'مستخدم شركة'),
        ('manager', 'manager123', 'مدير نظام'),
    ]
    
    print("| المستخدم | كلمة المرور | النوع |")
    print("|---------|------------|-------|")
    
    for username, password, role in login_info:
        print(f"| {username:<8} | {password:<10} | {role} |")
    
    print(f"\n🌐 رابط تسجيل الدخول: http://127.0.0.1:8000/accounts/login/")

def main():
    """الدالة الرئيسية"""
    print("🔧 إصلاح شامل لجميع المستخدمين")
    print("="*60)
    
    # إصلاح جميع المستخدمين
    fix_all_users()
    
    # اختبار تسجيل الدخول
    all_success = test_all_logins()
    
    # عرض معلومات تسجيل الدخول
    show_login_info()
    
    print("\n" + "="*60)
    if all_success:
        print("🎉 تم إصلاح جميع المستخدمين بنجاح!")
        print("✅ يمكنك الآن تسجيل الدخول بأي من المستخدمين أعلاه")
    else:
        print("⚠️ بعض المستخدمين لا يزالون يواجهون مشاكل")
    print("="*60)

if __name__ == '__main__':
    main()
