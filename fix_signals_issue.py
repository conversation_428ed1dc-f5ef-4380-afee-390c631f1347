#!/usr/bin/env python
"""
سكريبت لحل مشكلة الإشارات المكررة وإصلاح UserProfile
"""

import os
import sys
import django
from datetime import datetime

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'statistical_system.settings')
django.setup()

from django.contrib.auth.models import User
from stats_app.models import UserProfile
from django.db import transaction

def fix_signals_issue():
    """إصلاح مشكلة الإشارات المكررة"""
    print("بدء إصلاح مشكلة الإشارات...")
    
    # إزالة جميع الإشارات المكررة
    from django.db.models.signals import post_save
    from stats_app.signals import create_user_profile, save_user_profile
    
    # قطع الاتصال بالإشارات الموجودة
    try:
        post_save.disconnect(create_user_profile, sender=User)
        post_save.disconnect(save_user_profile, sender=User)
        print("✓ تم قطع الاتصال بالإشارات القديمة")
    except:
        print("⚠️ لم توجد إشارات للقطع")
    
    # إعادة تسجيل الإشارات
    post_save.connect(create_user_profile, sender=User)
    post_save.connect(save_user_profile, sender=User)
    print("✓ تم إعادة تسجيل الإشارات")

def clean_duplicate_profiles():
    """تنظيف ملفات المستخدمين المكررة"""
    print("\nتنظيف الملفات المكررة...")
    
    users_with_duplicates = []
    
    for user in User.objects.all():
        profile_count = UserProfile.objects.filter(user=user).count()
        if profile_count > 1:
            users_with_duplicates.append(user)
            print(f"⚠️ المستخدم {user.username} لديه {profile_count} ملفات")
    
    if users_with_duplicates:
        with transaction.atomic():
            for user in users_with_duplicates:
                profiles = UserProfile.objects.filter(user=user).order_by('id')
                # الاحتفاظ بالملف الأول وحذف الباقي
                for profile in profiles[1:]:
                    profile.delete()
                    print(f"✓ تم حذف ملف مكرر للمستخدم {user.username}")
        
        print(f"✅ تم تنظيف {len(users_with_duplicates)} مستخدم")
    else:
        print("✅ لا توجد ملفات مكررة")

def ensure_all_users_have_profiles():
    """التأكد من أن جميع المستخدمين لديهم ملفات شخصية"""
    print("\nالتأكد من وجود ملفات شخصية لجميع المستخدمين...")
    
    users_without_profiles = []
    
    for user in User.objects.all():
        if not UserProfile.objects.filter(user=user).exists():
            users_without_profiles.append(user)
    
    if users_without_profiles:
        with transaction.atomic():
            for user in users_without_profiles:
                UserProfile.objects.create(user=user)
                print(f"✓ تم إنشاء ملف شخصي للمستخدم {user.username}")
        
        print(f"✅ تم إنشاء {len(users_without_profiles)} ملف شخصي")
    else:
        print("✅ جميع المستخدمين لديهم ملفات شخصية")

def test_user_creation():
    """اختبار إنشاء مستخدم جديد"""
    print("\nاختبار إنشاء مستخدم جديد...")
    
    # إنشاء مستخدم تجريبي
    test_username = f"test_user_{int(datetime.now().timestamp())}"
    
    try:
        with transaction.atomic():
            # إنشاء المستخدم
            test_user = User.objects.create_user(
                username=test_username,
                email=f"{test_username}@test.com",
                password="testpass123"
            )
            
            # التحقق من إنشاء UserProfile
            if UserProfile.objects.filter(user=test_user).exists():
                print(f"✅ تم إنشاء المستخدم {test_username} مع ملف شخصي بنجاح")
                
                # حذف المستخدم التجريبي
                test_user.delete()
                print(f"✓ تم حذف المستخدم التجريبي")
                
                return True
            else:
                print(f"❌ فشل في إنشاء ملف شخصي للمستخدم {test_username}")
                test_user.delete()
                return False
                
    except Exception as e:
        print(f"❌ خطأ في اختبار إنشاء المستخدم: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=== أداة إصلاح مشكلة الإشارات ===\n")
    
    # إصلاح الإشارات
    fix_signals_issue()
    
    # تنظيف الملفات المكررة
    clean_duplicate_profiles()
    
    # التأكد من وجود ملفات لجميع المستخدمين
    ensure_all_users_have_profiles()
    
    # اختبار إنشاء مستخدم جديد
    success = test_user_creation()
    
    print("\n" + "="*50)
    if success:
        print("✅ تم إصلاح جميع المشاكل بنجاح!")
        print("يمكنك الآن إضافة مستخدمين جدد بدون مشاكل.")
    else:
        print("❌ لا تزال هناك مشاكل تحتاج إلى حل يدوي.")
    
    print("=== انتهى الإصلاح ===")

if __name__ == '__main__':
    main()
