from django.urls import path
from . import views

app_name = 'stats_app'

urlpatterns = [
    # الصفحة الرئيسية
    path('', views.DashboardView.as_view(), name='dashboard'),

    # إدارة الشركات
    path('companies/', views.CompanyListView.as_view(), name='company_list'),
    path('companies/create/', views.CompanyCreateView.as_view(), name='company_create'),
    path('companies/<int:pk>/update/', views.CompanyUpdateView.as_view(), name='company_update'),
    path('companies/<int:pk>/delete/', views.CompanyDeleteView.as_view(), name='company_delete'),
    
    # إدارة العناصر
    path('items/', views.ItemListView.as_view(), name='item_list'),
    path('items/create/', views.ItemCreateView.as_view(), name='item_create'),
    path('items/<int:pk>/update/', views.ItemUpdateView.as_view(), name='item_update'),
    path('items/<int:pk>/delete/', views.ItemDeleteView.as_view(), name='item_delete'),
    
    # إدارة المستهدفات
    path('targets/', views.TargetListView.as_view(), name='target_list'),
    path('targets/create/', views.TargetCreateView.as_view(), name='target_create'),
    path('targets/<int:pk>/update/', views.TargetUpdateView.as_view(), name='target_update'),
    path('targets/<int:pk>/delete/', views.TargetDeleteView.as_view(), name='target_delete'),
    
    # إدارة الإدخالات الإحصائية
    path('entries/', views.StatisticalEntryListView.as_view(), name='entry_list'),
    path('entries/create/', views.StatisticalEntryCreateView.as_view(), name='entry_create'),
    path('entries/<int:pk>/update/', views.StatisticalEntryUpdateView.as_view(), name='entry_update'),
    path('entries/<int:pk>/delete/', views.StatisticalEntryDeleteView.as_view(), name='entry_delete'),
    
    # التقارير البسيطة
    path('reports/', views.SimpleReportsView.as_view(), name='simple_reports'),
    path('reports/data/', views.SimpleReportDataView.as_view(), name='simple_report_data'),

    # إعدادات النظام
    path('settings/', views.SystemSettingsView.as_view(), name='system_settings'),
]
