django_guardian-3.0.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
django_guardian-3.0.0.dist-info/METADATA,sha256=L2FEYLVW3K_pgwc6D_8caYWbBSqJQegsSrpvvYUEqkM,7660
django_guardian-3.0.0.dist-info/RECORD,,
django_guardian-3.0.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
django_guardian-3.0.0.dist-info/WHEEL,sha256=0CuiUZ_p9E4cD6NyLD6UG80LBXYyiSYZOKDm5lp32xk,91
django_guardian-3.0.0.dist-info/licenses/LICENSE,sha256=19hGtwYdfCGxH3eL0TgFDAKFzjQxDck7p5fcXm0pl4s,2600
django_guardian-3.0.0.dist-info/top_level.txt,sha256=F4LU_xsR5-v9VLhHaHZGoz04kZoTYPfLoQJojYkEipk,9
guardian/__init__.py,sha256=zPj21bkbzQCnu-5YMleiGBmkaptBBnjsWhG-lQvtRN8,2213
guardian/__pycache__/__init__.cpython-313.pyc,,
guardian/__pycache__/admin.cpython-313.pyc,,
guardian/__pycache__/apps.cpython-313.pyc,,
guardian/__pycache__/backends.cpython-313.pyc,,
guardian/__pycache__/checks.cpython-313.pyc,,
guardian/__pycache__/compat.cpython-313.pyc,,
guardian/__pycache__/core.cpython-313.pyc,,
guardian/__pycache__/ctypes.cpython-313.pyc,,
guardian/__pycache__/decorators.cpython-313.pyc,,
guardian/__pycache__/exceptions.cpython-313.pyc,,
guardian/__pycache__/forms.cpython-313.pyc,,
guardian/__pycache__/managers.cpython-313.pyc,,
guardian/__pycache__/mixins.cpython-313.pyc,,
guardian/__pycache__/shortcuts.cpython-313.pyc,,
guardian/__pycache__/utils.cpython-313.pyc,,
guardian/admin.py,sha256=i9GuBaXB3NUlAgnEnsdLgMVdbmsGimjTy9gOrNS3j7g,19082
guardian/apps.py,sha256=Ju664Rszc4xnl5derMA_nW1zrG9OFlef9EG4AGexn00,415
guardian/backends.py,sha256=lvzII6TqkJALi09Yhth_YN3ZSRo0255hBZK_2OZskEY,4910
guardian/checks.py,sha256=2kJYWi_AL0A45YyrgBio04Az2EKwEVRV5yvsQP2gqAU,827
guardian/compat.py,sha256=WnxdF_fNdtfsG3e4tG9a8AQ7DsDWqhmghzxGCKmHYW8,2057
guardian/conf/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
guardian/conf/__pycache__/__init__.cpython-313.pyc,,
guardian/conf/__pycache__/settings.cpython-313.pyc,,
guardian/conf/settings.py,sha256=GUQtSiOs6YIuZj5jIPDR_cPRl0wHY7bhDsDfnoaTmqs,2500
guardian/core.py,sha256=l3P8Nzmo8TuIu2YyV_YmMOl_w3uDy1VvsqW1_GiLNsw,11003
guardian/ctypes.py,sha256=BlDQx_7FS5eADmtn2KtUGwYL809t9VwjISdTWyvLduM,895
guardian/decorators.py,sha256=Au9zXyIq9xp2b5gt0e2WifM31gU_KoY7qodu6MPhQlM,7951
guardian/exceptions.py,sha256=Wda-4lqEJ41oAT5U8K3xzpV__DvN7qipXN7SZJJaIwY,854
guardian/forms.py,sha256=m3UoLluHQbD093VUWXDGF13yEEdE1IMGe0FlcO4hung,7838
guardian/locale/es/LC_MESSAGES/django.mo,sha256=YjzoX55B3RzneQf8gmwQmoWiCa_l2Dcm051YOhNHqCY,1688
guardian/locale/es/LC_MESSAGES/django.po,sha256=d1bB_Lcu84H6jidZcVKyGbMxrGVRnfZ17I5B5OkVRGE,6180
guardian/locale/fr/LC_MESSAGES/django.mo,sha256=iWyk0pWnJ9tHP7R0QKBWsz89TS93-yYfT-Rqc12cZG4,1811
guardian/locale/fr/LC_MESSAGES/django.po,sha256=AFvvtwFgGliwJKVgHrh5k_BVJ04twcY62b8BE6PxxKI,6203
guardian/locale/nl/LC_MESSAGES/django.mo,sha256=wWfXzw9ZwrTA3VgBrhShu0oSfyW1ZlHpq9doriI0HFA,1726
guardian/locale/nl/LC_MESSAGES/django.po,sha256=wya4k7ldUey9dPjk5diheLbG_fFYjsK7mnZVc474HTM,6270
guardian/locale/pl/LC_MESSAGES/django.mo,sha256=85M0uvXlZhqiqNaNlhfpHW4UrDhIe6jq8fKOaw2k458,1703
guardian/locale/pl/LC_MESSAGES/django.po,sha256=OiCugOGOqPreFeMN1HUcAMwoBfiQveOIUHiMXrqF9ww,6261
guardian/locale/pt_BR/LC_MESSAGES/django.mo,sha256=ZSocfWC--puVlvRlWhtzYb2OtflkT17P_4eTmfCeKVg,1792
guardian/locale/pt_BR/LC_MESSAGES/django.po,sha256=lU8h6kRvHkjDMx8NlraWOVJCMr2GBioCYNnOJKxcTBw,6248
guardian/locale/ru/LC_MESSAGES/django.mo,sha256=p4lVYWVEJdct-tYHDhU5ycnJQWp3TxX7mM9qbfKvjKc,2205
guardian/locale/ru/LC_MESSAGES/django.po,sha256=8-402HyRx8SInqDXLkZ5T3Mq4EziMlcgTaQWAHX7c7g,6564
guardian/locale/zh_Hans/LC_MESSAGES/django.mo,sha256=KN3Ge2RnU1uvXbRhUlwQqzOWpLefcUDb0KMxK7l27I4,1607
guardian/locale/zh_Hans/LC_MESSAGES/django.po,sha256=EKZO3GzmqJbD9Z8BHvj0FbkGPBfepbOOX7-ZJGXYRTs,6846
guardian/management/__init__.py,sha256=ChHCBnUPwC0I59tWVDk_zcMqT258owrELKLgWQgl5NE,1648
guardian/management/__pycache__/__init__.cpython-313.pyc,,
guardian/management/commands/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
guardian/management/commands/__pycache__/__init__.cpython-313.pyc,,
guardian/management/commands/__pycache__/clean_orphan_obj_perms.cpython-313.pyc,,
guardian/management/commands/clean_orphan_obj_perms.py,sha256=ghcFb7XIDpbtiF3J7GMeLxkbcPGi-b8sxI0LEPIiZ38,773
guardian/managers.py,sha256=E2cn1SqRWKYdwvUCkbG-b94SF9j-7lqRnlNkCXpMeYE,6639
guardian/migrations/0001_initial.py,sha256=dNdzKbSzeABtWAvihyyq5tOMdll0VthTL9Q3EQqaj2s,2158
guardian/migrations/0002_generic_permissions_index.py,sha256=ZrNw5s2QzzxpCe9O87mfLoPlhmYgWsK1kr4PAccjA_E,616
guardian/migrations/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
guardian/migrations/__pycache__/0001_initial.cpython-313.pyc,,
guardian/migrations/__pycache__/0002_generic_permissions_index.cpython-313.pyc,,
guardian/migrations/__pycache__/__init__.cpython-313.pyc,,
guardian/mixins.py,sha256=oIcVIdhisc-egS8mZ4DEEZCAzVhuwm_LJCFC4RAypas,13570
guardian/models/__init__.py,sha256=XqIAyz3BecB9L0bI9mgInFCqXBIRw3lGcgJcgnwOAAQ,596
guardian/models/__pycache__/__init__.cpython-313.pyc,,
guardian/models/__pycache__/models.cpython-313.pyc,,
guardian/models/models.py,sha256=V4RMLPGociZn9VO8QMbEczkG7yF39soGFsu276gqCWA,7888
guardian/py.typed,sha256=ytiiOCM6Kzq0SbEzDVKpbp8di8XFekc-WGj7EodFcHc,35
guardian/shortcuts.py,sha256=a9v6pnzIFQrqjSxlUo6sSnE3lXW2jazhIvlay-AJEzo,36435
guardian/static/guardian/img/icon-no.svg,sha256=QqBaTrrp3KhYJxLYB5E-0cn_s4A_Y8PImYdWjfQSM-c,560
guardian/static/guardian/img/icon-yes.svg,sha256=_H4JqLywJ-NxoPLqSqk9aGJcxEdZwtSFua1TuI9kIcM,436
guardian/templates/admin/guardian/contrib/grappelli/field.html,sha256=V3ltBuMqYbYlRl2HgcjBN_B9R9lg3OVSXEM0KBLlmNQ,671
guardian/templates/admin/guardian/contrib/grappelli/obj_perms_manage.html,sha256=rvS5fUZ72y4UzL8hwJLXtagw4jlgRB2g9F6zDX-gqmg,6845
guardian/templates/admin/guardian/contrib/grappelli/obj_perms_manage_group.html,sha256=2QwxIdZyp5r0eR2NnSJimXLeHS3Iyik6yz_1X32Ek08,2693
guardian/templates/admin/guardian/contrib/grappelli/obj_perms_manage_user.html,sha256=IOv24aOanglWLkdBb0XiqD73TFU0yPejl94VQJW9w6k,2687
guardian/templates/admin/guardian/model/change_form.html,sha256=07fHrHdR4r4HJedtyvN4gVFraBSPIS2gOEJxfd-cJ1s,336
guardian/templates/admin/guardian/model/field.html,sha256=ZCz1N9TyV6hkiGBuSUMewOtL-9ubEXay-_ih38Lep4c,394
guardian/templates/admin/guardian/model/obj_perms_manage.html,sha256=3mLfqa2nJFbRmP9Uim92-_Y9UoD6xK59qnLHqd6ERhI,4998
guardian/templates/admin/guardian/model/obj_perms_manage_group.html,sha256=SnUByAvU27-Wn91Es0w1xS0h5fbMABOA4WT0VakV2VU,1609
guardian/templates/admin/guardian/model/obj_perms_manage_user.html,sha256=FN00DNyuC8qu0rXk30h0WEqKq_gl70Po5EI6cJdCqkg,1579
guardian/templates/admin/guardian/model/obj_perms_no.html,sha256=xgMcAono_bqc65k0bwVdHWwhESgNsNIr2OYEj12ZCzU,96
guardian/templates/admin/guardian/model/obj_perms_yes.html,sha256=0tS7uvi_2y8DLbL8Ft9JBuCdD24xt4bUS8yrUWIEq8o,97
guardian/templatetags/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
guardian/templatetags/__pycache__/__init__.cpython-313.pyc,,
guardian/templatetags/__pycache__/guardian_tags.cpython-313.pyc,,
guardian/templatetags/guardian_tags.py,sha256=VX9GoBRvoi0DPkHEc6PlmlxibGC6kIQqpzUyS2Ji1DQ,3549
guardian/testapp/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
guardian/testapp/__pycache__/__init__.cpython-313.pyc,,
guardian/testapp/__pycache__/models.cpython-313.pyc,,
guardian/testapp/__pycache__/testsettings.cpython-313.pyc,,
guardian/testapp/migrations/0001_initial.py,sha256=FME8ZJ_XLQJ20m_4YFynzB7p7T43Zygm6WGyuO9vS6k,8653
guardian/testapp/migrations/0002_logentrywithgroup.py,sha256=hTCAU5ZoMOAOxqICd2q2i5NIIgngmNawr-SmfK3OOSc,804
guardian/testapp/migrations/0003_auto_20190611_0440.py,sha256=u7HanMNTEhnK-n9ZCKcl9N33NbWE0qPKM3zIDbvrkCk,1015
guardian/testapp/migrations/0004_childtestmodel_parenttestmodel.py,sha256=8sgFvezdzXl4Ici8uIwE4QccacN1l8t2VWzXDOLKaQE,972
guardian/testapp/migrations/0005_uuidpkmodel.py,sha256=R7CVIVArzVlHQRj29AQv4jZhTB_FP9SoPAgmMdgcDjs,486
guardian/testapp/migrations/0006_auto_20230727_0658.py,sha256=upHUzJMgww9ziQ1f4yeJY0v97VC4Z9pBUi-Z7rYaSFM,2642
guardian/testapp/migrations/0007_genericgroupobjectpermission.py,sha256=Fcc7-b7oF9SRn3GHiDWZ6HzCfa_z2kf07DxTZ_FZxps,1343
guardian/testapp/migrations/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
guardian/testapp/migrations/__pycache__/0001_initial.cpython-313.pyc,,
guardian/testapp/migrations/__pycache__/0002_logentrywithgroup.cpython-313.pyc,,
guardian/testapp/migrations/__pycache__/0003_auto_20190611_0440.cpython-313.pyc,,
guardian/testapp/migrations/__pycache__/0004_childtestmodel_parenttestmodel.cpython-313.pyc,,
guardian/testapp/migrations/__pycache__/0005_uuidpkmodel.cpython-313.pyc,,
guardian/testapp/migrations/__pycache__/0006_auto_20230727_0658.cpython-313.pyc,,
guardian/testapp/migrations/__pycache__/0007_genericgroupobjectpermission.cpython-313.pyc,,
guardian/testapp/migrations/__pycache__/__init__.cpython-313.pyc,,
guardian/testapp/models.py,sha256=2eUj0Vtx39APsiSTzvpBn6xT1dBaNBtZjINN-hzIRXQ,3615
guardian/testapp/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
guardian/testapp/tests/__pycache__/__init__.cpython-313.pyc,,
guardian/testapp/tests/__pycache__/conf.cpython-313.pyc,,
guardian/testapp/tests/__pycache__/test_admin.cpython-313.pyc,,
guardian/testapp/tests/__pycache__/test_checks.cpython-313.pyc,,
guardian/testapp/tests/__pycache__/test_conf.cpython-313.pyc,,
guardian/testapp/tests/__pycache__/test_core.cpython-313.pyc,,
guardian/testapp/tests/__pycache__/test_custompkmodel.cpython-313.pyc,,
guardian/testapp/tests/__pycache__/test_decorators.cpython-313.pyc,,
guardian/testapp/tests/__pycache__/test_direct_rel.cpython-313.pyc,,
guardian/testapp/tests/__pycache__/test_forms.cpython-313.pyc,,
guardian/testapp/tests/__pycache__/test_management.cpython-313.pyc,,
guardian/testapp/tests/__pycache__/test_managers.cpython-313.pyc,,
guardian/testapp/tests/__pycache__/test_mixins.cpython-313.pyc,,
guardian/testapp/tests/__pycache__/test_orphans.cpython-313.pyc,,
guardian/testapp/tests/__pycache__/test_other.cpython-313.pyc,,
guardian/testapp/tests/__pycache__/test_shortcuts.cpython-313.pyc,,
guardian/testapp/tests/__pycache__/test_tags.cpython-313.pyc,,
guardian/testapp/tests/__pycache__/test_utils.cpython-313.pyc,,
guardian/testapp/tests/__pycache__/urls.cpython-313.pyc,,
guardian/testapp/tests/conf.py,sha256=WvI0_z2TDW0Q6dW3xxtFN-9fFdmt-Miz-ByxhxAUVHw,2696
guardian/testapp/tests/templates/404.html,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
guardian/testapp/tests/templates/500.html,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
guardian/testapp/tests/templates/blank.html,sha256=AbpHGcgLb-kRsJGnwFEktk7uzpZOCcBY74-YBdrKVGs,1
guardian/testapp/tests/templates/dummy403.html,sha256=VZH5Uz8qNc6scx3Xn3AmF0msPJQkK3nmfgBfxMxIX6s,10
guardian/testapp/tests/templates/dummy404.html,sha256=JTIMfXdkluQDl5gBfHYF7bpdTxACeafgvAhoFWpo_9E,10
guardian/testapp/tests/templates/list.html,sha256=oGTexLb2H_tGTDK0z_LJHwxfnO7_dCNk_7LLkj6pbTk,80
guardian/testapp/tests/test_admin.py,sha256=0VGGZQBny_rm-zHS4VhBE2imkBWGV3SElzbQMqmYU-A,21651
guardian/testapp/tests/test_checks.py,sha256=kveja56Udv_ueDMD32ZSp4rHa18_tqzR8RzmNwAopf8,470
guardian/testapp/tests/test_conf.py,sha256=wJXrJlW9TaWQT_5ORu_k1rHrx3tvMP8TYdLX5P-ULEw,896
guardian/testapp/tests/test_core.py,sha256=JkskT4a1tu9KhylGrEj2vWU_syzS98GEsHxLp7Gs4Fg,26849
guardian/testapp/tests/test_custompkmodel.py,sha256=OHybc8-PkZOjvm1bQNOpoPUZCxmQ7RL6KvSPqdbbRPg,1250
guardian/testapp/tests/test_decorators.py,sha256=tcY9mWHAB4Q_Rz1WXNMRGUoaU99RohbHuLqU9Or1CS0,14951
guardian/testapp/tests/test_direct_rel.py,sha256=EykSdgNvwFrb5uKD0c_pr9tk_MHDWgsZhVGb5H7wr_c,11733
guardian/testapp/tests/test_forms.py,sha256=nmBpXHxwQzQ_TXPbRVv7AKFanTcMTHWKG7x_5tBV_NI,1023
guardian/testapp/tests/test_management.py,sha256=N6LBjtHlT9L7FnV3pZygo01SIm6ac4_zn4OnJPbpNIA,2899
guardian/testapp/tests/test_managers.py,sha256=_oZclSCY98JrEfEsTmgN9nqGquDBO4x2xJuppqBrlIY,1349
guardian/testapp/tests/test_mixins.py,sha256=0ta0YHQy2QpvGlfZ04G1IPTIu6Yph6TCEw_a1neq1ZE,8160
guardian/testapp/tests/test_orphans.py,sha256=bEHK4uIi3_zCKgSc-kjAkcRKOB7fiP-FLbhZ_dcc1CE,3564
guardian/testapp/tests/test_other.py,sha256=mgAc0jXFQBYGHpQ_PTb0-m0Zz5MRdCBljjajC8ou-E8,12893
guardian/testapp/tests/test_shortcuts.py,sha256=aaOcUivZiDDXa8OAuleQn-B-R3NMP9gCftJRdg0RjTE,61090
guardian/testapp/tests/test_tags.py,sha256=yySJkWIdYUl8IhUBwvaTxHKwKX7b3-yuGzAvCmzm0G4,6408
guardian/testapp/tests/test_utils.py,sha256=5YKo8ZwUHo4xEGsTcnWN2ev09dBRrr2Qk0AQM1_rd14,5058
guardian/testapp/tests/urls.py,sha256=033W_prkPlE-AEnsRBGno6RCSUEoHqtBy30RXi3XbB0,640
guardian/testapp/testsettings.py,sha256=TaX6Lg5GWfzG25Rdi7z5-mspQCeFt98tg97bApjsfEE,1948
guardian/utils.py,sha256=LWVA9Rr6OzfvzeDi9B-t063_7s-Q-NN0WR9SQgr80c8,11025
