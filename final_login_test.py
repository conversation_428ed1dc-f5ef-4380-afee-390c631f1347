#!/usr/bin/env python
"""
اختبار نهائي شامل لجميع المستخدمين
"""

import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'statistical_system.settings')
django.setup()

from django.contrib.auth.models import User
from stats_app.models import UserProfile, Company
from django.contrib.auth import authenticate

def final_test():
    """اختبار نهائي شامل"""
    print("🎯 اختبار نهائي شامل لنظام تسجيل الدخول")
    print("="*60)
    
    # قائمة جميع المستخدمين
    all_users = [
        {
            'username': 'admin',
            'password': 'admin',
            'role': 'مدير عام',
            'description': 'صلاحيات كاملة للنظام'
        },
        {
            'username': 'alaa',
            'password': 'alaa123',
            'role': 'مدير نظام',
            'description': 'صلاحيات إدارية للنظام'
        },
        {
            'username': 'alaa1',
            'password': 'alaa1123',
            'role': 'مستخدم شركة',
            'description': 'مرتبط بشركة الأسمنت السعودية'
        },
        {
            'username': 'alaa2',
            'password': 'alaa2123',
            'role': 'مستخدم شركة',
            'description': 'مرتبط بشركة الأسمنت السعودية'
        },
        {
            'username': 'testuser',
            'password': 'test123456',
            'role': 'مستخدم تجريبي',
            'description': 'للاختبار والتجريب'
        },
        {
            'username': 'manager',
            'password': 'manager123',
            'role': 'مدير نظام',
            'description': 'صلاحيات إدارية للنظام'
        }
    ]
    
    print("📋 اختبار تسجيل الدخول لجميع المستخدمين:\n")
    
    success_count = 0
    total_count = len(all_users)
    
    for user_data in all_users:
        username = user_data['username']
        password = user_data['password']
        role = user_data['role']
        description = user_data['description']
        
        print(f"🧪 اختبار: {username} ({role})")
        
        # اختبار المصادقة
        user = authenticate(username=username, password=password)
        
        if user:
            print(f"   ✅ تسجيل الدخول ناجح")
            print(f"   👤 الاسم: {user.first_name} {user.last_name}")
            print(f"   📧 البريد: {user.email}")
            print(f"   📝 الوصف: {description}")
            
            # معلومات الصلاحيات
            try:
                profile = user.userprofile
                permissions = []
                
                if user.is_superuser:
                    permissions.append("مدير عام")
                if profile.is_system_admin:
                    permissions.append("مدير النظام")
                if profile.is_company_user:
                    permissions.append("مستخدم شركة")
                if user.is_staff:
                    permissions.append("موظف")
                
                print(f"   🔑 الصلاحيات: {' | '.join(permissions)}")
                
                if profile.company:
                    print(f"   🏢 الشركة: {profile.company.name}")
                
            except UserProfile.DoesNotExist:
                print(f"   ⚠️ لا يوجد ملف شخصي")
            
            success_count += 1
            
        else:
            print(f"   ❌ فشل في تسجيل الدخول")
            
            # التحقق من سبب الفشل
            if User.objects.filter(username=username).exists():
                print(f"   🔍 السبب: كلمة مرور خاطئة")
            else:
                print(f"   🔍 السبب: المستخدم غير موجود")
        
        print()
    
    # النتيجة النهائية
    print("="*60)
    print(f"📊 النتيجة النهائية: {success_count}/{total_count} مستخدم نجح في تسجيل الدخول")
    
    if success_count == total_count:
        print("🎉 جميع المستخدمين يعملون بشكل مثالي!")
        print_login_guide()
    else:
        print("⚠️ بعض المستخدمين يواجهون مشاكل")
    
    print("="*60)

def print_login_guide():
    """طباعة دليل تسجيل الدخول"""
    print("\n📖 دليل تسجيل الدخول:")
    print("-"*40)
    
    print("🌐 رابط تسجيل الدخول:")
    print("   http://127.0.0.1:8000/accounts/login/")
    
    print("\n👥 المستخدمين المتاحين:")
    
    users_info = [
        ("admin", "admin", "مدير عام", "جميع الصلاحيات"),
        ("alaa", "alaa123", "مدير نظام", "صلاحيات إدارية"),
        ("alaa1", "alaa1123", "مستخدم شركة", "شركة الأسمنت"),
        ("alaa2", "alaa2123", "مستخدم شركة", "شركة الأسمنت"),
        ("testuser", "test123456", "مستخدم تجريبي", "للاختبار"),
        ("manager", "manager123", "مدير نظام", "صلاحيات إدارية"),
    ]
    
    print("   ┌─────────────┬──────────────┬─────────────────┬─────────────────┐")
    print("   │ المستخدم    │ كلمة المرور   │ النوع           │ الوصف           │")
    print("   ├─────────────┼──────────────┼─────────────────┼─────────────────┤")
    
    for username, password, role, desc in users_info:
        print(f"   │ {username:<11} │ {password:<12} │ {role:<15} │ {desc:<15} │")
    
    print("   └─────────────┴──────────────┴─────────────────┴─────────────────┘")
    
    print("\n💡 نصائح:")
    print("   • استخدم admin للوصول إلى جميع ميزات النظام")
    print("   • استخدم alaa أو manager للمهام الإدارية")
    print("   • استخدم alaa1 أو alaa2 لاختبار ميزات الشركة")
    print("   • استخدم testuser للاختبار العام")

def check_system_health():
    """فحص صحة النظام"""
    print("\n🏥 فحص صحة النظام:")
    print("-"*30)
    
    # إحصائيات عامة
    total_users = User.objects.count()
    total_profiles = UserProfile.objects.count()
    total_companies = Company.objects.count()
    
    print(f"👥 إجمالي المستخدمين: {total_users}")
    print(f"📁 إجمالي الملفات الشخصية: {total_profiles}")
    print(f"🏢 إجمالي الشركات: {total_companies}")
    
    # فحص التطابق
    if total_users == total_profiles:
        print("✅ جميع المستخدمين لديهم ملفات شخصية")
    else:
        print(f"⚠️ {total_users - total_profiles} مستخدم بدون ملف شخصي")
    
    # فحص الملفات المكررة
    duplicate_count = 0
    for user in User.objects.all():
        profile_count = UserProfile.objects.filter(user=user).count()
        if profile_count > 1:
            duplicate_count += 1
    
    if duplicate_count == 0:
        print("✅ لا توجد ملفات مكررة")
    else:
        print(f"⚠️ {duplicate_count} مستخدم لديه ملفات مكررة")
    
    # فحص المستخدمين النشطين
    active_users = User.objects.filter(is_active=True).count()
    print(f"🟢 المستخدمين النشطين: {active_users}/{total_users}")
    
    return total_users == total_profiles and duplicate_count == 0

def main():
    """الدالة الرئيسية"""
    # فحص صحة النظام
    system_healthy = check_system_health()
    
    # الاختبار النهائي
    final_test()
    
    if system_healthy:
        print("\n🎯 النظام جاهز للاستخدام!")
    else:
        print("\n⚠️ النظام يحتاج إلى صيانة")

if __name__ == '__main__':
    main()
