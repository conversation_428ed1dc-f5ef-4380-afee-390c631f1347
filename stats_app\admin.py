from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.contrib.auth.models import User
from .models import Company, Item, Target, StatisticalEntry, UserProfile


@admin.register(Company)
class CompanyAdmin(admin.ModelAdmin):
    list_display = ['name', 'sector', 'is_active', 'created_at']
    list_filter = ['is_active', 'sector', 'created_at']
    search_fields = ['name', 'sector']
    ordering = ['name']
    list_editable = ['is_active']


@admin.register(Item)
class ItemAdmin(admin.ModelAdmin):
    list_display = ['name', 'unit', 'is_active', 'created_at']
    list_filter = ['is_active', 'unit', 'created_at']
    search_fields = ['name', 'unit', 'description']
    ordering = ['name']
    list_editable = ['is_active']


@admin.register(Target)
class TargetAdmin(admin.ModelAdmin):
    list_display = ['company', 'item', 'data_type', 'month', 'year', 'target_quantity', 'target_value', 'created_by']
    list_filter = ['year', 'month', 'data_type', 'company', 'item', 'created_at']
    search_fields = ['company__name', 'item__name']
    ordering = ['-year', '-month', 'company__name', 'item__name', 'data_type']
    autocomplete_fields = ['company', 'item']

    def save_model(self, request, obj, form, change):
        if not change:  # إذا كان إنشاء جديد
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(StatisticalEntry)
class StatisticalEntryAdmin(admin.ModelAdmin):
    list_display = ['company', 'item', 'data_type', 'date', 'quantity', 'value', 'created_by', 'created_at']
    list_filter = ['date', 'data_type', 'company', 'item', 'created_at']
    search_fields = ['company__name', 'item__name', 'notes']
    ordering = ['-date', 'company__name', 'item__name', 'data_type']
    autocomplete_fields = ['company', 'item']
    date_hierarchy = 'date'

    def save_model(self, request, obj, form, change):
        if not change:  # إذا كان إنشاء جديد
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


# تخصيص إدارة المستخدمين (بدون inline لتجنب المشاكل)
class UserAdmin(BaseUserAdmin):

    def save_model(self, request, obj, form, change):
        """حفظ المستخدم مع التأكد من إنشاء UserProfile"""
        super().save_model(request, obj, form, change)

        # التأكد من وجود UserProfile (بدون إنشاء مكرر)
        if not UserProfile.objects.filter(user=obj).exists():
            UserProfile.objects.create(user=obj)


# إلغاء تسجيل المستخدم الافتراضي وإعادة تسجيله مع التخصيص
admin.site.unregister(User)
admin.site.register(User, UserAdmin)


@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    list_display = ['user', 'company', 'is_company_user', 'is_system_admin', 'phone']
    list_filter = ['is_company_user', 'is_system_admin', 'company']
    search_fields = ['user__username', 'user__first_name', 'user__last_name', 'phone']
    autocomplete_fields = ['company']


# تخصيص عناوين الإدارة
admin.site.site_header = "نظام الإحصائيات الشهرية"
admin.site.site_title = "إدارة النظام"
admin.site.index_title = "لوحة التحكم"
