#!/usr/bin/env python
"""
فحص السنوات المتوفرة في البيانات
"""

import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'statistical_system.settings')
django.setup()

from stats_app.models import Company, Item, Target, StatisticalEntry
from django.db.models import Count

def check_available_data():
    """فحص البيانات المتوفرة"""
    print("📊 فحص البيانات المتوفرة في النظام")
    print("="*50)
    
    # فحص الشركات
    companies = Company.objects.filter(is_active=True)
    print(f"🏢 الشركات النشطة: {companies.count()}")
    for company in companies:
        print(f"   • {company.name}")
    
    # فحص العناصر
    items = Item.objects.filter(is_active=True)
    print(f"\n📦 العناصر النشطة: {items.count()}")
    for item in items:
        print(f"   • {item.name} ({item.unit})")
    
    # فحص السنوات في المستهدفات
    target_years = Target.objects.values_list('year', flat=True).distinct().order_by('-year')
    print(f"\n🎯 السنوات في المستهدفات: {list(target_years)}")
    
    # فحص السنوات في الإدخالات
    entry_years = StatisticalEntry.objects.values_list('date__year', flat=True).distinct().order_by('-date__year')
    print(f"📈 السنوات في الإدخالات: {list(entry_years)}")
    
    # دمج السنوات
    all_years = set(target_years) | set(entry_years)
    print(f"📅 جميع السنوات المتوفرة: {sorted(all_years, reverse=True)}")
    
    return sorted(all_years, reverse=True)

def check_data_by_company():
    """فحص البيانات حسب الشركة"""
    print(f"\n🏢 البيانات حسب الشركة")
    print("-"*30)
    
    companies = Company.objects.filter(is_active=True)
    
    for company in companies:
        print(f"\n🏭 {company.name}:")
        
        # المستهدفات
        target_years = Target.objects.filter(company=company).values_list('year', flat=True).distinct().order_by('-year')
        target_count = Target.objects.filter(company=company).count()
        print(f"   🎯 المستهدفات: {target_count} في السنوات {list(target_years)}")
        
        # الإدخالات
        entry_years = StatisticalEntry.objects.filter(company=company).values_list('date__year', flat=True).distinct().order_by('-date__year')
        entry_count = StatisticalEntry.objects.filter(company=company).count()
        print(f"   📈 الإدخالات: {entry_count} في السنوات {list(entry_years)}")
        
        # البيانات حسب النوع
        data_types = ['capacity', 'production', 'inventory', 'sales']
        data_type_names = {
            'capacity': 'الطاقة الإنتاجية',
            'production': 'الإنتاج',
            'inventory': 'المخزون',
            'sales': 'البيع'
        }
        
        print(f"   📊 البيانات حسب النوع:")
        for data_type in data_types:
            entry_count = StatisticalEntry.objects.filter(
                company=company,
                data_type=data_type
            ).count()
            target_count = Target.objects.filter(
                company=company,
                data_type=data_type
            ).count()
            print(f"      • {data_type_names[data_type]}: {entry_count} إدخال، {target_count} مستهدف")

def check_monthly_data():
    """فحص البيانات الشهرية"""
    print(f"\n📅 البيانات الشهرية")
    print("-"*20)
    
    # البيانات حسب السنة والشهر
    years = StatisticalEntry.objects.values_list('date__year', flat=True).distinct().order_by('-date__year')
    
    for year in years:
        print(f"\n📅 سنة {year}:")
        
        # الأشهر المتوفرة
        months = StatisticalEntry.objects.filter(
            date__year=year
        ).values_list('date__month', flat=True).distinct().order_by('date__month')
        
        month_names = {
            1: 'يناير', 2: 'فبراير', 3: 'مارس', 4: 'أبريل',
            5: 'مايو', 6: 'يونيو', 7: 'يوليو', 8: 'أغسطس',
            9: 'سبتمبر', 10: 'أكتوبر', 11: 'نوفمبر', 12: 'ديسمبر'
        }
        
        available_months = [month_names[month] for month in months]
        print(f"   الأشهر المتوفرة: {', '.join(available_months)}")
        
        # عدد الإدخالات لكل شهر
        for month in months:
            count = StatisticalEntry.objects.filter(
                date__year=year,
                date__month=month
            ).count()
            print(f"   {month_names[month]}: {count} إدخال")

def create_sample_data_if_empty():
    """إنشاء بيانات عينة إذا لم توجد بيانات"""
    print(f"\n🔧 فحص الحاجة لبيانات عينة")
    print("-"*30)
    
    entry_count = StatisticalEntry.objects.count()
    target_count = Target.objects.count()
    
    if entry_count == 0 and target_count == 0:
        print("⚠️ لا توجد بيانات في النظام")
        print("💡 يمكنك تشغيل add_saudi_cement_data.py لإضافة بيانات عينة")
        return False
    
    print(f"✅ يوجد {entry_count} إدخال و {target_count} مستهدف في النظام")
    return True

def test_reports_with_available_years():
    """اختبار التقارير مع السنوات المتوفرة"""
    print(f"\n🧪 اختبار التقارير مع السنوات المتوفرة")
    print("-"*40)
    
    from django.test import Client
    
    client = Client()
    
    # تسجيل الدخول
    login_success = client.login(username='alaa1', password='alaa1123')
    
    if not login_success:
        print("❌ فشل في تسجيل الدخول")
        return False
    
    try:
        # اختبار صفحة التقارير
        response = client.get('/reports/')
        
        if response.status_code == 200:
            print("✅ صفحة التقارير تعمل")
            
            # فحص السنوات في السياق
            context = response.context
            if 'years' in context:
                years = context['years']
                print(f"📅 السنوات المتوفرة في الفلاتر: {years}")
                
                if years:
                    # اختبار API مع أول سنة متوفرة
                    test_year = years[0]
                    api_response = client.get(f'/reports/data/?year={test_year}')
                    
                    if api_response.status_code == 200:
                        data = api_response.json()
                        print(f"✅ API يعمل للسنة {test_year}")
                        print(f"   📊 عدد الأشهر: {len(data.get('labels', []))}")
                        print(f"   📈 عدد مجموعات البيانات: {len(data.get('datasets', []))}")
                    else:
                        print(f"❌ API لا يعمل للسنة {test_year}: {api_response.status_code}")
                else:
                    print("⚠️ لا توجد سنوات متوفرة في الفلاتر")
            else:
                print("❌ لا توجد سنوات في السياق")
        else:
            print(f"❌ صفحة التقارير لا تعمل: {response.status_code}")
    
    except Exception as e:
        print(f"❌ خطأ في اختبار التقارير: {e}")
    
    finally:
        client.logout()

def main():
    """الدالة الرئيسية"""
    print("🔍 فحص شامل للبيانات المتوفرة")
    print("="*60)
    
    # فحص البيانات العامة
    available_years = check_available_data()
    
    # فحص البيانات حسب الشركة
    check_data_by_company()
    
    # فحص البيانات الشهرية
    check_monthly_data()
    
    # فحص الحاجة لبيانات عينة
    has_data = create_sample_data_if_empty()
    
    if has_data:
        # اختبار التقارير
        test_reports_with_available_years()
    
    print("\n" + "="*60)
    print("📋 ملخص الفحص:")
    
    if available_years:
        print(f"✅ السنوات المتوفرة: {available_years}")
        print("✅ يمكن استخدام التقارير")
        print(f"🌐 للاختبار: http://127.0.0.1:8000/reports/")
    else:
        print("❌ لا توجد بيانات متوفرة")
        print("💡 قم بتشغيل add_saudi_cement_data.py أولاً")
    
    print("="*60)

if __name__ == '__main__':
    main()
