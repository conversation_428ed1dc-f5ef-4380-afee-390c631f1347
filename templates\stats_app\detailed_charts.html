{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}الرسوم البيانية التفصيلية - نظام الإحصائيات الشهرية{% endblock %}

{% block extra_css %}
<style>
    .chart-container {
        position: relative;
        height: 400px;
        margin-bottom: 30px;
    }
    .chart-controls {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 20px;
    }
    .chart-card {
        transition: transform 0.2s;
    }
    .chart-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    .chart-selector {
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 10px;
        margin-bottom: 15px;
    }
    .chart-selector.active {
        border-color: #007bff;
        background: #f8f9ff;
    }
    .kpi-card {
        transition: all 0.3s ease;
    }
    .kpi-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 20px rgba(0,0,0,0.15);
    }
    .risk-indicator {
        font-size: 2rem;
        margin-bottom: 10px;
    }
    .correlation-cell {
        font-weight: bold;
        font-size: 0.9rem;
    }
    .benchmark-line {
        border-top: 2px dashed #007bff;
        position: relative;
    }
    .text-purple {
        color: #6f42c1 !important;
    }
    .bg-gradient-primary {
        background: linear-gradient(45deg, #007bff, #0056b3);
    }
    .bg-gradient-success {
        background: linear-gradient(45deg, #28a745, #1e7e34);
    }
    .bg-gradient-warning {
        background: linear-gradient(45deg, #ffc107, #e0a800);
    }
    .bg-gradient-danger {
        background: linear-gradient(45deg, #dc3545, #c82333);
    }
    .metric-card {
        border-left: 4px solid #007bff;
        transition: all 0.3s ease;
    }
    .metric-card:hover {
        border-left-width: 6px;
        transform: translateX(2px);
    }
    .ranking-medal {
        font-size: 1.5rem;
    }
    .correlation-matrix th,
    .correlation-matrix td {
        text-align: center;
        vertical-align: middle;
        min-width: 80px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3">
                    <i class="fas fa-chart-line me-2"></i>
                    الرسوم البيانية التفصيلية
                </h1>
                <div>
                    <a href="{% url 'stats_app:charts' %}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-chart-pie me-2"></i>
                        الرسوم الأساسية
                    </a>
                    <a href="{% url 'stats_app:reports' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>
                        العودة للتقارير
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- عناصر التحكم والتصفية -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-sliders-h me-2"></i>
                        عناصر التحكم والتصفية
                    </h5>
                </div>
                <div class="card-body">
                    <form method="get" id="filterForm">
                        {% crispy filter_form %}
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- اختيار نوع الرسم البياني -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        اختيار نوع التحليل
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <div class="chart-selector" data-chart="detailed_monthly">
                                <div class="text-center">
                                    <i class="fas fa-calendar-alt fa-2x text-primary mb-2"></i>
                                    <h6>التحليل الشهري التفصيلي</h6>
                                    <small class="text-muted">مقارنة أنواع البيانات شهرياً</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="chart-selector" data-chart="data_types_comparison">
                                <div class="text-center">
                                    <i class="fas fa-layer-group fa-2x text-success mb-2"></i>
                                    <h6>مقارنة أنواع البيانات</h6>
                                    <small class="text-muted">مقارنة المستهدف مع المحقق</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="chart-selector" data-chart="achievement_analysis">
                                <div class="text-center">
                                    <i class="fas fa-bullseye fa-2x text-warning mb-2"></i>
                                    <h6>تحليل نسب الإنجاز</h6>
                                    <small class="text-muted">نسب الإنجاز الشهرية</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="chart-selector" data-chart="capacity_utilization">
                                <div class="text-center">
                                    <i class="fas fa-industry fa-2x text-danger mb-2"></i>
                                    <h6>استغلال الطاقة الإنتاجية</h6>
                                    <small class="text-muted">نسبة استغلال الطاقة</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <div class="chart-selector" data-chart="trend_analysis">
                                <div class="text-center">
                                    <i class="fas fa-chart-line fa-2x text-info mb-2"></i>
                                    <h6>تحليل الاتجاهات</h6>
                                    <small class="text-muted">اتجاهات آخر 12 شهر</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="chart-selector" data-chart="company_data_types">
                                <div class="text-center">
                                    <i class="fas fa-building fa-2x text-secondary mb-2"></i>
                                    <h6>أداء الشركات</h6>
                                    <small class="text-muted">حسب نوع البيانات</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="chart-selector" data-chart="kpi_dashboard">
                                <div class="text-center">
                                    <i class="fas fa-tachometer-alt fa-2x text-primary mb-2"></i>
                                    <h6>مؤشرات الأداء الرئيسية</h6>
                                    <small class="text-muted">KPIs متقدمة</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="chart-selector" data-chart="variance_analysis">
                                <div class="text-center">
                                    <i class="fas fa-balance-scale fa-2x text-warning mb-2"></i>
                                    <h6>تحليل التباين</h6>
                                    <small class="text-muted">التباين المطلق والنسبي</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <div class="chart-selector" data-chart="efficiency_metrics">
                                <div class="text-center">
                                    <i class="fas fa-cogs fa-2x text-success mb-2"></i>
                                    <h6>مقاييس الكفاءة</h6>
                                    <small class="text-muted">كفاءة الإنتاج والمبيعات</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="chart-selector" data-chart="seasonal_analysis">
                                <div class="text-center">
                                    <i class="fas fa-calendar-alt fa-2x text-info mb-2"></i>
                                    <h6>التحليل الموسمي</h6>
                                    <small class="text-muted">مقارنة 3 سنوات</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="chart-selector" data-chart="performance_ranking">
                                <div class="text-center">
                                    <i class="fas fa-trophy fa-2x text-warning mb-2"></i>
                                    <h6>ترتيب الأداء</h6>
                                    <small class="text-muted">ترتيب الشركات</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="chart-selector" data-chart="growth_analysis">
                                <div class="text-center">
                                    <i class="fas fa-arrow-trend-up fa-2x text-success mb-2"></i>
                                    <h6>تحليل النمو</h6>
                                    <small class="text-muted">مقارنة سنوية</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <div class="chart-selector" data-chart="risk_assessment">
                                <div class="text-center">
                                    <i class="fas fa-exclamation-triangle fa-2x text-danger mb-2"></i>
                                    <h6>تقييم المخاطر</h6>
                                    <small class="text-muted">مؤشرات المخاطر</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="chart-selector" data-chart="forecasting">
                                <div class="text-center">
                                    <i class="fas fa-crystal-ball fa-2x text-purple mb-2"></i>
                                    <h6>التنبؤ</h6>
                                    <small class="text-muted">توقعات 6 أشهر</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="chart-selector" data-chart="benchmark_analysis">
                                <div class="text-center">
                                    <i class="fas fa-chart-bar fa-2x text-info mb-2"></i>
                                    <h6>المعايير المرجعية</h6>
                                    <small class="text-muted">مقارنة مع المعايير</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="chart-selector" data-chart="correlation_matrix">
                                <div class="text-center">
                                    <i class="fas fa-project-diagram fa-2x text-secondary mb-2"></i>
                                    <h6>مصفوفة الارتباط</h6>
                                    <small class="text-muted">ارتباط أنواع البيانات</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الرسم البياني الرئيسي -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card chart-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0" id="chartTitle">
                        <i class="fas fa-chart-area me-2"></i>
                        اختر نوع التحليل من الأعلى
                    </h5>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="downloadChart()">
                            <i class="fas fa-download me-1"></i>
                            تحميل
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="refreshChart()">
                            <i class="fas fa-sync me-1"></i>
                            تحديث
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="mainDetailedChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- رسوم بيانية إضافية -->
    <div class="row" id="additionalCharts" style="display: none;">
        <div class="col-md-6 mb-4">
            <div class="card chart-card">
                <div class="card-header">
                    <h5 class="card-title mb-0" id="secondaryChartTitle">
                        <i class="fas fa-chart-pie me-2"></i>
                        رسم بياني إضافي
                    </h5>
                </div>
                <div class="card-body">
                    <div class="chart-container" style="height: 300px;">
                        <canvas id="secondaryChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6 mb-4">
            <div class="card chart-card">
                <div class="card-header">
                    <h5 class="card-title mb-0" id="tertiaryChartTitle">
                        <i class="fas fa-chart-bar me-2"></i>
                        رسم بياني إضافي
                    </h5>
                </div>
                <div class="card-body">
                    <div class="chart-container" style="height: 300px;">
                        <canvas id="tertiaryChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- مؤشرات الأداء الرئيسية -->
    <div class="row mb-4" id="kpiSection" style="display: none;">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-tachometer-alt me-2"></i>
                        مؤشرات الأداء الرئيسية (KPIs)
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row" id="kpiCards">
                        <!-- سيتم ملؤها ديناميكياً -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- تحليل المخاطر -->
    <div class="row mb-4" id="riskSection" style="display: none;">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-shield-alt me-2"></i>
                        تقييم المخاطر
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row" id="riskMetrics">
                        <!-- سيتم ملؤها ديناميكياً -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- ترتيب الأداء -->
    <div class="row mb-4" id="rankingSection" style="display: none;">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-medal me-2"></i>
                        ترتيب الأداء
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover" id="rankingTable">
                            <thead class="table-light">
                                <tr>
                                    <th>الترتيب</th>
                                    <th>الشركة</th>
                                    <th>نسبة الإنجاز</th>
                                    <th>نقاط الثبات</th>
                                    <th>نقاط النمو</th>
                                    <th>نقاط الكفاءة</th>
                                    <th>النقاط الإجمالية</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- سيتم ملؤها ديناميكياً -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- المعايير المرجعية -->
    <div class="row mb-4" id="benchmarkSection" style="display: none;">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        مقارنة مع المعايير المرجعية
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="benchmarkChart" height="100"></canvas>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        الإحصائيات المرجعية
                    </h5>
                </div>
                <div class="card-body" id="benchmarkStats">
                    <!-- سيتم ملؤها ديناميكياً -->
                </div>
            </div>
        </div>
    </div>

    <!-- مصفوفة الارتباط -->
    <div class="row mb-4" id="correlationSection" style="display: none;">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-project-diagram me-2"></i>
                        مصفوفة الارتباط بين أنواع البيانات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="correlationMatrix">
                            <!-- سيتم ملؤها ديناميكياً -->
                        </table>
                    </div>
                    <div class="mt-3">
                        <small class="text-muted">
                            <strong>تفسير معاملات الارتباط:</strong><br>
                            <span class="badge bg-success">0.7 - 1.0</span> ارتباط قوي موجب
                            <span class="badge bg-info">0.3 - 0.7</span> ارتباط متوسط موجب
                            <span class="badge bg-secondary">-0.3 - 0.3</span> ارتباط ضعيف
                            <span class="badge bg-warning">-0.7 - -0.3</span> ارتباط متوسط سالب
                            <span class="badge bg-danger">-1.0 - -0.7</span> ارتباط قوي سالب
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات تفصيلية -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        إحصائيات تفصيلية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row" id="detailedStats">
                        <div class="col-12 text-center text-muted">
                            <i class="fas fa-chart-line fa-3x mb-3"></i>
                            <p>اختر نوع التحليل لعرض الإحصائيات التفصيلية</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let mainChart, secondaryChart, tertiaryChart;
let currentChartType = null;

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // إضافة مستمعي الأحداث لاختيار نوع الرسم البياني
    document.querySelectorAll('.chart-selector').forEach(selector => {
        selector.addEventListener('click', function() {
            const chartType = this.dataset.chart;
            selectChartType(chartType);
        });
    });

    // إضافة مستمع لتغيير التصفية
    document.getElementById('filterForm').addEventListener('change', function() {
        if (currentChartType) {
            loadChart(currentChartType);
        }
    });

    // تحديد النوع الافتراضي
    selectChartType('detailed_monthly');
});

function selectChartType(chartType) {
    // إزالة التحديد من جميع الاختيارات
    document.querySelectorAll('.chart-selector').forEach(selector => {
        selector.classList.remove('active');
    });

    // تحديد الاختيار الحالي
    document.querySelector(`[data-chart="${chartType}"]`).classList.add('active');

    currentChartType = chartType;
    loadChart(chartType);
}

function loadChart(chartType) {
    // الحصول على معاملات التصفية
    const formData = new FormData(document.getElementById('filterForm'));
    const params = new URLSearchParams(formData);
    params.append('type', chartType);

    // تحديث عنوان الرسم البياني
    updateChartTitle(chartType);

    // تحميل البيانات
    fetch(`{% url "stats_app:chart_data_api" %}?${params.toString()}`)
        .then(response => response.json())
        .then(data => {
            renderChart(chartType, data);
            updateDetailedStats(chartType, data);
            handleSpecialChartTypes(chartType, data);
        })
        .catch(error => {
            console.error('خطأ في تحميل البيانات:', error);
        });
}

function updateChartTitle(chartType) {
    const titles = {
        'detailed_monthly': 'التحليل الشهري التفصيلي',
        'data_types_comparison': 'مقارنة أنواع البيانات',
        'achievement_analysis': 'تحليل نسب الإنجاز',
        'capacity_utilization': 'تحليل استغلال الطاقة الإنتاجية',
        'trend_analysis': 'تحليل الاتجاهات',
        'company_data_types': 'أداء الشركات حسب نوع البيانات',
        'kpi_dashboard': 'مؤشرات الأداء الرئيسية',
        'variance_analysis': 'تحليل التباين',
        'efficiency_metrics': 'مقاييس الكفاءة',
        'seasonal_analysis': 'التحليل الموسمي',
        'performance_ranking': 'ترتيب الأداء',
        'growth_analysis': 'تحليل النمو',
        'risk_assessment': 'تقييم المخاطر',
        'forecasting': 'التنبؤ',
        'benchmark_analysis': 'المعايير المرجعية',
        'correlation_matrix': 'مصفوفة الارتباط'
    };

    document.getElementById('chartTitle').innerHTML =
        `<i class="fas fa-chart-area me-2"></i>${titles[chartType] || 'رسم بياني تفصيلي'}`;
}

function renderChart(chartType, data) {
    // تدمير الرسم البياني الموجود
    if (mainChart) {
        mainChart.destroy();
    }

    const ctx = document.getElementById('mainDetailedChart').getContext('2d');
    
    // تحديد نوع الرسم البياني والخيارات حسب النوع
    let chartConfig = getChartConfig(chartType, data);
    
    mainChart = new Chart(ctx, chartConfig);
}

function getChartConfig(chartType, data) {
    const baseConfig = {
        data: data,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'القيمة'
                    }
                }
            }
        }
    };

    // تخصيص الإعدادات حسب نوع الرسم البياني
    switch(chartType) {
        case 'detailed_monthly':
        case 'trend_analysis':
            baseConfig.type = 'line';
            break;
        case 'data_types_comparison':
        case 'company_data_types':
            baseConfig.type = 'bar';
            break;
        case 'achievement_analysis':
            baseConfig.type = 'line';
            baseConfig.options.scales.y.max = 120;
            baseConfig.options.scales.y.title.text = 'نسبة الإنجاز (%)';
            break;
        case 'capacity_utilization':
            baseConfig.type = 'bar';
            baseConfig.options.scales.y.max = 100;
            baseConfig.options.scales.y.title.text = 'نسبة الاستغلال (%)';
            break;
        default:
            baseConfig.type = 'bar';
    }

    // إضافة خيارات خاصة لتحليل الاتجاهات
    if (chartType === 'trend_analysis') {
        baseConfig.options.scales.y1 = {
            type: 'linear',
            display: true,
            position: 'right',
            title: {
                display: true,
                text: 'القيمة'
            },
            grid: {
                drawOnChartArea: false,
            },
        };
    }

    return baseConfig;
}

function updateDetailedStats(chartType, data) {
    const statsContainer = document.getElementById('detailedStats');
    
    // حساب إحصائيات مختلفة حسب نوع الرسم البياني
    let statsHTML = '';
    
    if (data.datasets && data.datasets.length > 0) {
        const dataset = data.datasets[0];
        const values = dataset.data;
        
        if (values.length > 0) {
            const total = values.reduce((a, b) => a + b, 0);
            const average = total / values.length;
            const max = Math.max(...values);
            const min = Math.min(...values);
            
            statsHTML = `
                <div class="col-md-3 text-center">
                    <h4 class="text-primary">${total.toLocaleString()}</h4>
                    <small class="text-muted">الإجمالي</small>
                </div>
                <div class="col-md-3 text-center">
                    <h4 class="text-success">${average.toFixed(2)}</h4>
                    <small class="text-muted">المتوسط</small>
                </div>
                <div class="col-md-3 text-center">
                    <h4 class="text-warning">${max.toLocaleString()}</h4>
                    <small class="text-muted">الحد الأقصى</small>
                </div>
                <div class="col-md-3 text-center">
                    <h4 class="text-info">${min.toLocaleString()}</h4>
                    <small class="text-muted">الحد الأدنى</small>
                </div>
            `;
        }
    }
    
    if (!statsHTML) {
        statsHTML = `
            <div class="col-12 text-center text-muted">
                <i class="fas fa-info-circle fa-2x mb-3"></i>
                <p>لا توجد بيانات كافية لعرض الإحصائيات</p>
            </div>
        `;
    }
    
    statsContainer.innerHTML = statsHTML;
}

function downloadChart() {
    if (mainChart) {
        const link = document.createElement('a');
        link.download = `chart-${currentChartType}-${new Date().getTime()}.png`;
        link.href = mainChart.toBase64Image();
        link.click();
    }
}

function refreshChart() {
    if (currentChartType) {
        loadChart(currentChartType);
    }
}

function handleSpecialChartTypes(chartType, data) {
    // إخفاء جميع الأقسام الخاصة
    hideAllSpecialSections();

    switch(chartType) {
        case 'kpi_dashboard':
            showKPISection(data);
            break;
        case 'risk_assessment':
            showRiskSection(data);
            break;
        case 'performance_ranking':
            showRankingSection(data);
            break;
        case 'benchmark_analysis':
            showBenchmarkSection(data);
            break;
        case 'correlation_matrix':
            showCorrelationSection(data);
            break;
        case 'variance_analysis':
            setupVarianceChart(data);
            break;
        case 'efficiency_metrics':
            setupEfficiencyChart(data);
            break;
        case 'forecasting':
            setupForecastingChart(data);
            break;
    }
}

function hideAllSpecialSections() {
    document.getElementById('kpiSection').style.display = 'none';
    document.getElementById('riskSection').style.display = 'none';
    document.getElementById('rankingSection').style.display = 'none';
    document.getElementById('benchmarkSection').style.display = 'none';
    document.getElementById('correlationSection').style.display = 'none';
}

function showKPISection(data) {
    if (!data.kpis) return;

    document.getElementById('kpiSection').style.display = 'block';
    const kpiCards = document.getElementById('kpiCards');

    let cardsHTML = '';
    for (const [dataType, kpi] of Object.entries(data.kpis)) {
        const achievementColor = kpi.achievement_rate >= 100 ? 'success' :
                                kpi.achievement_rate >= 80 ? 'warning' : 'danger';
        const growthColor = kpi.growth_rate >= 0 ? 'success' : 'danger';
        const growthIcon = kpi.growth_rate >= 0 ? 'fa-arrow-up' : 'fa-arrow-down';

        cardsHTML += `
            <div class="col-md-3 mb-3">
                <div class="card border-${achievementColor}">
                    <div class="card-header bg-${achievementColor} text-white">
                        <h6 class="card-title mb-0">${kpi.name}</h6>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6">
                                <h5 class="text-${achievementColor}">${kpi.achievement_rate}%</h5>
                                <small class="text-muted">نسبة الإنجاز</small>
                            </div>
                            <div class="col-6">
                                <h5 class="text-${growthColor}">
                                    <i class="fas ${growthIcon} me-1"></i>${Math.abs(kpi.growth_rate)}%
                                </h5>
                                <small class="text-muted">معدل النمو</small>
                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-12">
                                <small class="text-muted">
                                    <strong>التباين:</strong> ${kpi.variance.toLocaleString()}<br>
                                    <strong>الانحراف المعياري:</strong> ${kpi.std_deviation}<br>
                                    <strong>معامل التباين:</strong> ${kpi.coefficient_of_variation}%
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    kpiCards.innerHTML = cardsHTML;
}

function showRiskSection(data) {
    if (!data.risk_metrics) return;

    document.getElementById('riskSection').style.display = 'block';
    const riskMetrics = document.getElementById('riskMetrics');

    let metricsHTML = '';
    for (const [dataType, risk] of Object.entries(data.risk_metrics)) {
        const riskColorClass = risk.risk_color === 'green' ? 'success' :
                              risk.risk_color === 'yellow' ? 'warning' :
                              risk.risk_color === 'orange' ? 'warning' : 'danger';

        metricsHTML += `
            <div class="col-md-3 mb-3">
                <div class="card border-${riskColorClass}">
                    <div class="card-header bg-${riskColorClass} text-white">
                        <h6 class="card-title mb-0">${risk.name}</h6>
                    </div>
                    <div class="card-body">
                        <div class="text-center mb-3">
                            <h4 class="text-${riskColorClass}">${risk.risk_level}</h4>
                            <small class="text-muted">مستوى المخاطر</small>
                        </div>
                        <div class="row">
                            <div class="col-12">
                                <small>
                                    <strong>نقاط المخاطر:</strong> ${risk.risk_score}<br>
                                    <strong>التقلبات:</strong> ${risk.volatility}%<br>
                                    <strong>احتمالية الفشل:</strong> ${risk.failure_probability}%
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    riskMetrics.innerHTML = metricsHTML;
}

function showRankingSection(data) {
    if (!data.rankings) return;

    document.getElementById('rankingSection').style.display = 'block';
    const tableBody = document.querySelector('#rankingTable tbody');

    let rowsHTML = '';
    data.rankings.forEach(company => {
        const medalIcon = company.rank === 1 ? '🥇' :
                         company.rank === 2 ? '🥈' :
                         company.rank === 3 ? '🥉' : company.rank;

        rowsHTML += `
            <tr>
                <td><strong>${medalIcon}</strong></td>
                <td>${company.company}</td>
                <td><span class="badge bg-primary">${company.achievement_rate}%</span></td>
                <td><span class="badge bg-info">${company.consistency_score}</span></td>
                <td><span class="badge bg-success">${company.growth_score}</span></td>
                <td><span class="badge bg-warning">${company.efficiency_score}</span></td>
                <td><strong class="text-primary">${company.overall_score}</strong></td>
            </tr>
        `;
    });

    tableBody.innerHTML = rowsHTML;
}

function showBenchmarkSection(data) {
    if (!data.chart_data || !data.benchmark_stats) return;

    document.getElementById('benchmarkSection').style.display = 'block';

    // رسم الرسم البياني
    const ctx = document.getElementById('benchmarkChart').getContext('2d');
    if (window.benchmarkChart) {
        window.benchmarkChart.destroy();
    }

    window.benchmarkChart = new Chart(ctx, {
        type: 'bar',
        data: data.chart_data,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'نسبة الإنجاز (%)'
                    }
                }
            }
        }
    });

    // عرض الإحصائيات المرجعية
    const benchmarkStats = document.getElementById('benchmarkStats');
    benchmarkStats.innerHTML = `
        <div class="mb-3">
            <strong>المتوسط:</strong> <span class="badge bg-primary">${data.benchmark_stats.mean}%</span>
        </div>
        <div class="mb-3">
            <strong>الوسيط:</strong> <span class="badge bg-info">${data.benchmark_stats.median}%</span>
        </div>
        <div class="mb-3">
            <strong>الربع الثالث:</strong> <span class="badge bg-success">${data.benchmark_stats.q3}%</span>
        </div>
        <div class="mb-3">
            <strong>الربع الأول:</strong> <span class="badge bg-warning">${data.benchmark_stats.q1}%</span>
        </div>
        <div class="mb-3">
            <strong>الحد الأقصى:</strong> <span class="badge bg-success">${data.benchmark_stats.max}%</span>
        </div>
        <div class="mb-3">
            <strong>الحد الأدنى:</strong> <span class="badge bg-danger">${data.benchmark_stats.min}%</span>
        </div>
        <div class="mb-3">
            <strong>الانحراف المعياري:</strong> <span class="badge bg-secondary">${data.benchmark_stats.std_dev}</span>
        </div>
    `;
}

function showCorrelationSection(data) {
    if (!data.correlations || !data.labels) return;

    document.getElementById('correlationSection').style.display = 'block';
    const correlationMatrix = document.getElementById('correlationMatrix');

    // إنشاء جدول مصفوفة الارتباط
    let tableHTML = '<thead><tr><th></th>';
    data.labels.forEach(label => {
        tableHTML += `<th class="text-center">${label}</th>`;
    });
    tableHTML += '</tr></thead><tbody>';

    const dataTypes = Object.keys(data.correlations);
    dataTypes.forEach((dt1, i) => {
        tableHTML += `<tr><th>${data.labels[i]}</th>`;
        dataTypes.forEach(dt2 => {
            const correlation = data.correlations[dt1][dt2];
            const cellClass = getCorrelationCellClass(correlation);
            tableHTML += `<td class="text-center ${cellClass}">${correlation}</td>`;
        });
        tableHTML += '</tr>';
    });

    tableHTML += '</tbody>';
    correlationMatrix.innerHTML = tableHTML;
}

function getCorrelationCellClass(correlation) {
    const abs = Math.abs(correlation);
    if (abs >= 0.7) return correlation > 0 ? 'bg-success text-white' : 'bg-danger text-white';
    if (abs >= 0.3) return correlation > 0 ? 'bg-info text-white' : 'bg-warning';
    return 'bg-light';
}

function setupVarianceChart(data) {
    // إعداد خاص للرسم البياني للتباين مع محورين Y
    if (mainChart) {
        mainChart.destroy();
    }

    const ctx = document.getElementById('mainDetailedChart').getContext('2d');
    mainChart = new Chart(ctx, {
        type: 'bar',
        data: data,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: 'التباين المطلق'
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: {
                        display: true,
                        text: 'التباين النسبي (%)'
                    },
                    grid: {
                        drawOnChartArea: false,
                    },
                }
            }
        }
    });
}

function setupEfficiencyChart(data) {
    // إعداد خاص لرسم الكفاءة مع محور إضافي
    if (mainChart) {
        mainChart.destroy();
    }

    const ctx = document.getElementById('mainDetailedChart').getContext('2d');
    mainChart = new Chart(ctx, {
        type: 'line',
        data: data,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100,
                    title: {
                        display: true,
                        text: 'الكفاءة (%)'
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: {
                        display: true,
                        text: 'معدل الدوران'
                    },
                    grid: {
                        drawOnChartArea: false,
                    },
                }
            }
        }
    });
}

function setupForecastingChart(data) {
    // إعداد خاص لرسم التنبؤ
    if (mainChart) {
        mainChart.destroy();
    }

    const ctx = document.getElementById('mainDetailedChart').getContext('2d');
    mainChart = new Chart(ctx, {
        type: 'line',
        data: data,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    callbacks: {
                        title: function(context) {
                            return context[0].label;
                        },
                        label: function(context) {
                            let label = context.dataset.label || '';
                            if (label) {
                                label += ': ';
                            }
                            if (context.parsed.y !== null) {
                                label += context.parsed.y.toLocaleString();
                            }
                            return label;
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'القيمة'
                    }
                }
            }
        }
    });
}
</script>
{% endblock %}
