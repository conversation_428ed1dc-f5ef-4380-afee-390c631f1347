{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}الرسوم البيانية التفصيلية - نظام الإحصائيات الشهرية{% endblock %}

{% block extra_css %}
<style>
    .chart-container {
        position: relative;
        height: 400px;
        margin-bottom: 30px;
    }
    .chart-controls {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 20px;
    }
    .chart-card {
        transition: transform 0.2s;
    }
    .chart-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    .chart-selector {
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 10px;
        margin-bottom: 15px;
    }
    .chart-selector.active {
        border-color: #007bff;
        background: #f8f9ff;
    }
    .kpi-card {
        transition: all 0.3s ease;
    }
    .kpi-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 20px rgba(0,0,0,0.15);
    }
    .risk-indicator {
        font-size: 2rem;
        margin-bottom: 10px;
    }
    .correlation-cell {
        font-weight: bold;
        font-size: 0.9rem;
    }
    .benchmark-line {
        border-top: 2px dashed #007bff;
        position: relative;
    }
    .text-purple {
        color: #6f42c1 !important;
    }
    .bg-gradient-primary {
        background: linear-gradient(45deg, #007bff, #0056b3);
    }
    .bg-gradient-success {
        background: linear-gradient(45deg, #28a745, #1e7e34);
    }
    .bg-gradient-warning {
        background: linear-gradient(45deg, #ffc107, #e0a800);
    }
    .bg-gradient-danger {
        background: linear-gradient(45deg, #dc3545, #c82333);
    }
    .metric-card {
        border-left: 4px solid #007bff;
        transition: all 0.3s ease;
    }
    .metric-card:hover {
        border-left-width: 6px;
        transform: translateX(2px);
    }
    .ranking-medal {
        font-size: 1.5rem;
    }
    .correlation-matrix th,
    .correlation-matrix td {
        text-align: center;
        vertical-align: middle;
        min-width: 80px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3">
                    <i class="fas fa-chart-line me-2"></i>
                    الرسوم البيانية التفصيلية
                </h1>
                <div>
                    <a href="{% url 'stats_app:charts' %}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-chart-pie me-2"></i>
                        الرسوم الأساسية
                    </a>
                    <a href="{% url 'stats_app:reports' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>
                        العودة للتقارير
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- عناصر التحكم والتصفية -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-sliders-h me-2"></i>
                        عناصر التحكم والتصفية
                    </h5>
                </div>
                <div class="card-body">
                    <form method="get" id="filterForm">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <label for="id_company" class="form-label">
                                    <i class="fas fa-building me-1"></i>الشركة
                                </label>
                                {{ filter_form.company }}
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="id_item" class="form-label">
                                    <i class="fas fa-box me-1"></i>العنصر
                                </label>
                                {{ filter_form.item }}
                            </div>
                            <div class="col-md-2 mb-3">
                                <label for="id_data_type" class="form-label">
                                    <i class="fas fa-layer-group me-1"></i>نوع البيانات
                                </label>
                                {{ filter_form.data_type }}
                            </div>
                            <div class="col-md-2 mb-3">
                                <label for="id_year" class="form-label">
                                    <i class="fas fa-calendar me-1"></i>السنة
                                </label>
                                {{ filter_form.year }}
                            </div>
                            <div class="col-md-2 mb-3">
                                <label for="id_month" class="form-label">
                                    <i class="fas fa-calendar-day me-1"></i>الشهر
                                </label>
                                {{ filter_form.month }}
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <label for="comparison_period" class="form-label">
                                    <i class="fas fa-clock me-1"></i>فترة المقارنة
                                </label>
                                <select class="form-select" id="comparison_period" name="comparison_period">
                                    <option value="monthly">شهرية</option>
                                    <option value="quarterly">ربع سنوية</option>
                                    <option value="yearly">سنوية</option>
                                </select>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="chart_style" class="form-label">
                                    <i class="fas fa-palette me-1"></i>نمط الرسم البياني
                                </label>
                                <select class="form-select" id="chart_style" name="chart_style">
                                    <option value="default">افتراضي</option>
                                    <option value="gradient">متدرج</option>
                                    <option value="pattern">منقوش</option>
                                </select>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="show_trend" class="form-label">
                                    <i class="fas fa-chart-line me-1"></i>خط الاتجاه
                                </label>
                                <div class="form-check form-switch mt-2">
                                    <input class="form-check-input" type="checkbox" id="show_trend" name="show_trend">
                                    <label class="form-check-label" for="show_trend">
                                        إظهار خط الاتجاه
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="show_targets" class="form-label">
                                    <i class="fas fa-bullseye me-1"></i>المستهدفات
                                </label>
                                <div class="form-check form-switch mt-2">
                                    <input class="form-check-input" type="checkbox" id="show_targets" name="show_targets" checked>
                                    <label class="form-check-label" for="show_targets">
                                        إظهار المستهدفات
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12">
                                <div class="btn-group w-100" role="group">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-filter me-2"></i>تطبيق التصفية
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" onclick="resetFilters()">
                                        <i class="fas fa-undo me-2"></i>إعادة تعيين
                                    </button>
                                    <button type="button" class="btn btn-outline-info" onclick="exportData()">
                                        <i class="fas fa-file-export me-2"></i>تصدير البيانات
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- اختيار نوع الرسم البياني -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        اختيار نوع التحليل
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <div class="chart-selector" data-chart="detailed_monthly">
                                <div class="text-center">
                                    <i class="fas fa-calendar-alt fa-2x text-primary mb-2"></i>
                                    <h6>التحليل الشهري التفصيلي</h6>
                                    <small class="text-muted">مقارنة أنواع البيانات شهرياً</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="chart-selector" data-chart="data_types_comparison">
                                <div class="text-center">
                                    <i class="fas fa-layer-group fa-2x text-success mb-2"></i>
                                    <h6>مقارنة أنواع البيانات</h6>
                                    <small class="text-muted">مقارنة المستهدف مع المحقق</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="chart-selector" data-chart="achievement_analysis">
                                <div class="text-center">
                                    <i class="fas fa-bullseye fa-2x text-warning mb-2"></i>
                                    <h6>تحليل نسب الإنجاز</h6>
                                    <small class="text-muted">نسب الإنجاز الشهرية</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="chart-selector" data-chart="capacity_utilization">
                                <div class="text-center">
                                    <i class="fas fa-industry fa-2x text-danger mb-2"></i>
                                    <h6>استغلال الطاقة الإنتاجية</h6>
                                    <small class="text-muted">نسبة استغلال الطاقة</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <div class="chart-selector" data-chart="trend_analysis">
                                <div class="text-center">
                                    <i class="fas fa-chart-line fa-2x text-info mb-2"></i>
                                    <h6>تحليل الاتجاهات</h6>
                                    <small class="text-muted">اتجاهات آخر 12 شهر</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="chart-selector" data-chart="company_data_types">
                                <div class="text-center">
                                    <i class="fas fa-building fa-2x text-secondary mb-2"></i>
                                    <h6>أداء الشركات</h6>
                                    <small class="text-muted">حسب نوع البيانات</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="chart-selector" data-chart="kpi_dashboard">
                                <div class="text-center">
                                    <i class="fas fa-tachometer-alt fa-2x text-primary mb-2"></i>
                                    <h6>مؤشرات الأداء الرئيسية</h6>
                                    <small class="text-muted">KPIs متقدمة</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="chart-selector" data-chart="variance_analysis">
                                <div class="text-center">
                                    <i class="fas fa-balance-scale fa-2x text-warning mb-2"></i>
                                    <h6>تحليل التباين</h6>
                                    <small class="text-muted">التباين المطلق والنسبي</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <div class="chart-selector" data-chart="efficiency_metrics">
                                <div class="text-center">
                                    <i class="fas fa-cogs fa-2x text-success mb-2"></i>
                                    <h6>مقاييس الكفاءة</h6>
                                    <small class="text-muted">كفاءة الإنتاج والمبيعات</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="chart-selector" data-chart="seasonal_analysis">
                                <div class="text-center">
                                    <i class="fas fa-calendar-alt fa-2x text-info mb-2"></i>
                                    <h6>التحليل الموسمي</h6>
                                    <small class="text-muted">مقارنة 3 سنوات</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="chart-selector" data-chart="performance_ranking">
                                <div class="text-center">
                                    <i class="fas fa-trophy fa-2x text-warning mb-2"></i>
                                    <h6>ترتيب الأداء</h6>
                                    <small class="text-muted">ترتيب الشركات</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="chart-selector" data-chart="growth_analysis">
                                <div class="text-center">
                                    <i class="fas fa-arrow-trend-up fa-2x text-success mb-2"></i>
                                    <h6>تحليل النمو</h6>
                                    <small class="text-muted">مقارنة سنوية</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <div class="chart-selector" data-chart="risk_assessment">
                                <div class="text-center">
                                    <i class="fas fa-exclamation-triangle fa-2x text-danger mb-2"></i>
                                    <h6>تقييم المخاطر</h6>
                                    <small class="text-muted">مؤشرات المخاطر</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="chart-selector" data-chart="forecasting">
                                <div class="text-center">
                                    <i class="fas fa-crystal-ball fa-2x text-purple mb-2"></i>
                                    <h6>التنبؤ</h6>
                                    <small class="text-muted">توقعات 6 أشهر</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="chart-selector" data-chart="benchmark_analysis">
                                <div class="text-center">
                                    <i class="fas fa-chart-bar fa-2x text-info mb-2"></i>
                                    <h6>المعايير المرجعية</h6>
                                    <small class="text-muted">مقارنة مع المعايير</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="chart-selector" data-chart="correlation_matrix">
                                <div class="text-center">
                                    <i class="fas fa-project-diagram fa-2x text-secondary mb-2"></i>
                                    <h6>مصفوفة الارتباط</h6>
                                    <small class="text-muted">ارتباط أنواع البيانات</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الرسم البياني الرئيسي -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card chart-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="card-title mb-0" id="chartTitle">
                            <i class="fas fa-chart-area me-2"></i>
                            اختر نوع التحليل من الأعلى
                        </h5>
                        <small class="text-muted" id="chartDescription">
                            اختر نوع التحليل المطلوب من القائمة أعلاه لعرض الرسم البياني التفاعلي
                        </small>
                    </div>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-sm btn-outline-success" onclick="toggleFullscreen()">
                            <i class="fas fa-expand me-1"></i>
                            ملء الشاشة
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="downloadChart()">
                            <i class="fas fa-download me-1"></i>
                            تحميل
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="refreshChart()">
                            <i class="fas fa-sync me-1"></i>
                            تحديث
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-info" onclick="showChartInfo()">
                            <i class="fas fa-info-circle me-1"></i>
                            معلومات
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="mainDetailedChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- رسوم بيانية إضافية -->
    <div class="row" id="additionalCharts" style="display: none;">
        <div class="col-md-6 mb-4">
            <div class="card chart-card">
                <div class="card-header">
                    <h5 class="card-title mb-0" id="secondaryChartTitle">
                        <i class="fas fa-chart-pie me-2"></i>
                        رسم بياني إضافي
                    </h5>
                </div>
                <div class="card-body">
                    <div class="chart-container" style="height: 300px;">
                        <canvas id="secondaryChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6 mb-4">
            <div class="card chart-card">
                <div class="card-header">
                    <h5 class="card-title mb-0" id="tertiaryChartTitle">
                        <i class="fas fa-chart-bar me-2"></i>
                        رسم بياني إضافي
                    </h5>
                </div>
                <div class="card-body">
                    <div class="chart-container" style="height: 300px;">
                        <canvas id="tertiaryChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- مؤشرات الأداء الرئيسية -->
    <div class="row mb-4" id="kpiSection" style="display: none;">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-tachometer-alt me-2"></i>
                        مؤشرات الأداء الرئيسية (KPIs)
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row" id="kpiCards">
                        <!-- سيتم ملؤها ديناميكياً -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- تحليل المخاطر -->
    <div class="row mb-4" id="riskSection" style="display: none;">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-shield-alt me-2"></i>
                        تقييم المخاطر
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row" id="riskMetrics">
                        <!-- سيتم ملؤها ديناميكياً -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- ترتيب الأداء -->
    <div class="row mb-4" id="rankingSection" style="display: none;">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-medal me-2"></i>
                        ترتيب الأداء
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover" id="rankingTable">
                            <thead class="table-light">
                                <tr>
                                    <th>الترتيب</th>
                                    <th>الشركة</th>
                                    <th>نسبة الإنجاز</th>
                                    <th>نقاط الثبات</th>
                                    <th>نقاط النمو</th>
                                    <th>نقاط الكفاءة</th>
                                    <th>النقاط الإجمالية</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- سيتم ملؤها ديناميكياً -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- المعايير المرجعية -->
    <div class="row mb-4" id="benchmarkSection" style="display: none;">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        مقارنة مع المعايير المرجعية
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="benchmarkChart" height="100"></canvas>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        الإحصائيات المرجعية
                    </h5>
                </div>
                <div class="card-body" id="benchmarkStats">
                    <!-- سيتم ملؤها ديناميكياً -->
                </div>
            </div>
        </div>
    </div>

    <!-- مصفوفة الارتباط -->
    <div class="row mb-4" id="correlationSection" style="display: none;">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-project-diagram me-2"></i>
                        مصفوفة الارتباط بين أنواع البيانات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="correlationMatrix">
                            <!-- سيتم ملؤها ديناميكياً -->
                        </table>
                    </div>
                    <div class="mt-3">
                        <small class="text-muted">
                            <strong>تفسير معاملات الارتباط:</strong><br>
                            <span class="badge bg-success">0.7 - 1.0</span> ارتباط قوي موجب
                            <span class="badge bg-info">0.3 - 0.7</span> ارتباط متوسط موجب
                            <span class="badge bg-secondary">-0.3 - 0.3</span> ارتباط ضعيف
                            <span class="badge bg-warning">-0.7 - -0.3</span> ارتباط متوسط سالب
                            <span class="badge bg-danger">-1.0 - -0.7</span> ارتباط قوي سالب
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات تفصيلية -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-calculator me-2"></i>
                        إحصائيات تفصيلية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row" id="detailedStats">
                        <div class="col-12 text-center text-muted">
                            <i class="fas fa-chart-line fa-3x mb-3"></i>
                            <p>اختر نوع التحليل لعرض الإحصائيات التفصيلية</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-lightbulb me-2"></i>
                        نصائح وإرشادات
                    </h5>
                </div>
                <div class="card-body">
                    <div id="chartTips">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>كيفية الاستخدام:</h6>
                            <ul class="mb-0">
                                <li>اختر نوع التحليل من القائمة أعلاه</li>
                                <li>استخدم عناصر التصفية لتخصيص البيانات</li>
                                <li>انقر على الرسم البياني للتفاعل معه</li>
                                <li>استخدم أزرار التحكم لتحميل أو تحديث البيانات</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- معلومات الرسم البياني -->
    <div class="modal fade" id="chartInfoModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-info-circle me-2"></i>
                        معلومات الرسم البياني
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="chartInfoContent">
                    <!-- سيتم ملؤها ديناميكياً -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let mainChart, secondaryChart, tertiaryChart;
let currentChartType = null;

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // إضافة مستمعي الأحداث لاختيار نوع الرسم البياني
    document.querySelectorAll('.chart-selector').forEach(selector => {
        selector.addEventListener('click', function() {
            const chartType = this.dataset.chart;
            selectChartType(chartType);
        });
    });

    // إضافة مستمع لتغيير التصفية
    document.getElementById('filterForm').addEventListener('change', function() {
        if (currentChartType) {
            loadChart(currentChartType);
        }
    });

    // تحديد النوع الافتراضي
    selectChartType('detailed_monthly');
});

function selectChartType(chartType) {
    // إزالة التحديد من جميع الاختيارات
    document.querySelectorAll('.chart-selector').forEach(selector => {
        selector.classList.remove('active');
    });

    // تحديد الاختيار الحالي
    document.querySelector(`[data-chart="${chartType}"]`).classList.add('active');

    currentChartType = chartType;
    loadChart(chartType);
}

function loadChart(chartType) {
    // إظهار مؤشر التحميل
    showLoadingIndicator();

    // الحصول على معاملات التصفية
    const formData = new FormData(document.getElementById('filterForm'));
    const params = new URLSearchParams(formData);
    params.append('chart_type', chartType);

    // تحديث عنوان الرسم البياني
    updateChartTitle(chartType);

    // تحميل البيانات
    fetch(`{% url "stats_app:chart_data_api" %}?${params.toString()}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            // إخفاء مؤشر التحميل
            hideLoadingIndicator();

            if (data.error) {
                showErrorMessage(data.error);
                return;
            }

            renderChart(chartType, data);
            updateDetailedStats(chartType, data);
            handleSpecialChartTypes(chartType, data);

            // إظهار رسالة نجاح
            showSuccessMessage('تم تحميل البيانات بنجاح');
        })
        .catch(error => {
            console.error('خطأ في تحميل البيانات:', error);
            hideLoadingIndicator();
            showErrorMessage('حدث خطأ في تحميل البيانات. يرجى المحاولة مرة أخرى.');
        });
}

function hideLoadingIndicator() {
    const chartContainer = document.querySelector('.chart-container');
    chartContainer.innerHTML = '<canvas id="mainDetailedChart"></canvas>';
}

function showErrorMessage(message) {
    const chartContainer = document.querySelector('.chart-container');
    chartContainer.innerHTML = `
        <div class="d-flex justify-content-center align-items-center h-100">
            <div class="text-center">
                <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                <h5 class="text-muted">خطأ في تحميل البيانات</h5>
                <p class="text-muted">${message}</p>
                <button class="btn btn-primary" onclick="refreshChart()">
                    <i class="fas fa-sync me-2"></i>إعادة المحاولة
                </button>
            </div>
        </div>
    `;
}

function showSuccessMessage(message) {
    // إظهار رسالة نجاح مؤقتة
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="fas fa-check-circle me-2"></i>${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);

    // إزالة الرسالة تلقائياً بعد 3 ثوان
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 3000);
}

function updateChartTitle(chartType) {
    const chartInfo = {
        'detailed_monthly': {
            title: 'التحليل الشهري التفصيلي',
            description: 'مقارنة تفصيلية لأنواع البيانات المختلفة على مدار الأشهر',
            icon: 'fa-calendar-alt'
        },
        'data_types_comparison': {
            title: 'مقارنة أنواع البيانات',
            description: 'مقارنة المستهدف مع المحقق لجميع أنواع البيانات',
            icon: 'fa-layer-group'
        },
        'achievement_analysis': {
            title: 'تحليل نسب الإنجاز',
            description: 'تحليل نسب الإنجاز الشهرية وتتبع الأداء',
            icon: 'fa-bullseye'
        },
        'capacity_utilization': {
            title: 'تحليل استغلال الطاقة الإنتاجية',
            description: 'نسبة استغلال الطاقة الإنتاجية مقارنة بالإنتاج الفعلي',
            icon: 'fa-industry'
        },
        'trend_analysis': {
            title: 'تحليل الاتجاهات',
            description: 'تحليل اتجاهات البيانات على مدار آخر 12 شهر',
            icon: 'fa-chart-line'
        },
        'company_data_types': {
            title: 'أداء الشركات حسب نوع البيانات',
            description: 'مقارنة أداء الشركات المختلفة حسب نوع البيانات المحدد',
            icon: 'fa-building'
        },
        'kpi_dashboard': {
            title: 'مؤشرات الأداء الرئيسية',
            description: 'لوحة تحكم شاملة لمؤشرات الأداء الرئيسية (KPIs)',
            icon: 'fa-tachometer-alt'
        },
        'variance_analysis': {
            title: 'تحليل التباين',
            description: 'تحليل التباين المطلق والنسبي بين المستهدف والمحقق',
            icon: 'fa-balance-scale'
        },
        'efficiency_metrics': {
            title: 'مقاييس الكفاءة',
            description: 'مقاييس كفاءة الإنتاج والمبيعات ومعدلات الدوران',
            icon: 'fa-cogs'
        },
        'seasonal_analysis': {
            title: 'التحليل الموسمي',
            description: 'مقارنة البيانات الموسمية على مدار 3 سنوات',
            icon: 'fa-calendar-alt'
        },
        'performance_ranking': {
            title: 'ترتيب الأداء',
            description: 'ترتيب الشركات حسب مؤشرات الأداء المختلفة',
            icon: 'fa-trophy'
        },
        'growth_analysis': {
            title: 'تحليل النمو',
            description: 'تحليل معدلات النمو السنوية والمقارنات',
            icon: 'fa-arrow-trend-up'
        },
        'risk_assessment': {
            title: 'تقييم المخاطر',
            description: 'تقييم مستويات المخاطر ومؤشرات التقلبات',
            icon: 'fa-exclamation-triangle'
        },
        'forecasting': {
            title: 'التنبؤ',
            description: 'توقعات البيانات للأشهر الستة القادمة',
            icon: 'fa-crystal-ball'
        },
        'benchmark_analysis': {
            title: 'المعايير المرجعية',
            description: 'مقارنة الأداء مع المعايير المرجعية والمتوسطات',
            icon: 'fa-chart-bar'
        },
        'correlation_matrix': {
            title: 'مصفوفة الارتباط',
            description: 'تحليل الارتباط بين أنواع البيانات المختلفة',
            icon: 'fa-project-diagram'
        }
    };

    const info = chartInfo[chartType] || {
        title: 'رسم بياني تفصيلي',
        description: 'اختر نوع التحليل المطلوب',
        icon: 'fa-chart-area'
    };

    document.getElementById('chartTitle').innerHTML =
        `<i class="fas ${info.icon} me-2"></i>${info.title}`;

    document.getElementById('chartDescription').textContent = info.description;

    // تحديث النصائح
    updateChartTips(chartType);
}

function renderChart(chartType, data) {
    // تدمير الرسم البياني الموجود
    if (mainChart) {
        mainChart.destroy();
    }

    const ctx = document.getElementById('mainDetailedChart').getContext('2d');
    
    // تحديد نوع الرسم البياني والخيارات حسب النوع
    let chartConfig = getChartConfig(chartType, data);
    
    mainChart = new Chart(ctx, chartConfig);
}

function getChartConfig(chartType, data) {
    const baseConfig = {
        data: data,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'القيمة'
                    }
                }
            }
        }
    };

    // تخصيص الإعدادات حسب نوع الرسم البياني
    switch(chartType) {
        case 'detailed_monthly':
        case 'trend_analysis':
            baseConfig.type = 'line';
            break;
        case 'data_types_comparison':
        case 'company_data_types':
            baseConfig.type = 'bar';
            break;
        case 'achievement_analysis':
            baseConfig.type = 'line';
            baseConfig.options.scales.y.max = 120;
            baseConfig.options.scales.y.title.text = 'نسبة الإنجاز (%)';
            break;
        case 'capacity_utilization':
            baseConfig.type = 'bar';
            baseConfig.options.scales.y.max = 100;
            baseConfig.options.scales.y.title.text = 'نسبة الاستغلال (%)';
            break;
        default:
            baseConfig.type = 'bar';
    }

    // إضافة خيارات خاصة لتحليل الاتجاهات
    if (chartType === 'trend_analysis') {
        baseConfig.options.scales.y1 = {
            type: 'linear',
            display: true,
            position: 'right',
            title: {
                display: true,
                text: 'القيمة'
            },
            grid: {
                drawOnChartArea: false,
            },
        };
    }

    return baseConfig;
}

function updateDetailedStats(chartType, data) {
    const statsContainer = document.getElementById('detailedStats');

    // حساب إحصائيات مختلفة حسب نوع الرسم البياني
    let statsHTML = '';

    if (data.datasets && data.datasets.length > 0) {
        const dataset = data.datasets[0];
        const values = dataset.data.filter(val => val !== null && val !== undefined);

        if (values.length > 0) {
            const total = values.reduce((a, b) => a + b, 0);
            const average = total / values.length;
            const max = Math.max(...values);
            const min = Math.min(...values);
            const range = max - min;

            // حساب الانحراف المعياري
            const variance = values.reduce((acc, val) => acc + Math.pow(val - average, 2), 0) / values.length;
            const stdDev = Math.sqrt(variance);
            const coefficientOfVariation = (stdDev / average) * 100;

            // حساب الوسيط
            const sortedValues = [...values].sort((a, b) => a - b);
            const median = sortedValues.length % 2 === 0
                ? (sortedValues[sortedValues.length / 2 - 1] + sortedValues[sortedValues.length / 2]) / 2
                : sortedValues[Math.floor(sortedValues.length / 2)];

            // حساب الربعيات
            const q1Index = Math.floor(sortedValues.length * 0.25);
            const q3Index = Math.floor(sortedValues.length * 0.75);
            const q1 = sortedValues[q1Index];
            const q3 = sortedValues[q3Index];

            statsHTML = `
                <div class="col-md-2 text-center mb-3">
                    <div class="card border-primary">
                        <div class="card-body p-2">
                            <h5 class="text-primary mb-1">${total.toLocaleString()}</h5>
                            <small class="text-muted">الإجمالي</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-2 text-center mb-3">
                    <div class="card border-success">
                        <div class="card-body p-2">
                            <h5 class="text-success mb-1">${average.toFixed(2)}</h5>
                            <small class="text-muted">المتوسط</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-2 text-center mb-3">
                    <div class="card border-info">
                        <div class="card-body p-2">
                            <h5 class="text-info mb-1">${median.toFixed(2)}</h5>
                            <small class="text-muted">الوسيط</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-2 text-center mb-3">
                    <div class="card border-warning">
                        <div class="card-body p-2">
                            <h5 class="text-warning mb-1">${max.toLocaleString()}</h5>
                            <small class="text-muted">الحد الأقصى</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-2 text-center mb-3">
                    <div class="card border-secondary">
                        <div class="card-body p-2">
                            <h5 class="text-secondary mb-1">${min.toLocaleString()}</h5>
                            <small class="text-muted">الحد الأدنى</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-2 text-center mb-3">
                    <div class="card border-danger">
                        <div class="card-body p-2">
                            <h5 class="text-danger mb-1">${range.toFixed(2)}</h5>
                            <small class="text-muted">المدى</small>
                        </div>
                    </div>
                </div>
                <div class="col-12 mt-3">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="card bg-light">
                                <div class="card-body p-2 text-center">
                                    <strong>الانحراف المعياري:</strong><br>
                                    <span class="text-primary">${stdDev.toFixed(2)}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-light">
                                <div class="card-body p-2 text-center">
                                    <strong>معامل التباين:</strong><br>
                                    <span class="text-success">${coefficientOfVariation.toFixed(2)}%</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-light">
                                <div class="card-body p-2 text-center">
                                    <strong>الربع الأول (Q1):</strong><br>
                                    <span class="text-info">${q1.toFixed(2)}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-light">
                                <div class="card-body p-2 text-center">
                                    <strong>الربع الثالث (Q3):</strong><br>
                                    <span class="text-warning">${q3.toFixed(2)}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // إضافة إحصائيات خاصة حسب نوع الرسم البياني
            if (chartType === 'achievement_analysis' && data.datasets.length >= 2) {
                const targetDataset = data.datasets.find(ds => ds.label.includes('مستهدف'));
                const actualDataset = data.datasets.find(ds => ds.label.includes('محقق'));

                if (targetDataset && actualDataset) {
                    const achievementRates = actualDataset.data.map((actual, index) => {
                        const target = targetDataset.data[index];
                        return target > 0 ? (actual / target) * 100 : 0;
                    });

                    const avgAchievement = achievementRates.reduce((a, b) => a + b, 0) / achievementRates.length;
                    const monthsAboveTarget = achievementRates.filter(rate => rate >= 100).length;

                    statsHTML += `
                        <div class="col-12 mt-3">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-bullseye me-2"></i>تحليل الإنجاز:</h6>
                                <div class="row">
                                    <div class="col-md-4">
                                        <strong>متوسط نسبة الإنجاز:</strong> ${avgAchievement.toFixed(1)}%
                                    </div>
                                    <div class="col-md-4">
                                        <strong>الأشهر التي حققت المستهدف:</strong> ${monthsAboveTarget} من ${achievementRates.length}
                                    </div>
                                    <div class="col-md-4">
                                        <strong>نسبة النجاح:</strong> ${((monthsAboveTarget / achievementRates.length) * 100).toFixed(1)}%
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                }
            }
        }
    }

    if (!statsHTML) {
        statsHTML = `
            <div class="col-12 text-center text-muted">
                <i class="fas fa-info-circle fa-2x mb-3"></i>
                <p>لا توجد بيانات كافية لعرض الإحصائيات</p>
                <small>تأكد من اختيار المرشحات المناسبة وتوفر البيانات للفترة المحددة</small>
            </div>
        `;
    }

    statsContainer.innerHTML = statsHTML;
}

function downloadChart() {
    if (mainChart) {
        const link = document.createElement('a');
        link.download = `chart-${currentChartType}-${new Date().getTime()}.png`;
        link.href = mainChart.toBase64Image();
        link.click();
    }
}

function refreshChart() {
    if (currentChartType) {
        // إظهار مؤشر التحميل
        showLoadingIndicator();
        loadChart(currentChartType);
    }
}

function resetFilters() {
    document.getElementById('filterForm').reset();
    if (currentChartType) {
        loadChart(currentChartType);
    }
}

function exportData() {
    if (!currentChartType) {
        alert('يرجى اختيار نوع التحليل أولاً');
        return;
    }

    const formData = new FormData(document.getElementById('filterForm'));
    const params = new URLSearchParams(formData);
    params.append('type', currentChartType);
    params.append('export', 'csv');

    window.open(`{% url "stats_app:chart_data_api" %}?${params.toString()}`);
}

function toggleFullscreen() {
    const chartCard = document.querySelector('.chart-card');
    if (!document.fullscreenElement) {
        chartCard.requestFullscreen().catch(err => {
            console.log('خطأ في ملء الشاشة:', err);
        });
    } else {
        document.exitFullscreen();
    }
}

function showChartInfo() {
    const modal = new bootstrap.Modal(document.getElementById('chartInfoModal'));
    updateChartInfoModal(currentChartType);
    modal.show();
}

function showLoadingIndicator() {
    const chartContainer = document.querySelector('.chart-container');
    chartContainer.innerHTML = `
        <div class="d-flex justify-content-center align-items-center h-100">
            <div class="text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <p class="mt-3 text-muted">جاري تحميل البيانات...</p>
            </div>
        </div>
    `;
}

function updateChartTips(chartType) {
    const tips = {
        'detailed_monthly': `
            <div class="alert alert-info">
                <h6><i class="fas fa-lightbulb me-2"></i>نصائح للتحليل الشهري:</h6>
                <ul class="mb-0">
                    <li>قارن الأداء عبر الأشهر المختلفة</li>
                    <li>ابحث عن الأنماط الموسمية</li>
                    <li>حدد الشهور ذات الأداء المتميز</li>
                </ul>
            </div>
        `,
        'achievement_analysis': `
            <div class="alert alert-success">
                <h6><i class="fas fa-target me-2"></i>نصائح لتحليل الإنجاز:</h6>
                <ul class="mb-0">
                    <li>نسبة 100% تعني تحقيق المستهدف</li>
                    <li>أكثر من 100% يعني تجاوز المستهدف</li>
                    <li>أقل من 80% يتطلب مراجعة</li>
                </ul>
            </div>
        `,
        'risk_assessment': `
            <div class="alert alert-warning">
                <h6><i class="fas fa-shield-alt me-2"></i>نصائح لتقييم المخاطر:</h6>
                <ul class="mb-0">
                    <li>المخاطر العالية تتطلب إجراءات فورية</li>
                    <li>راقب التقلبات في البيانات</li>
                    <li>ضع خطط طوارئ للمخاطر المحتملة</li>
                </ul>
            </div>
        `
    };

    const tipsContainer = document.getElementById('chartTips');
    tipsContainer.innerHTML = tips[chartType] || `
        <div class="alert alert-info">
            <h6><i class="fas fa-info-circle me-2"></i>كيفية الاستخدام:</h6>
            <ul class="mb-0">
                <li>اختر نوع التحليل من القائمة أعلاه</li>
                <li>استخدم عناصر التصفية لتخصيص البيانات</li>
                <li>انقر على الرسم البياني للتفاعل معه</li>
                <li>استخدم أزرار التحكم لتحميل أو تحديث البيانات</li>
            </ul>
        </div>
    `;
}

function updateChartInfoModal(chartType) {
    const chartInfos = {
        'detailed_monthly': {
            title: 'التحليل الشهري التفصيلي',
            description: 'يعرض هذا التحليل مقارنة تفصيلية لأنواع البيانات المختلفة على مدار الأشهر.',
            features: [
                'مقارنة شهرية لجميع أنواع البيانات',
                'إمكانية تصفية البيانات حسب الشركة والعنصر',
                'عرض الاتجاهات الموسمية',
                'حساب المتوسطات والإجماليات'
            ],
            usage: [
                'اختر الشركة والعنصر المطلوب تحليله',
                'حدد السنة المطلوبة للتحليل',
                'استخدم خيارات التصفية الإضافية حسب الحاجة',
                'انقر على نقاط البيانات لعرض التفاصيل'
            ]
        },
        'achievement_analysis': {
            title: 'تحليل نسب الإنجاز',
            description: 'يحلل نسب الإنجاز الشهرية ويتتبع الأداء مقارنة بالمستهدفات.',
            features: [
                'حساب نسب الإنجاز الشهرية',
                'مقارنة الأداء مع المستهدفات',
                'تحديد الشهور ذات الأداء المتميز',
                'عرض الانحرافات عن المستهدف'
            ],
            usage: [
                'نسبة 100% تعني تحقيق المستهدف بالضبط',
                'أكثر من 100% يعني تجاوز المستهدف',
                'أقل من 80% يتطلب مراجعة وتحسين',
                'استخدم هذا التحليل لتقييم الأداء الشهري'
            ]
        }
    };

    const info = chartInfos[chartType] || {
        title: 'معلومات الرسم البياني',
        description: 'معلومات تفصيلية حول الرسم البياني المحدد.',
        features: ['عرض البيانات بصرياً', 'إمكانية التفاعل مع البيانات'],
        usage: ['اختر نوع التحليل المطلوب', 'استخدم عناصر التصفية']
    };

    const modalContent = document.getElementById('chartInfoContent');
    modalContent.innerHTML = `
        <h6>${info.title}</h6>
        <p>${info.description}</p>

        <h6 class="mt-4">الميزات:</h6>
        <ul>
            ${info.features.map(feature => `<li>${feature}</li>`).join('')}
        </ul>

        <h6 class="mt-4">كيفية الاستخدام:</h6>
        <ul>
            ${info.usage.map(usage => `<li>${usage}</li>`).join('')}
        </ul>
    `;
}

function handleSpecialChartTypes(chartType, data) {
    // إخفاء جميع الأقسام الخاصة
    hideAllSpecialSections();

    switch(chartType) {
        case 'kpi_dashboard':
            showKPISection(data);
            break;
        case 'risk_assessment':
            showRiskSection(data);
            break;
        case 'performance_ranking':
            showRankingSection(data);
            break;
        case 'benchmark_analysis':
            showBenchmarkSection(data);
            break;
        case 'correlation_matrix':
            showCorrelationSection(data);
            break;
        case 'variance_analysis':
            setupVarianceChart(data);
            break;
        case 'efficiency_metrics':
            setupEfficiencyChart(data);
            break;
        case 'forecasting':
            setupForecastingChart(data);
            break;
    }
}

function hideAllSpecialSections() {
    document.getElementById('kpiSection').style.display = 'none';
    document.getElementById('riskSection').style.display = 'none';
    document.getElementById('rankingSection').style.display = 'none';
    document.getElementById('benchmarkSection').style.display = 'none';
    document.getElementById('correlationSection').style.display = 'none';
}

function showKPISection(data) {
    if (!data.kpis) return;

    document.getElementById('kpiSection').style.display = 'block';
    const kpiCards = document.getElementById('kpiCards');

    let cardsHTML = '';
    for (const [dataType, kpi] of Object.entries(data.kpis)) {
        const achievementColor = kpi.achievement_rate >= 100 ? 'success' :
                                kpi.achievement_rate >= 80 ? 'warning' : 'danger';
        const growthColor = kpi.growth_rate >= 0 ? 'success' : 'danger';
        const growthIcon = kpi.growth_rate >= 0 ? 'fa-arrow-up' : 'fa-arrow-down';

        cardsHTML += `
            <div class="col-md-3 mb-3">
                <div class="card border-${achievementColor}">
                    <div class="card-header bg-${achievementColor} text-white">
                        <h6 class="card-title mb-0">${kpi.name}</h6>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6">
                                <h5 class="text-${achievementColor}">${kpi.achievement_rate}%</h5>
                                <small class="text-muted">نسبة الإنجاز</small>
                            </div>
                            <div class="col-6">
                                <h5 class="text-${growthColor}">
                                    <i class="fas ${growthIcon} me-1"></i>${Math.abs(kpi.growth_rate)}%
                                </h5>
                                <small class="text-muted">معدل النمو</small>
                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-12">
                                <small class="text-muted">
                                    <strong>التباين:</strong> ${kpi.variance.toLocaleString()}<br>
                                    <strong>الانحراف المعياري:</strong> ${kpi.std_deviation}<br>
                                    <strong>معامل التباين:</strong> ${kpi.coefficient_of_variation}%
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    kpiCards.innerHTML = cardsHTML;
}

function showRiskSection(data) {
    if (!data.risk_metrics) return;

    document.getElementById('riskSection').style.display = 'block';
    const riskMetrics = document.getElementById('riskMetrics');

    let metricsHTML = '';
    for (const [dataType, risk] of Object.entries(data.risk_metrics)) {
        const riskColorClass = risk.risk_color === 'green' ? 'success' :
                              risk.risk_color === 'yellow' ? 'warning' :
                              risk.risk_color === 'orange' ? 'warning' : 'danger';

        metricsHTML += `
            <div class="col-md-3 mb-3">
                <div class="card border-${riskColorClass}">
                    <div class="card-header bg-${riskColorClass} text-white">
                        <h6 class="card-title mb-0">${risk.name}</h6>
                    </div>
                    <div class="card-body">
                        <div class="text-center mb-3">
                            <h4 class="text-${riskColorClass}">${risk.risk_level}</h4>
                            <small class="text-muted">مستوى المخاطر</small>
                        </div>
                        <div class="row">
                            <div class="col-12">
                                <small>
                                    <strong>نقاط المخاطر:</strong> ${risk.risk_score}<br>
                                    <strong>التقلبات:</strong> ${risk.volatility}%<br>
                                    <strong>احتمالية الفشل:</strong> ${risk.failure_probability}%
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    riskMetrics.innerHTML = metricsHTML;
}

function showRankingSection(data) {
    if (!data.rankings) return;

    document.getElementById('rankingSection').style.display = 'block';
    const tableBody = document.querySelector('#rankingTable tbody');

    let rowsHTML = '';
    data.rankings.forEach(company => {
        const medalIcon = company.rank === 1 ? '🥇' :
                         company.rank === 2 ? '🥈' :
                         company.rank === 3 ? '🥉' : company.rank;

        rowsHTML += `
            <tr>
                <td><strong>${medalIcon}</strong></td>
                <td>${company.company}</td>
                <td><span class="badge bg-primary">${company.achievement_rate}%</span></td>
                <td><span class="badge bg-info">${company.consistency_score}</span></td>
                <td><span class="badge bg-success">${company.growth_score}</span></td>
                <td><span class="badge bg-warning">${company.efficiency_score}</span></td>
                <td><strong class="text-primary">${company.overall_score}</strong></td>
            </tr>
        `;
    });

    tableBody.innerHTML = rowsHTML;
}

function showBenchmarkSection(data) {
    if (!data.chart_data || !data.benchmark_stats) return;

    document.getElementById('benchmarkSection').style.display = 'block';

    // رسم الرسم البياني
    const ctx = document.getElementById('benchmarkChart').getContext('2d');
    if (window.benchmarkChart) {
        window.benchmarkChart.destroy();
    }

    window.benchmarkChart = new Chart(ctx, {
        type: 'bar',
        data: data.chart_data,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'نسبة الإنجاز (%)'
                    }
                }
            }
        }
    });

    // عرض الإحصائيات المرجعية
    const benchmarkStats = document.getElementById('benchmarkStats');
    benchmarkStats.innerHTML = `
        <div class="mb-3">
            <strong>المتوسط:</strong> <span class="badge bg-primary">${data.benchmark_stats.mean}%</span>
        </div>
        <div class="mb-3">
            <strong>الوسيط:</strong> <span class="badge bg-info">${data.benchmark_stats.median}%</span>
        </div>
        <div class="mb-3">
            <strong>الربع الثالث:</strong> <span class="badge bg-success">${data.benchmark_stats.q3}%</span>
        </div>
        <div class="mb-3">
            <strong>الربع الأول:</strong> <span class="badge bg-warning">${data.benchmark_stats.q1}%</span>
        </div>
        <div class="mb-3">
            <strong>الحد الأقصى:</strong> <span class="badge bg-success">${data.benchmark_stats.max}%</span>
        </div>
        <div class="mb-3">
            <strong>الحد الأدنى:</strong> <span class="badge bg-danger">${data.benchmark_stats.min}%</span>
        </div>
        <div class="mb-3">
            <strong>الانحراف المعياري:</strong> <span class="badge bg-secondary">${data.benchmark_stats.std_dev}</span>
        </div>
    `;
}

function showCorrelationSection(data) {
    if (!data.correlations || !data.labels) return;

    document.getElementById('correlationSection').style.display = 'block';
    const correlationMatrix = document.getElementById('correlationMatrix');

    // إنشاء جدول مصفوفة الارتباط
    let tableHTML = '<thead><tr><th></th>';
    data.labels.forEach(label => {
        tableHTML += `<th class="text-center">${label}</th>`;
    });
    tableHTML += '</tr></thead><tbody>';

    const dataTypes = Object.keys(data.correlations);
    dataTypes.forEach((dt1, i) => {
        tableHTML += `<tr><th>${data.labels[i]}</th>`;
        dataTypes.forEach(dt2 => {
            const correlation = data.correlations[dt1][dt2];
            const cellClass = getCorrelationCellClass(correlation);
            tableHTML += `<td class="text-center ${cellClass}">${correlation}</td>`;
        });
        tableHTML += '</tr>';
    });

    tableHTML += '</tbody>';
    correlationMatrix.innerHTML = tableHTML;
}

function getCorrelationCellClass(correlation) {
    const abs = Math.abs(correlation);
    if (abs >= 0.7) return correlation > 0 ? 'bg-success text-white' : 'bg-danger text-white';
    if (abs >= 0.3) return correlation > 0 ? 'bg-info text-white' : 'bg-warning';
    return 'bg-light';
}

function setupVarianceChart(data) {
    // إعداد خاص للرسم البياني للتباين مع محورين Y
    if (mainChart) {
        mainChart.destroy();
    }

    const ctx = document.getElementById('mainDetailedChart').getContext('2d');
    mainChart = new Chart(ctx, {
        type: 'bar',
        data: data,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: 'التباين المطلق'
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: {
                        display: true,
                        text: 'التباين النسبي (%)'
                    },
                    grid: {
                        drawOnChartArea: false,
                    },
                }
            }
        }
    });
}

function setupEfficiencyChart(data) {
    // إعداد خاص لرسم الكفاءة مع محور إضافي
    if (mainChart) {
        mainChart.destroy();
    }

    const ctx = document.getElementById('mainDetailedChart').getContext('2d');
    mainChart = new Chart(ctx, {
        type: 'line',
        data: data,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100,
                    title: {
                        display: true,
                        text: 'الكفاءة (%)'
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: {
                        display: true,
                        text: 'معدل الدوران'
                    },
                    grid: {
                        drawOnChartArea: false,
                    },
                }
            }
        }
    });
}

function setupForecastingChart(data) {
    // إعداد خاص لرسم التنبؤ
    if (mainChart) {
        mainChart.destroy();
    }

    const ctx = document.getElementById('mainDetailedChart').getContext('2d');
    mainChart = new Chart(ctx, {
        type: 'line',
        data: data,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    callbacks: {
                        title: function(context) {
                            return context[0].label;
                        },
                        label: function(context) {
                            let label = context.dataset.label || '';
                            if (label) {
                                label += ': ';
                            }
                            if (context.parsed.y !== null) {
                                label += context.parsed.y.toLocaleString();
                            }
                            return label;
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'القيمة'
                    }
                }
            }
        }
    });
}
</script>
{% endblock %}
