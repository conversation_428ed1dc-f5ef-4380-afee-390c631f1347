{% extends 'base.html' %}

{% block title %}لوحة أنواع البيانات - نظام الإحصائيات الشهرية{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1 class="h3 mb-4">
                <i class="fas fa-layer-group me-2"></i>
                لوحة أنواع البيانات
            </h1>
        </div>
    </div>

    <!-- بطاقات أنواع البيانات -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-primary">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-industry me-2"></i>
                        الطاقة الإنتاجية المتاحة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <h4 class="text-primary">{{ capacity_targets }}</h4>
                            <small class="text-muted">مستهدفات</small>
                        </div>
                        <div class="col-6">
                            <h4 class="text-primary">{{ capacity_entries }}</h4>
                            <small class="text-muted">إدخالات</small>
                        </div>
                    </div>
                    <div class="mt-3">
                        <a href="{% url 'stats_app:target_list' %}?data_type=capacity" class="btn btn-primary btn-sm">
                            عرض التفاصيل
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-success">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cogs me-2"></i>
                        الإنتاج
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <h4 class="text-success">{{ production_targets }}</h4>
                            <small class="text-muted">مستهدفات</small>
                        </div>
                        <div class="col-6">
                            <h4 class="text-success">{{ production_entries }}</h4>
                            <small class="text-muted">إدخالات</small>
                        </div>
                    </div>
                    <div class="mt-3">
                        <a href="{% url 'stats_app:target_list' %}?data_type=production" class="btn btn-success btn-sm">
                            عرض التفاصيل
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-warning">
                <div class="card-header bg-warning text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-warehouse me-2"></i>
                        المخزون
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <h4 class="text-warning">{{ inventory_targets }}</h4>
                            <small class="text-muted">مستهدفات</small>
                        </div>
                        <div class="col-6">
                            <h4 class="text-warning">{{ inventory_entries }}</h4>
                            <small class="text-muted">إدخالات</small>
                        </div>
                    </div>
                    <div class="mt-3">
                        <a href="{% url 'stats_app:target_list' %}?data_type=inventory" class="btn btn-warning btn-sm">
                            عرض التفاصيل
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-shopping-cart me-2"></i>
                        البيع
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <h4 class="text-danger">{{ sales_targets }}</h4>
                            <small class="text-muted">مستهدفات</small>
                        </div>
                        <div class="col-6">
                            <h4 class="text-danger">{{ sales_entries }}</h4>
                            <small class="text-muted">إدخالات</small>
                        </div>
                    </div>
                    <div class="mt-3">
                        <a href="{% url 'stats_app:target_list' %}?data_type=sales" class="btn btn-danger btn-sm">
                            عرض التفاصيل
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- رسم بياني مقارن -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        مقارنة أنواع البيانات
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="dataTypesChart" height="100"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول تفصيلي -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-table me-2"></i>
                        تفاصيل أنواع البيانات حسب الشركة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>الشركة</th>
                                    <th>الطاقة الإنتاجية</th>
                                    <th>الإنتاج</th>
                                    <th>المخزون</th>
                                    <th>البيع</th>
                                    <th>إجمالي الإدخالات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for company_data in companies_data %}
                                <tr>
                                    <td><strong>{{ company_data.company.name }}</strong></td>
                                    <td>
                                        <span class="badge bg-primary">{{ company_data.capacity_count }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-success">{{ company_data.production_count }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-warning">{{ company_data.inventory_count }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-danger">{{ company_data.sales_count }}</span>
                                    </td>
                                    <td>
                                        <strong>{{ company_data.total_count }}</strong>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- روابط سريعة -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bolt me-2"></i>
                        إجراءات سريعة حسب نوع البيانات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="{% url 'stats_app:entry_create' %}?data_type=capacity" class="btn btn-primary w-100">
                                <i class="fas fa-industry me-2"></i>
                                إدخال طاقة إنتاجية
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{% url 'stats_app:entry_create' %}?data_type=production" class="btn btn-success w-100">
                                <i class="fas fa-cogs me-2"></i>
                                إدخال إنتاج
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{% url 'stats_app:entry_create' %}?data_type=inventory" class="btn btn-warning w-100">
                                <i class="fas fa-warehouse me-2"></i>
                                إدخال مخزون
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{% url 'stats_app:entry_create' %}?data_type=sales" class="btn btn-danger w-100">
                                <i class="fas fa-shopping-cart me-2"></i>
                                إدخال مبيعات
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// رسم بياني لمقارنة أنواع البيانات
const ctx = document.getElementById('dataTypesChart').getContext('2d');
new Chart(ctx, {
    type: 'bar',
    data: {
        labels: ['الطاقة الإنتاجية', 'الإنتاج', 'المخزون', 'البيع'],
        datasets: [
            {
                label: 'المستهدفات',
                data: [{{ capacity_targets }}, {{ production_targets }}, {{ inventory_targets }}, {{ sales_targets }}],
                backgroundColor: 'rgba(54, 162, 235, 0.8)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 1
            },
            {
                label: 'الإدخالات',
                data: [{{ capacity_entries }}, {{ production_entries }}, {{ inventory_entries }}, {{ sales_entries }}],
                backgroundColor: 'rgba(255, 99, 132, 0.8)',
                borderColor: 'rgba(255, 99, 132, 1)',
                borderWidth: 1
            }
        ]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                title: {
                    display: true,
                    text: 'العدد'
                }
            }
        },
        plugins: {
            legend: {
                position: 'top',
            },
            title: {
                display: true,
                text: 'مقارنة المستهدفات والإدخالات حسب نوع البيانات'
            }
        }
    }
});
</script>
{% endblock %}
