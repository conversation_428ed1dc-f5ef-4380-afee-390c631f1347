#!/usr/bin/env python
"""
ملخص نهائي لتطبيق فصل البيانات
"""

import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'statistical_system.settings')
django.setup()

from django.contrib.auth.models import User
from stats_app.models import UserProfile, Company, StatisticalEntry, Target

def show_data_separation_summary():
    """عرض ملخص فصل البيانات"""
    print("🎯 ملخص تطبيق فصل البيانات")
    print("="*60)
    
    # إحصائيات عامة
    total_companies = Company.objects.count()
    total_users = User.objects.count()
    total_entries = StatisticalEntry.objects.count()
    total_targets = Target.objects.count()
    
    print(f"📊 إحصائيات عامة:")
    print(f"   🏢 إجمالي الشركات: {total_companies}")
    print(f"   👥 إجمالي المستخدمين: {total_users}")
    print(f"   📈 إجمالي الإدخالات: {total_entries}")
    print(f"   🎯 إجمالي المستهدفات: {total_targets}")
    
    print(f"\n👥 تفاصيل المستخدمين:")
    print("-"*40)
    
    users = User.objects.all().order_by('username')
    
    for user in users:
        try:
            profile = user.userprofile
            
            # تحديد نوع المستخدم
            user_type = []
            if user.is_superuser:
                user_type.append("مدير عام")
            if profile.is_system_admin:
                user_type.append("مدير النظام")
            if profile.is_company_user:
                company_name = profile.company.name if profile.company else "غير محدد"
                user_type.append(f"مستخدم شركة ({company_name})")
            
            if not user_type:
                user_type.append("مستخدم عادي")
            
            # حساب البيانات المرئية للمستخدم
            if profile.is_system_admin or user.is_superuser:
                visible_companies = Company.objects.count()
                visible_entries = StatisticalEntry.objects.count()
                visible_targets = Target.objects.count()
            elif profile.is_company_user and profile.company:
                visible_companies = 1
                visible_entries = StatisticalEntry.objects.filter(company=profile.company).count()
                visible_targets = Target.objects.filter(company=profile.company).count()
            else:
                visible_companies = 0
                visible_entries = 0
                visible_targets = 0
            
            print(f"👤 {user.username}:")
            print(f"   📧 البريد: {user.email}")
            print(f"   🔑 النوع: {' | '.join(user_type)}")
            print(f"   👁️ البيانات المرئية:")
            print(f"      🏢 الشركات: {visible_companies}")
            print(f"      📈 الإدخالات: {visible_entries}")
            print(f"      🎯 المستهدفات: {visible_targets}")
            print()
            
        except UserProfile.DoesNotExist:
            print(f"👤 {user.username}: ⚠️ لا يوجد ملف شخصي")
            print()

def show_companies_data():
    """عرض بيانات الشركات"""
    print("🏢 تفاصيل الشركات:")
    print("-"*30)
    
    companies = Company.objects.all().order_by('name')
    
    for company in companies:
        entries_count = StatisticalEntry.objects.filter(company=company).count()
        targets_count = Target.objects.filter(company=company).count()
        
        # المستخدمين المرتبطين بالشركة
        company_users = UserProfile.objects.filter(company=company, is_company_user=True)
        
        print(f"🏢 {company.name}:")
        print(f"   📈 الإدخالات: {entries_count}")
        print(f"   🎯 المستهدفات: {targets_count}")
        print(f"   👥 المستخدمين: {company_users.count()}")
        
        for profile in company_users:
            print(f"      - {profile.user.username}")
        
        print()

def show_testing_guide():
    """دليل الاختبار"""
    print("🧪 دليل اختبار فصل البيانات:")
    print("-"*40)
    
    print("📋 خطوات الاختبار:")
    print("1. افتح المتصفح واذهب إلى: http://127.0.0.1:8000/accounts/login/")
    print()
    
    print("2. اختبر المستخدمين التاليين:")
    
    test_users = [
        {
            'username': 'admin',
            'password': 'admin',
            'type': 'مدير عام',
            'expected': 'يرى جميع البيانات لجميع الشركات'
        },
        {
            'username': 'alaa',
            'password': 'alaa123',
            'type': 'مدير نظام',
            'expected': 'يرى جميع البيانات لجميع الشركات'
        },
        {
            'username': 'alaa1',
            'password': 'alaa1123',
            'type': 'مستخدم شركة الأسمنت السعودية',
            'expected': 'يرى بيانات شركة الأسمنت السعودية فقط'
        },
        {
            'username': 'alaa2',
            'password': 'alaa2123',
            'type': 'مستخدم شركة الأسمنت الأردنية',
            'expected': 'يرى بيانات شركة الأسمنت الأردنية فقط'
        }
    ]
    
    for i, user in enumerate(test_users, 1):
        print(f"\n   {i}. {user['type']}:")
        print(f"      اسم المستخدم: {user['username']}")
        print(f"      كلمة المرور: {user['password']}")
        print(f"      المتوقع: {user['expected']}")
    
    print("\n3. الصفحات المطلوب اختبارها:")
    test_pages = [
        "الصفحة الرئيسية (/) - تحقق من الإحصائيات",
        "قائمة الشركات (/companies/) - تحقق من الشركات المرئية",
        "الإدخالات الإحصائية (/entries/) - تحقق من البيانات المرئية",
        "التقارير (/reports/) - تحقق من التقارير المفلترة",
        "الرسوم البيانية (/reports/charts/) - تحقق من الرسوم البيانية"
    ]
    
    for page in test_pages:
        print(f"   • {page}")
    
    print("\n4. نقاط التحقق المهمة:")
    checkpoints = [
        "مستخدم الشركة يرى شركته فقط في قائمة الشركات",
        "مستخدم الشركة يرى بيانات شركته فقط في الإدخالات",
        "الرسوم البيانية تظهر بيانات الشركة المرتبطة فقط",
        "التقارير مفلترة حسب شركة المستخدم",
        "مدير النظام يرى جميع البيانات"
    ]
    
    for checkpoint in checkpoints:
        print(f"   ✓ {checkpoint}")

def show_implementation_summary():
    """ملخص التطبيق"""
    print("\n🔧 ملخص التطبيق:")
    print("-"*25)
    
    implemented_features = [
        "✅ CompanyDataMixin - نظام فصل البيانات الأساسي",
        "✅ تحديث DashboardView - لوحة التحكم مفلترة",
        "✅ تحديث CompanyListView - قائمة الشركات مفلترة",
        "✅ تحديث TargetListView - المستهدفات مفلترة",
        "✅ تحديث StatisticalEntryListView - الإدخالات مفلترة",
        "✅ تحديث ReportsView - التقارير مفلترة",
        "✅ تحديث ChartsView - الرسوم البيانية مفلترة",
        "✅ تحديث جميع عروض JSON API - البيانات مفلترة",
        "✅ التحقق من صحة معرف الشركة في API",
        "✅ ربط المستخدمين بالشركات المناسبة"
    ]
    
    for feature in implemented_features:
        print(f"   {feature}")

def main():
    """الدالة الرئيسية"""
    show_data_separation_summary()
    print()
    show_companies_data()
    print()
    show_implementation_summary()
    print()
    show_testing_guide()
    
    print("\n" + "="*60)
    print("🎉 تم تطبيق فصل البيانات بنجاح!")
    print("✅ كل مستخدم يعمل على بيانات شركته فقط")
    print("✅ مدير النظام يرى جميع البيانات")
    print("✅ النظام جاهز للاستخدام في الإنتاج")
    print("="*60)

if __name__ == '__main__':
    main()
