{% extends 'base.html' %}
{% load static %}

{% block title %}التقارير البسيطة{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.css" rel="stylesheet">
<style>
.chart-container {
    position: relative;
    height: 400px;
    margin: 20px 0;
}

.filter-card {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.btn-group-custom {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.loading {
    text-align: center;
    padding: 40px;
    color: #6c757d;
}

.stats-card {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.stats-number {
    font-size: 2rem;
    font-weight: bold;
    color: #007bff;
}

.stats-label {
    color: #6c757d;
    font-size: 0.9rem;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-chart-bar me-2"></i>التقارير البسيطة</h2>
                <div class="btn-group-custom">
                    <button type="button" class="btn btn-outline-primary" onclick="exportChart()">
                        <i class="fas fa-download me-1"></i>تصدير
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="resetFilters()">
                        <i class="fas fa-refresh me-1"></i>إعادة تعيين
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- الفلاتر -->
    <div class="row">
        <div class="col-12">
            <div class="filter-card">
                <h5><i class="fas fa-filter me-2"></i>الفلاتر</h5>
                <form id="filterForm">
                    <div class="row">
                        <div class="col-md-3">
                            <label for="companySelect" class="form-label">الشركة</label>
                            <select class="form-select" id="companySelect" name="company">
                                <option value="">جميع الشركات</option>
                                {% for company in companies %}
                                <option value="{{ company.id }}">{{ company.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="itemSelect" class="form-label">العنصر</label>
                            <select class="form-select" id="itemSelect" name="item">
                                <option value="">جميع العناصر</option>
                                {% for item in items %}
                                <option value="{{ item.id }}">{{ item.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="yearSelect" class="form-label">السنة</label>
                            <select class="form-select" id="yearSelect" name="year">
                                {% for year in years %}
                                <option value="{{ year }}" {% if year == 2024 %}selected{% endif %}>{{ year }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="dataTypeSelect" class="form-label">نوع البيانات</label>
                            <select class="form-select" id="dataTypeSelect" name="data_type">
                                <option value="">جميع الأنواع</option>
                                {% for type_code, type_name in data_types %}
                                <option value="{{ type_code }}">{{ type_name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <button type="button" class="btn btn-primary w-100" onclick="loadReport()">
                                <i class="fas fa-search me-1"></i>عرض التقرير
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- الإحصائيات السريعة -->
    <div class="row" id="quickStats" style="display: none;">
        <div class="col-md-3">
            <div class="stats-card text-center">
                <div class="stats-number" id="totalAchieved">0</div>
                <div class="stats-label">إجمالي المحقق</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card text-center">
                <div class="stats-number" id="totalTarget">0</div>
                <div class="stats-label">إجمالي المستهدف</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card text-center">
                <div class="stats-number" id="achievementRate">0%</div>
                <div class="stats-label">نسبة الإنجاز</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card text-center">
                <div class="stats-number" id="bestMonth">-</div>
                <div class="stats-label">أفضل شهر</div>
            </div>
        </div>
    </div>

    <!-- الرسم البياني -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-line me-2"></i>مقارنة المستهدف مقابل المحقق</h5>
                </div>
                <div class="card-body">
                    <div id="loadingIndicator" class="loading">
                        <i class="fas fa-spinner fa-spin fa-2x"></i>
                        <p class="mt-2">جاري تحميل البيانات...</p>
                    </div>
                    <div class="chart-container">
                        <canvas id="reportChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول البيانات -->
    <div class="row" id="dataTable" style="display: none;">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-table me-2"></i>البيانات التفصيلية</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>الشهر</th>
                                    <th>المستهدف</th>
                                    <th>المحقق</th>
                                    <th>نسبة الإنجاز</th>
                                    <th>الفرق</th>
                                </tr>
                            </thead>
                            <tbody id="dataTableBody">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>
<script>
let reportChart = null;

// تحميل التقرير عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadReport();
});

function loadReport() {
    // إظهار مؤشر التحميل
    document.getElementById('loadingIndicator').style.display = 'block';
    document.getElementById('quickStats').style.display = 'none';
    document.getElementById('dataTable').style.display = 'none';
    
    // جمع بيانات الفلاتر
    const formData = new FormData(document.getElementById('filterForm'));
    const params = new URLSearchParams(formData);
    
    // طلب البيانات
    fetch(`{% url 'stats_app:simple_report_data' %}?${params}`)
        .then(response => response.json())
        .then(data => {
            updateChart(data);
            updateStats(data);
            updateTable(data);
            document.getElementById('loadingIndicator').style.display = 'none';
        })
        .catch(error => {
            console.error('خطأ في تحميل البيانات:', error);
            document.getElementById('loadingIndicator').style.display = 'none';
            alert('حدث خطأ في تحميل البيانات');
        });
}

function updateChart(data) {
    const ctx = document.getElementById('reportChart').getContext('2d');
    
    if (reportChart) {
        reportChart.destroy();
    }
    
    reportChart = new Chart(ctx, {
        type: 'line',
        data: data,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'القيمة'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'الشهر'
                    }
                }
            },
            plugins: {
                legend: {
                    position: 'top',
                },
                title: {
                    display: true,
                    text: 'مقارنة الأداء الشهري'
                }
            }
        }
    });
}

function updateStats(data) {
    if (!data.datasets || data.datasets.length < 2) return;
    
    const achieved = data.datasets[0].data;
    const targets = data.datasets[1].data;
    
    const totalAchieved = achieved.reduce((a, b) => a + b, 0);
    const totalTarget = targets.reduce((a, b) => a + b, 0);
    const achievementRate = totalTarget > 0 ? (totalAchieved / totalTarget * 100).toFixed(1) : 0;
    
    // العثور على أفضل شهر
    let bestMonthIndex = 0;
    let bestRate = 0;
    for (let i = 0; i < achieved.length; i++) {
        if (targets[i] > 0) {
            const rate = achieved[i] / targets[i] * 100;
            if (rate > bestRate) {
                bestRate = rate;
                bestMonthIndex = i;
            }
        }
    }
    
    document.getElementById('totalAchieved').textContent = totalAchieved.toLocaleString();
    document.getElementById('totalTarget').textContent = totalTarget.toLocaleString();
    document.getElementById('achievementRate').textContent = achievementRate + '%';
    document.getElementById('bestMonth').textContent = data.labels[bestMonthIndex] || '-';
    
    document.getElementById('quickStats').style.display = 'flex';
}

function updateTable(data) {
    if (!data.datasets || data.datasets.length < 2) return;
    
    const achieved = data.datasets[0].data;
    const targets = data.datasets[1].data;
    const labels = data.labels;
    
    let tableHTML = '';
    for (let i = 0; i < labels.length; i++) {
        const target = targets[i] || 0;
        const actual = achieved[i] || 0;
        const rate = target > 0 ? (actual / target * 100).toFixed(1) : 0;
        const diff = actual - target;
        
        tableHTML += `
            <tr>
                <td>${labels[i]}</td>
                <td>${target.toLocaleString()}</td>
                <td>${actual.toLocaleString()}</td>
                <td><span class="badge ${rate >= 100 ? 'bg-success' : rate >= 80 ? 'bg-warning' : 'bg-danger'}">${rate}%</span></td>
                <td><span class="${diff >= 0 ? 'text-success' : 'text-danger'}">${diff.toLocaleString()}</span></td>
            </tr>
        `;
    }
    
    document.getElementById('dataTableBody').innerHTML = tableHTML;
    document.getElementById('dataTable').style.display = 'block';
}

function resetFilters() {
    document.getElementById('filterForm').reset();
    document.getElementById('yearSelect').value = '2024';
    loadReport();
}

function exportChart() {
    if (reportChart) {
        const link = document.createElement('a');
        link.download = 'تقرير_بسيط.png';
        link.href = reportChart.toBase64Image();
        link.click();
    }
}
</script>
{% endblock %}
