#!/usr/bin/env python
"""
فحص أخطاء تقرير المقارنة بالأعوام السابقة
"""

import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'statistical_system.settings')
django.setup()

from django.test import Client
from django.contrib.auth.models import User
import traceback

def test_yearly_comparison_page():
    """اختبار صفحة المقارنة السنوية"""
    print("🧪 فحص صفحة المقارنة السنوية")
    print("="*40)
    
    client = Client()
    
    # تسجيل الدخول
    login_success = client.login(username='alaa1', password='alaa1123')
    
    if not login_success:
        print("❌ فشل في تسجيل الدخول")
        return False
    
    try:
        # اختبار صفحة المقارنة
        print("📄 اختبار صفحة المقارنة...")
        response = client.get('/reports/yearly-comparison/')
        
        if response.status_code == 200:
            print("✅ صفحة المقارنة تعمل")
            return True
        else:
            print(f"❌ صفحة المقارنة فشلت: {response.status_code}")
            if hasattr(response, 'content'):
                print(f"المحتوى: {response.content.decode()[:500]}...")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في صفحة المقارنة: {e}")
        traceback.print_exc()
        return False
    
    finally:
        client.logout()

def test_yearly_comparison_api():
    """اختبار API المقارنة السنوية"""
    print("\n🔌 فحص API المقارنة السنوية")
    print("-"*35)
    
    client = Client()
    
    # تسجيل الدخول
    login_success = client.login(username='alaa1', password='alaa1123')
    
    if not login_success:
        print("❌ فشل في تسجيل الدخول")
        return False
    
    try:
        # اختبار API الأساسي
        print("🔌 اختبار API الأساسي...")
        response = client.get('/reports/yearly-comparison/data/')
        
        if response.status_code == 200:
            try:
                data = response.json()
                print("✅ API يعمل ويعيد JSON")
                
                # فحص البيانات المطلوبة
                required_keys = ['labels', 'datasets', 'summary', 'growth_rates', 'performance_analysis', 'selected_years']
                
                for key in required_keys:
                    if key in data:
                        print(f"   ✅ {key}: متوفر")
                    else:
                        print(f"   ❌ {key}: غير متوفر")
                
                return True
                
            except Exception as json_error:
                print(f"❌ خطأ في تحليل JSON: {json_error}")
                print(f"المحتوى: {response.content.decode()[:500]}...")
                return False
        else:
            print(f"❌ API فشل: {response.status_code}")
            if hasattr(response, 'content'):
                print(f"المحتوى: {response.content.decode()[:500]}...")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في API: {e}")
        traceback.print_exc()
        return False
    
    finally:
        client.logout()

def check_view_imports():
    """فحص استيرادات الـ views"""
    print("\n📦 فحص استيرادات الـ views")
    print("-"*25)
    
    try:
        from stats_app.views import YearlyComparisonView, YearlyComparisonDataView
        print("✅ YearlyComparisonView: مستورد بنجاح")
        print("✅ YearlyComparisonDataView: مستورد بنجاح")
        return True
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

def check_urls():
    """فحص URLs"""
    print("\n🔗 فحص URLs")
    print("-"*15)
    
    try:
        from django.urls import reverse
        
        # فحص URL الصفحة
        try:
            url1 = reverse('stats_app:yearly_comparison')
            print(f"✅ yearly_comparison URL: {url1}")
        except Exception as e:
            print(f"❌ yearly_comparison URL: {e}")
        
        # فحص URL الـ API
        try:
            url2 = reverse('stats_app:yearly_comparison_data')
            print(f"✅ yearly_comparison_data URL: {url2}")
        except Exception as e:
            print(f"❌ yearly_comparison_data URL: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص URLs: {e}")
        return False

def check_template():
    """فحص القالب"""
    print("\n📄 فحص القالب")
    print("-"*15)
    
    try:
        from django.template.loader import get_template
        
        template = get_template('stats_app/yearly_comparison.html')
        print("✅ قالب yearly_comparison.html موجود")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في القالب: {e}")
        return False

def check_data_availability():
    """فحص توفر البيانات"""
    print("\n📊 فحص توفر البيانات")
    print("-"*20)
    
    try:
        from stats_app.models import Target, StatisticalEntry
        
        # فحص المستهدفات
        targets_count = Target.objects.count()
        targets_with_fiscal = Target.objects.filter(fiscal_year__isnull=False).count()
        
        print(f"🎯 المستهدفات: {targets_count} (مع سنة مالية: {targets_with_fiscal})")
        
        # فحص الإدخالات
        entries_count = StatisticalEntry.objects.count()
        entries_with_fiscal = StatisticalEntry.objects.filter(fiscal_year__isnull=False).count()
        
        print(f"📈 الإدخالات: {entries_count} (مع سنة مالية: {entries_with_fiscal})")
        
        # فحص السنوات المالية
        fiscal_years_targets = Target.objects.filter(fiscal_year__isnull=False).values_list('fiscal_year', flat=True).distinct()
        fiscal_years_entries = StatisticalEntry.objects.filter(fiscal_year__isnull=False).values_list('fiscal_year', flat=True).distinct()
        
        all_fiscal_years = set(fiscal_years_targets) | set(fiscal_years_entries)
        
        print(f"📅 السنوات المالية المتوفرة: {sorted(all_fiscal_years)}")
        
        if len(all_fiscal_years) >= 2:
            print("✅ يوجد سنوات كافية للمقارنة")
            return True
        else:
            print("⚠️ لا توجد سنوات كافية للمقارنة")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في فحص البيانات: {e}")
        return False

def run_manual_view_test():
    """اختبار يدوي للـ view"""
    print("\n🔧 اختبار يدوي للـ view")
    print("-"*25)
    
    try:
        from stats_app.views import YearlyComparisonDataView
        from django.http import HttpRequest
        from django.contrib.auth.models import AnonymousUser
        from stats_app.models import Company
        
        # إنشاء طلب وهمي
        request = HttpRequest()
        request.method = 'GET'
        request.GET = {}
        request.user = AnonymousUser()
        
        # محاولة إنشاء الـ view
        view = YearlyComparisonDataView()
        view.request = request
        
        print("✅ تم إنشاء الـ view بنجاح")
        
        # اختبار الدوال المساعدة
        test_summary = {
            2024: {'total_actual': 100, 'total_target': 120, 'achievement_rate': 83.33},
            2023: {'total_actual': 80, 'total_target': 100, 'achievement_rate': 80.00}
        }
        
        growth_rates = view.calculate_growth_rates(test_summary, [2023, 2024])
        print(f"✅ حساب معدلات النمو: {growth_rates}")
        
        performance = view.analyze_performance(test_summary)
        print(f"✅ تحليل الأداء: متوسط الإنجاز {performance.get('average_achievement', 'N/A')}%")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار اليدوي: {e}")
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("🔍 فحص شامل لأخطاء تقرير المقارنة السنوية")
    print("="*50)
    
    # فحص الاستيرادات
    imports_ok = check_view_imports()
    
    # فحص URLs
    urls_ok = check_urls()
    
    # فحص القالب
    template_ok = check_template()
    
    # فحص البيانات
    data_ok = check_data_availability()
    
    # اختبار يدوي للـ view
    manual_test_ok = run_manual_view_test()
    
    # اختبار الصفحة
    page_ok = test_yearly_comparison_page()
    
    # اختبار API
    api_ok = test_yearly_comparison_api()
    
    print("\n" + "="*50)
    print("📋 ملخص الفحص:")
    
    checks = [
        ("📦 الاستيرادات", imports_ok),
        ("🔗 URLs", urls_ok),
        ("📄 القالب", template_ok),
        ("📊 البيانات", data_ok),
        ("🔧 الاختبار اليدوي", manual_test_ok),
        ("📄 الصفحة", page_ok),
        ("🔌 API", api_ok)
    ]
    
    all_ok = True
    for name, result in checks:
        status = "✅ يعمل" if result else "❌ خطأ"
        print(f"   {name}: {status}")
        if not result:
            all_ok = False
    
    if all_ok:
        print("\n🎉 جميع الفحوصات نجحت!")
        print("✅ تقرير المقارنة السنوية يعمل بشكل صحيح")
    else:
        print("\n⚠️ توجد أخطاء تحتاج إصلاح")
        print("🔧 راجع التفاصيل أعلاه لمعرفة المشاكل")
    
    print("="*50)

if __name__ == '__main__':
    main()
