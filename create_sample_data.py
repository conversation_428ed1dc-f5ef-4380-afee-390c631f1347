#!/usr/bin/env python
"""
سكريبت لإنشاء بيانات تجريبية للنظام الإحصائي
"""

import os
import sys
import django
from datetime import date, datetime
from decimal import Decimal

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'statistical_system.settings')
django.setup()

from stats_app.models import Company, Item, Target, StatisticalEntry
from django.contrib.auth.models import User

def create_sample_data():
    print("إنشاء بيانات تجريبية...")
    
    # إنشاء الشركات
    companies_data = [
        {'name': 'شركة الأسمنت السعودية', 'sector': 'الصناعة'},
        {'name': 'شركة البتروكيماويات', 'sector': 'البتروكيماويات'},
        {'name': 'شركة الحديد والصلب', 'sector': 'الصناعة'},
        {'name': 'شركة الأغذية المتحدة', 'sector': 'الأغذية'},
        {'name': 'شركة النسيج الوطنية', 'sector': 'النسيج'},
    ]
    
    companies = []
    for company_data in companies_data:
        company, created = Company.objects.get_or_create(
            name=company_data['name'],
            defaults={'sector': company_data['sector']}
        )
        companies.append(company)
        if created:
            print(f"تم إنشاء الشركة: {company.name}")
    
    # إنشاء العناصر
    items_data = [
        {'name': 'الأسمنت', 'unit': 'طن'},
        {'name': 'البلاستيك', 'unit': 'كجم'},
        {'name': 'الحديد', 'unit': 'طن'},
        {'name': 'المواد الغذائية', 'unit': 'كجم'},
        {'name': 'الأقمشة', 'unit': 'متر'},
        {'name': 'الكيماويات', 'unit': 'لتر'},
    ]
    
    items = []
    for item_data in items_data:
        item, created = Item.objects.get_or_create(
            name=item_data['name'],
            defaults={'unit': item_data['unit']}
        )
        items.append(item)
        if created:
            print(f"تم إنشاء العنصر: {item.name}")
    
    # الحصول على المستخدم المدير
    admin_user = User.objects.get(username='admin')
    
    # إنشاء المستهدفات
    current_year = date.today().year
    months = range(1, 13)  # من يناير إلى ديسمبر
    
    targets_created = 0
    for company in companies:
        for item in items[:3]:  # أول 3 عناصر لكل شركة
            for month in months:
                target, created = Target.objects.get_or_create(
                    company=company,
                    item=item,
                    month=month,
                    year=current_year,
                    defaults={
                        'target_quantity': Decimal(str(1000 + (month * 100))),
                        'target_value': Decimal(str(50000 + (month * 5000))),
                        'created_by': admin_user
                    }
                )
                if created:
                    targets_created += 1
    
    print(f"تم إنشاء {targets_created} مستهدف")
    
    # إنشاء الإدخالات الإحصائية
    entries_created = 0
    for company in companies:
        for item in items[:3]:  # أول 3 عناصر لكل شركة
            for month in range(1, 7):  # أول 6 أشهر
                entry_date = date(current_year, month, 15)  # منتصف الشهر
                
                # الحصول على المستهدف المقابل
                try:
                    target = Target.objects.get(
                        company=company,
                        item=item,
                        month=month,
                        year=current_year
                    )
                    
                    # إنشاء قيم محققة (80-120% من المستهدف)
                    import random
                    achievement_ratio = random.uniform(0.8, 1.2)
                    achieved_quantity = target.target_quantity * Decimal(str(achievement_ratio))
                    achieved_value = target.target_value * Decimal(str(achievement_ratio))
                    
                    entry, created = StatisticalEntry.objects.get_or_create(
                        company=company,
                        item=item,
                        date=entry_date,
                        defaults={
                            'quantity': achieved_quantity,
                            'value': achieved_value,
                            'created_by': admin_user,
                            'notes': f'إدخال تجريبي للشهر {month}'
                        }
                    )
                    if created:
                        entries_created += 1
                        
                except Target.DoesNotExist:
                    continue
    
    print(f"تم إنشاء {entries_created} إدخال إحصائي")
    
    print("تم الانتهاء من إنشاء البيانات التجريبية بنجاح!")
    
    # طباعة ملخص
    print("\n=== ملخص البيانات ===")
    print(f"الشركات: {Company.objects.count()}")
    print(f"العناصر: {Item.objects.count()}")
    print(f"المستهدفات: {Target.objects.count()}")
    print(f"الإدخالات الإحصائية: {StatisticalEntry.objects.count()}")

if __name__ == '__main__':
    create_sample_data()
