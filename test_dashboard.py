#!/usr/bin/env python
"""
اختبار لوحة مؤشرات الأداء
"""

import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'statistical_system.settings')
django.setup()

from django.test import Client
from django.contrib.auth.models import User
import json

def test_dashboard_access():
    """اختبار الوصول للوحة المؤشرات"""
    print("🧪 اختبار الوصول للوحة مؤشرات الأداء")
    print("="*45)
    
    client = Client()
    
    # تسجيل الدخول
    login_success = client.login(username='alaa1', password='alaa1123')
    
    if not login_success:
        print("❌ فشل في تسجيل الدخول")
        return False
    
    try:
        # اختبار الصفحة الرئيسية
        print("📄 اختبار صفحة لوحة المؤشرات...")
        response = client.get('/dashboard/')
        
        if response.status_code == 200:
            print("✅ لوحة المؤشرات تعمل")
            
            # فحص السياق
            context = response.context
            if context:
                required_context = [
                    'total_companies', 'total_items', 'current_fiscal_year_targets',
                    'current_fiscal_year_entries', 'overall_achievement', 'total_target_value',
                    'total_actual_value', 'data_types_performance', 'items_statistics',
                    'best_performance', 'worst_performance', 'growth_rate', 'growth_trend'
                ]
                
                all_present = True
                for key in required_context:
                    if key in context and context[key] is not None:
                        print(f"   ✅ {key}: متوفر")
                    else:
                        print(f"   ❌ {key}: غير متوفر")
                        all_present = False
                
                return all_present
            else:
                print("   ⚠️ لا يوجد context في الاستجابة")
                return False
        else:
            print(f"❌ لوحة المؤشرات فشلت: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار لوحة المؤشرات: {e}")
        return False
    
    finally:
        client.logout()

def test_dashboard_data_accuracy():
    """اختبار دقة البيانات في لوحة المؤشرات"""
    print("\n📊 اختبار دقة البيانات")
    print("-"*25)
    
    client = Client()
    
    # تسجيل الدخول
    login_success = client.login(username='alaa1', password='alaa1123')
    
    if not login_success:
        print("❌ فشل في تسجيل الدخول")
        return False
    
    try:
        from stats_app.models import Company, Item, Target, StatisticalEntry
        from stats_app.views import DashboardView
        from django.http import HttpRequest
        from django.contrib.auth.models import User
        
        # إنشاء طلب وهمي
        request = HttpRequest()
        request.method = 'GET'
        request.user = User.objects.get(username='alaa1')
        
        # إنشاء الـ view
        view = DashboardView()
        view.request = request
        
        # اختبار الإحصائيات العامة
        print("📈 اختبار الإحصائيات العامة...")
        general_stats = view.get_general_statistics()
        
        # التحقق من البيانات
        expected_companies = view.get_user_companies().count()
        expected_items = Item.objects.filter(is_active=True).count()
        
        if general_stats['total_companies'] == expected_companies:
            print(f"   ✅ عدد الشركات صحيح: {general_stats['total_companies']}")
        else:
            print(f"   ❌ عدد الشركات خطأ: {general_stats['total_companies']} (متوقع: {expected_companies})")
        
        if general_stats['total_items'] == expected_items:
            print(f"   ✅ عدد العناصر صحيح: {general_stats['total_items']}")
        else:
            print(f"   ❌ عدد العناصر خطأ: {general_stats['total_items']} (متوقع: {expected_items})")
        
        # اختبار مؤشرات الأداء الرئيسية
        print("\n🎯 اختبار مؤشرات الأداء الرئيسية...")
        settings = view.system_settings if hasattr(view, 'system_settings') else None
        if not settings:
            from stats_app.models import SystemSettings
            settings = SystemSettings.get_settings()
        
        current_fiscal_year = settings.get_current_fiscal_year()
        kpis = view.get_key_performance_indicators(current_fiscal_year)
        
        print(f"   ✅ السنة المالية الحالية: {current_fiscal_year}")
        print(f"   ✅ أنواع البيانات المتوفرة: {len(kpis['data_types_performance'])}")
        print(f"   ✅ أفضل أداء: {kpis['best_performance']['name']} ({kpis['best_performance']['achievement']}%)")
        print(f"   ✅ أسوأ أداء: {kpis['worst_performance']['name']} ({kpis['worst_performance']['achievement']}%)")
        
        # اختبار إحصائيات العناصر
        print("\n📦 اختبار إحصائيات العناصر...")
        items_stats = view.get_items_statistics(current_fiscal_year)
        
        print(f"   ✅ عدد العناصر المحللة: {len(items_stats['items_statistics'])}")
        
        if items_stats['top_performing_item']:
            print(f"   ✅ أفضل عنصر: {items_stats['top_performing_item']['item'].name} ({items_stats['top_performing_item']['achievement']}%)")
        
        if items_stats['lowest_performing_item']:
            print(f"   ✅ أقل عنصر أداءً: {items_stats['lowest_performing_item']['item'].name} ({items_stats['lowest_performing_item']['achievement']}%)")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار دقة البيانات: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        client.logout()

def test_dashboard_performance():
    """اختبار أداء لوحة المؤشرات"""
    print("\n⚡ اختبار أداء لوحة المؤشرات")
    print("-"*30)
    
    import time
    
    client = Client()
    
    # تسجيل الدخول
    login_success = client.login(username='alaa1', password='alaa1123')
    
    if not login_success:
        print("❌ فشل في تسجيل الدخول")
        return False
    
    try:
        # قياس وقت الاستجابة
        start_time = time.time()
        response = client.get('/dashboard/')
        end_time = time.time()
        
        response_time = end_time - start_time
        
        print(f"⏱️ وقت الاستجابة: {response_time:.2f} ثانية")
        
        if response_time < 2.0:
            print("✅ الأداء ممتاز (أقل من ثانيتين)")
            return True
        elif response_time < 5.0:
            print("⚠️ الأداء مقبول (أقل من 5 ثوان)")
            return True
        else:
            print("❌ الأداء بطيء (أكثر من 5 ثوان)")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الأداء: {e}")
        return False
    
    finally:
        client.logout()

def test_dashboard_responsiveness():
    """اختبار استجابة لوحة المؤشرات"""
    print("\n📱 اختبار الاستجابة")
    print("-"*20)
    
    client = Client()
    
    # تسجيل الدخول
    login_success = client.login(username='alaa1', password='alaa1123')
    
    if not login_success:
        print("❌ فشل في تسجيل الدخول")
        return False
    
    try:
        # اختبار مع user agents مختلفة
        user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',  # Desktop
            'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)',        # Mobile
            'Mozilla/5.0 (iPad; CPU OS 14_0 like Mac OS X)',                 # Tablet
        ]
        
        all_responsive = True
        
        for i, ua in enumerate(user_agents):
            device_type = ['Desktop', 'Mobile', 'Tablet'][i]
            
            response = client.get('/dashboard/', HTTP_USER_AGENT=ua)
            
            if response.status_code == 200:
                print(f"   ✅ {device_type}: يعمل بشكل صحيح")
            else:
                print(f"   ❌ {device_type}: فشل ({response.status_code})")
                all_responsive = False
        
        return all_responsive
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الاستجابة: {e}")
        return False
    
    finally:
        client.logout()

def main():
    """الدالة الرئيسية"""
    print("🔍 اختبار شامل للوحة مؤشرات الأداء")
    print("="*45)
    
    # اختبار الوصول
    access_ok = test_dashboard_access()
    
    # اختبار دقة البيانات
    data_accuracy_ok = test_dashboard_data_accuracy()
    
    # اختبار الأداء
    performance_ok = test_dashboard_performance()
    
    # اختبار الاستجابة
    responsiveness_ok = test_dashboard_responsiveness()
    
    print("\n" + "="*45)
    print("📋 ملخص الاختبارات:")
    
    tests = [
        ("🔐 الوصول للوحة المؤشرات", access_ok),
        ("📊 دقة البيانات", data_accuracy_ok),
        ("⚡ الأداء", performance_ok),
        ("📱 الاستجابة", responsiveness_ok)
    ]
    
    all_working = True
    for name, result in tests:
        status = "✅ يعمل" if result else "❌ خطأ"
        print(f"   {name}: {status}")
        if not result:
            all_working = False
    
    if all_working:
        print("\n🎉 جميع اختبارات لوحة المؤشرات نجحت!")
        print("✅ لوحة مؤشرات الأداء تعمل بشكل مثالي")
        
        print("\n🌐 للاختبار:")
        print("   📊 لوحة المؤشرات: http://127.0.0.1:8000/dashboard/")
        
        print("\n💡 المميزات المتاحة:")
        print("   ✅ عدادات للعناصر والشركات")
        print("   ✅ مؤشرات الأداء الرئيسية")
        print("   ✅ إحصائيات أنواع البيانات")
        print("   ✅ تحليل الاتجاهات والنمو")
        print("   ✅ واجهة تفاعلية وجميلة")
        print("   ✅ استجابة لجميع الأجهزة")
        
    else:
        print("\n⚠️ لا تزال هناك بعض المشاكل")
        print("🔧 راجع التفاصيل أعلاه")
    
    print("="*45)

if __name__ == '__main__':
    main()
