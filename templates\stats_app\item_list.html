{% extends 'base.html' %}

{% block title %}قائمة العناصر - نظام الإحصائيات الشهرية{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3">
                    <i class="fas fa-boxes me-2"></i>
                    قائمة العناصر
                </h1>
                <a href="{% url 'stats_app:item_create' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    إضافة عنصر جديد
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    {% if items %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>اسم العنصر</th>
                                        <th>وحدة القياس</th>
                                        <th>الوصف</th>
                                        <th>الحالة</th>
                                        <th>تاريخ الإنشاء</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for item in items %}
                                    <tr>
                                        <td>
                                            <strong>{{ item.name }}</strong>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">{{ item.unit }}</span>
                                        </td>
                                        <td>{{ item.description|default:"غير محدد"|truncatechars:50 }}</td>
                                        <td>
                                            {% if item.is_active %}
                                                <span class="badge bg-success">نشط</span>
                                            {% else %}
                                                <span class="badge bg-secondary">غير نشط</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ item.created_at|date:"Y-m-d" }}</td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{% url 'stats_app:item_update' item.pk %}" 
                                                   class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="{% url 'stats_app:item_delete' item.pk %}" 
                                                   class="btn btn-sm btn-outline-danger">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        {% if is_paginated %}
                            <nav aria-label="Page navigation">
                                <ul class="pagination justify-content-center">
                                    {% if page_obj.has_previous %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page=1">الأولى</a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}">السابقة</a>
                                        </li>
                                    {% endif %}

                                    <li class="page-item active">
                                        <span class="page-link">
                                            صفحة {{ page_obj.number }} من {{ page_obj.paginator.num_pages }}
                                        </span>
                                    </li>

                                    {% if page_obj.has_next %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.next_page_number }}">التالية</a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">الأخيرة</a>
                                        </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-boxes fa-3x text-muted mb-3"></i>
                            <h4 class="text-muted">لا توجد عناصر مسجلة</h4>
                            <p class="text-muted">ابدأ بإضافة عنصر جديد للنظام</p>
                            <a href="{% url 'stats_app:item_create' %}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>
                                إضافة عنصر جديد
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
