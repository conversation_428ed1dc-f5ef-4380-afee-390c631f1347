{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}
    {% if object %}تعديل المستهدف{% else %}إضافة مستهدف جديد{% endif %} - نظام الإحصائيات الشهرية
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3">
                    <i class="fas fa-bullseye me-2"></i>
                    {% if object %}تعديل المستهدف{% else %}إضافة مستهدف جديد{% endif %}
                </h1>
                <a href="{% url 'stats_app:target_list' %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>
                    العودة للقائمة
                </a>
            </div>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        {% if object %}تعديل بيانات المستهدف{% else %}بيانات المستهدف الجديد{% endif %}
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        {% crispy form %}
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
