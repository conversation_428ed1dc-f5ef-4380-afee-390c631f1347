#!/usr/bin/env python
"""
سكريبت لإصلاح مشكلة المستخدم alaa
"""

import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'statistical_system.settings')
django.setup()

from django.contrib.auth.models import User
from stats_app.models import UserProfile, Company
from django.contrib.auth import authenticate

def check_alaa_user():
    """فحص حالة المستخدم alaa"""
    print("🔍 فحص حالة المستخدم alaa")
    print("="*40)
    
    try:
        user = User.objects.get(username='alaa')
        print(f"✅ المستخدم موجود: {user.username}")
        print(f"   📧 البريد: {user.email or 'غير محدد'}")
        print(f"   👤 الاسم: {user.first_name} {user.last_name}")
        print(f"   🔑 مدير عام: {'نعم' if user.is_superuser else 'لا'}")
        print(f"   🔑 موظف: {'نعم' if user.is_staff else 'لا'}")
        print(f"   ✅ نشط: {'نعم' if user.is_active else 'لا'}")
        print(f"   📅 تاريخ الإنشاء: {user.date_joined.strftime('%Y-%m-%d %H:%M')}")
        
        # فحص الملف الشخصي
        try:
            profile = user.userprofile
            print(f"   📁 ملف شخصي: موجود")
            print(f"   🏢 مدير النظام: {'نعم' if profile.is_system_admin else 'لا'}")
            print(f"   🏭 مستخدم شركة: {'نعم' if profile.is_company_user else 'لا'}")
            if profile.company:
                print(f"   🏢 الشركة: {profile.company.name}")
            else:
                print(f"   🏢 الشركة: غير محدد")
        except UserProfile.DoesNotExist:
            print(f"   ❌ ملف شخصي: غير موجود")
        
        return user
        
    except User.DoesNotExist:
        print("❌ المستخدم alaa غير موجود")
        return None

def test_alaa_passwords():
    """اختبار كلمات مرور مختلفة للمستخدم alaa"""
    print("\n🔐 اختبار كلمات المرور المحتملة")
    print("="*40)
    
    possible_passwords = [
        "alaa",
        "123456",
        "alaa123",
        "password",
        "admin",
        "alaa1234",
        "123",
        "alaa2024",
        "alaa2025"
    ]
    
    for password in possible_passwords:
        user = authenticate(username='alaa', password=password)
        if user:
            print(f"✅ كلمة المرور الصحيحة: {password}")
            return password
        else:
            print(f"❌ كلمة مرور خاطئة: {password}")
    
    print("\n⚠️ لم يتم العثور على كلمة المرور الصحيحة")
    return None

def reset_alaa_password():
    """إعادة تعيين كلمة مرور المستخدم alaa"""
    print("\n🔧 إعادة تعيين كلمة مرور المستخدم alaa")
    print("="*40)
    
    try:
        user = User.objects.get(username='alaa')
        
        # تعيين كلمة مرور جديدة
        new_password = "alaa123"
        user.set_password(new_password)
        user.save()
        
        print(f"✅ تم تحديث كلمة المرور بنجاح")
        print(f"   اسم المستخدم: alaa")
        print(f"   كلمة المرور الجديدة: {new_password}")
        
        # اختبار كلمة المرور الجديدة
        test_user = authenticate(username='alaa', password=new_password)
        if test_user:
            print(f"✅ تم اختبار كلمة المرور الجديدة بنجاح")
            return True
        else:
            print(f"❌ فشل في اختبار كلمة المرور الجديدة")
            return False
            
    except User.DoesNotExist:
        print("❌ المستخدم alaa غير موجود")
        return False

def fix_alaa_profile():
    """إصلاح ملف المستخدم alaa الشخصي"""
    print("\n📁 إصلاح الملف الشخصي للمستخدم alaa")
    print("="*40)
    
    try:
        user = User.objects.get(username='alaa')
        
        # التحقق من وجود الملف الشخصي
        try:
            profile = user.userprofile
            print("✅ الملف الشخصي موجود")
        except UserProfile.DoesNotExist:
            # إنشاء ملف شخصي جديد
            profile = UserProfile.objects.create(user=user)
            print("✅ تم إنشاء ملف شخصي جديد")
        
        # تحديث الملف الشخصي
        profile.is_system_admin = True
        profile.is_company_user = False
        
        # ربط بشركة إذا كانت متوفرة
        if Company.objects.exists():
            profile.company = Company.objects.first()
            print(f"✅ تم ربط المستخدم بالشركة: {profile.company.name}")
        
        profile.save()
        print("✅ تم تحديث الملف الشخصي")
        
        return True
        
    except User.DoesNotExist:
        print("❌ المستخدم alaa غير موجود")
        return False

def create_alaa_if_missing():
    """إنشاء المستخدم alaa إذا كان مفقوداً"""
    print("\n👤 إنشاء المستخدم alaa إذا كان مفقوداً")
    print("="*40)
    
    if User.objects.filter(username='alaa').exists():
        print("✅ المستخدم alaa موجود بالفعل")
        return True
    
    # إنشاء المستخدم
    user = User.objects.create_user(
        username='alaa',
        password='alaa123',
        email='<EMAIL>',
        first_name='علاء',
        last_name='المستخدم'
    )
    
    print(f"✅ تم إنشاء المستخدم: alaa")
    print(f"   كلمة المرور: alaa123")
    
    # إنشاء الملف الشخصي
    try:
        profile = user.userprofile
        profile.is_system_admin = True
        profile.is_company_user = False
        
        if Company.objects.exists():
            profile.company = Company.objects.first()
        
        profile.save()
        print("✅ تم إنشاء الملف الشخصي")
        
    except UserProfile.DoesNotExist:
        UserProfile.objects.create(
            user=user,
            is_system_admin=True,
            is_company_user=False,
            company=Company.objects.first() if Company.objects.exists() else None
        )
        print("✅ تم إنشاء الملف الشخصي")
    
    return True

def test_final_login():
    """اختبار تسجيل الدخول النهائي"""
    print("\n🧪 اختبار تسجيل الدخول النهائي")
    print("="*40)
    
    user = authenticate(username='alaa', password='alaa123')
    
    if user:
        print("✅ تسجيل الدخول ناجح!")
        print(f"   👤 المستخدم: {user.username}")
        print(f"   📧 البريد: {user.email}")
        
        try:
            profile = user.userprofile
            print(f"   🏢 مدير النظام: {'نعم' if profile.is_system_admin else 'لا'}")
            print(f"   🏭 مستخدم شركة: {'نعم' if profile.is_company_user else 'لا'}")
            if profile.company:
                print(f"   🏢 الشركة: {profile.company.name}")
        except:
            print("   ⚠️ مشكلة في الملف الشخصي")
        
        return True
    else:
        print("❌ فشل في تسجيل الدخول")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 إصلاح مشكلة المستخدم alaa")
    print("="*50)
    
    # فحص المستخدم
    user = check_alaa_user()
    
    if user:
        # اختبار كلمات المرور
        correct_password = test_alaa_passwords()
        
        if not correct_password:
            # إعادة تعيين كلمة المرور
            reset_alaa_password()
        
        # إصلاح الملف الشخصي
        fix_alaa_profile()
    else:
        # إنشاء المستخدم إذا كان مفقوداً
        create_alaa_if_missing()
    
    # اختبار نهائي
    success = test_final_login()
    
    print("\n" + "="*50)
    if success:
        print("🎉 تم إصلاح المستخدم alaa بنجاح!")
        print("\n📋 معلومات تسجيل الدخول:")
        print("   اسم المستخدم: alaa")
        print("   كلمة المرور: alaa123")
        print("   الرابط: http://127.0.0.1:8000/accounts/login/")
    else:
        print("❌ فشل في إصلاح المستخدم alaa")
    print("="*50)

if __name__ == '__main__':
    main()
