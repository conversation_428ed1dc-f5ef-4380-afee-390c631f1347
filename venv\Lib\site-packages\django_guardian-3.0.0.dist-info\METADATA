Metadata-Version: 2.4
Name: django-guardian
Version: 3.0.0
Summary: Per object permissions for Django
Author: <PERSON>, Bona Fide IT GmbH, Columbia University, SIS Development and Application Services
Author-email: <PERSON><PERSON><PERSON> <l<PERSON><PERSON><PERSON>@gmail.com>, <PERSON><PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>, <PERSON><PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON> <<EMAIL>>, <PERSON><PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>, <PERSON> <alilo<PERSON>@gmail.com>, <PERSON><PERSON> <<EMAIL>>, <PERSON><PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>, <PERSON><PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON>-<PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON> <<EMAIL>>, Jameel Al-<PERSON> <<EMAIL>>, "<PERSON> P. <PERSON>" <<EMAIL>>, <PERSON> Madsack <<EMAIL>>, <PERSON> Kharlamov <<EMAIL>>, <PERSON> de <PERSON>-Borro <<EMAIL>>, Jan Nakladal <<EMAIL>>, Yonel Ceruto <<EMAIL>>, Luke Faraone <<EMAIL>>, John Wegis <<EMAIL>>, Florentin Sardan <<EMAIL>>, Geoff Greer <<EMAIL>>, Hans Larsen <<EMAIL>>, "Fabio C. Barrionuevo da Luz" <<EMAIL>>, Tomasz Wsuł <<EMAIL>>, Xavier Ordoquy <<EMAIL>>, Joshua Bonnett <<EMAIL>>, Jernej Kos <<EMAIL>>, Bruno Ribeiro da Silva <<EMAIL>>, Cezar Jenkins <<EMAIL>>, Warren Volz <<EMAIL>>, Omer Katz <<EMAIL>>, Vishal Lal <<EMAIL>>, Steven DeMartini <<EMAIL>>, zauddelig <<EMAIL>>, Remco Wendt <<EMAIL>>, Kevin London <<EMAIL>>, Kouhei Maeda <<EMAIL>>, Samuel Sutch <<EMAIL>>, Morgan Aubert <<EMAIL>>, Brian May <<EMAIL>>, Troy Grosfield <<EMAIL>>, Michael Drescher <<EMAIL>>, Verena Jaspersen <<EMAIL>>, Bertrand Svetchine <<EMAIL>>, Frank Wickström <<EMAIL>>, George Karakostas <<EMAIL>>, Adam Dobrawy <<EMAIL>>, Jeff Hackshaw <<EMAIL>>, Chase Bennett <<EMAIL>>, Jonny Arnold <<EMAIL>>, Davis Raymond Muro <<EMAIL>>, Richard de Wit <<EMAIL>>, Pedro Rojas Gavidia <<EMAIL>>, Rainshaw Gao <<EMAIL>>, Tom Clark <<EMAIL>>, Meredith Hoo <<EMAIL>>
Maintainer: Michael K., Chris Maggiuli, Bona Fide IT GmbH
Maintainer-email: Tom Clark <<EMAIL>>, Brian May <<EMAIL>>, Lukasz Balcerzak <<EMAIL>>, Adam Dobrawy <<EMAIL>>
License: BSD-2-Clause
Project-URL: Homepage, https://github.com/django-guardian/django-guardian
Project-URL: Documentation, https://django-guardian.readthedocs.io/
Project-URL: Repository, https://github.com/django-guardian/django-guardian
Project-URL: Issues, https://github.com/me/spam/issues
Project-URL: Changelog, https://github.com/django-guardian/django-guardian/releases
Keywords: django,permissions,authorization,object,row,level
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: Environment :: Web Environment
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Classifier: License :: OSI Approved :: BSD License
Classifier: Framework :: Django
Classifier: Framework :: Django :: 3.2
Classifier: Framework :: Django :: 4.1
Classifier: Framework :: Django :: 4.2
Classifier: Framework :: Django :: 5.0
Classifier: Framework :: Django :: 5.1
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Operating System :: OS Independent
Classifier: Topic :: Security
Requires-Python: >=3.9
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: django>=3.2
Dynamic: license-file

# django-guardian

[![Tests](https://github.com/django-guardian/django-guardian/workflows/Tests/badge.svg?branch=devel)](https://github.com/django-guardian/django-guardian/actions/workflows/tests.yml)
[![PyPI version](https://img.shields.io/pypi/v/django-guardian.svg)](https://pypi.python.org/pypi/django-guardian)
[![Python versions](https://img.shields.io/pypi/pyversions/django-guardian.svg)](https://pypi.python.org/pypi/django-guardian)

`django-guardian` is an implementation of _per-object permissions_ on top
of Django’s authorization backend. Read an introduction to per-object permissions [on djangoadvent articles](https://github.com/djangoadvent/djangoadvent-articles/blob/master/1.2/06_object-permissions.rst).

## Documentation

Online documentation is available at [https://django-guardian.readthedocs.io/](https://django-guardian.readthedocs.io/).


## Installation

To install `django-guardian` into your project run:

```bash
uv add django-guardian
```
> **TIP**: Not using a package manager like `uv` or `poetry` for your django project? You probably should try them :). In the meantime, `pip install django-guardian` works just fine too.


## Configuration

We need to hook `django-guardian`` into our project.

1. Put `guardian` into your `INSTALLED_APPS` at settings module:

```python
    INSTALLED_APPS = (
     ...
     'guardian',
    )
```

2. Add extra authorization backend to your `settings.py`:

```py
    AUTHENTICATION_BACKENDS = (
        'django.contrib.auth.backends.ModelBackend',
        'guardian.backends.ObjectPermissionBackend',
    )
```

3. Create `guardian` database tables by running::

```
     python manage.py migrate
```

## Usage

After installation and project hooks we can finally use object permissions
with Django.

Lets start really quickly:

```py
      >>> from django.contrib.auth.models import User, Group
      >>> jack = User.objects.create_user('jack', '<EMAIL>', 'topsecretagentjack')
      >>> admins = Group.objects.create(name='admins')
      >>> jack.has_perm('change_group', admins)
      False
      >>> from guardian.shortcuts import assign_perm
      >>> assign_perm('change_group', jack, obj=admins)
      <UserObjectPermission: admins | jack | change_group>
      >>> jack.has_perm('change_group', admins)
      True
```

Of course our agent jack here would not be able to _change_group_ globally:

```py

    >>> jack.has_perm('change_group')
    False
```

## Admin integration

Replace `admin.ModelAdmin` with `GuardedModelAdmin` for those models
which should have object permissions support within admin panel.

For example:

```py
    from django.contrib import admin
    from myapp.models import Author
    from guardian.admin import GuardedModelAdmin

    # Old way:
    #class AuthorAdmin(admin.ModelAdmin):
    #    pass

    # With object permissions support
    class AuthorAdmin(GuardedModelAdmin):
        pass

    admin.site.register(Author, AuthorAdmin)
```

## Django Unfold integration

Users of [`django-unfold`](https://unfoldadmin.com/) will find that `guardian` is [supported out of the box via a `contrib` module](https://unfoldadmin.com/docs/integrations/django-guardian/).
