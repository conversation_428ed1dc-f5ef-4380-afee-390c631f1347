#!/usr/bin/env python
"""
سكريپت شامل لتطبيق فصل البيانات على جميع العروض
"""

import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'statistical_system.settings')
django.setup()

def apply_remaining_updates():
    """تطبيق التحديثات المتبقية"""
    
    views_file_path = 'stats_app/views.py'
    
    with open(views_file_path, 'r', encoding='utf-8') as file:
        content = file.read()
    
    # قائمة التحديثات المطلوبة
    updates = [
        # get_data_types_comparison
        {
            'search': 'def get_data_types_comparison(self):',
            'old_pattern': 'entries = StatisticalEntry.objects.filter(date__year=year)\n        targets = Target.objects.filter(year=year)',
            'new_pattern': '''# التحقق من صحة معرف الشركة
        if company_id:
            allowed_companies = self.get_user_companies()
            if not allowed_companies.filter(id=company_id).exists():
                company_id = None

        entries = self.filter_queryset_by_company(StatisticalEntry.objects.filter(date__year=year))
        targets = self.filter_queryset_by_company(Target.objects.filter(year=year))'''
        },
        
        # get_company_data_types
        {
            'search': 'def get_company_data_types(self):',
            'old_pattern': 'companies = Company.objects.filter(is_active=True)',
            'new_pattern': 'companies = self.get_user_companies().filter(is_active=True)'
        },
        
        # get_achievement_analysis
        {
            'search': 'def get_achievement_analysis(self):',
            'old_pattern': 'entries = StatisticalEntry.objects.filter(date__year=year)\n        targets = Target.objects.filter(year=year)',
            'new_pattern': '''# التحقق من صحة معرف الشركة
        if company_id:
            allowed_companies = self.get_user_companies()
            if not allowed_companies.filter(id=company_id).exists():
                company_id = None

        entries = self.filter_queryset_by_company(StatisticalEntry.objects.filter(date__year=year))
        targets = self.filter_queryset_by_company(Target.objects.filter(year=year))'''
        },
        
        # get_trend_analysis
        {
            'search': 'def get_trend_analysis(self):',
            'old_pattern': 'entries = StatisticalEntry.objects.filter(date__year=year)\n        targets = Target.objects.filter(year=year)',
            'new_pattern': '''# التحقق من صحة معرف الشركة
        if company_id:
            allowed_companies = self.get_user_companies()
            if not allowed_companies.filter(id=company_id).exists():
                company_id = None

        entries = self.filter_queryset_by_company(StatisticalEntry.objects.filter(date__year=year))
        targets = self.filter_queryset_by_company(Target.objects.filter(year=year))'''
        },
        
        # get_capacity_utilization
        {
            'search': 'def get_capacity_utilization(self):',
            'old_pattern': 'entries = StatisticalEntry.objects.filter(date__year=year)',
            'new_pattern': '''# التحقق من صحة معرف الشركة
        if company_id:
            allowed_companies = self.get_user_companies()
            if not allowed_companies.filter(id=company_id).exists():
                company_id = None

        entries = self.filter_queryset_by_company(StatisticalEntry.objects.filter(date__year=year))'''
        },
        
        # get_kpi_dashboard
        {
            'search': 'def get_kpi_dashboard(self):',
            'old_pattern': 'entries = StatisticalEntry.objects.filter(date__year=year)\n        targets = Target.objects.filter(year=year)',
            'new_pattern': '''# التحقق من صحة معرف الشركة
        if company_id:
            allowed_companies = self.get_user_companies()
            if not allowed_companies.filter(id=company_id).exists():
                company_id = None

        entries = self.filter_queryset_by_company(StatisticalEntry.objects.filter(date__year=year))
        targets = self.filter_queryset_by_company(Target.objects.filter(year=year))'''
        },
        
        # get_variance_analysis
        {
            'search': 'def get_variance_analysis(self):',
            'old_pattern': 'entries = StatisticalEntry.objects.filter(date__year=year)\n        targets = Target.objects.filter(year=year)',
            'new_pattern': '''# التحقق من صحة معرف الشركة
        if company_id:
            allowed_companies = self.get_user_companies()
            if not allowed_companies.filter(id=company_id).exists():
                company_id = None

        entries = self.filter_queryset_by_company(StatisticalEntry.objects.filter(date__year=year))
        targets = self.filter_queryset_by_company(Target.objects.filter(year=year))'''
        },
        
        # get_efficiency_metrics
        {
            'search': 'def get_efficiency_metrics(self):',
            'old_pattern': 'companies = Company.objects.filter(is_active=True)',
            'new_pattern': 'companies = self.get_user_companies().filter(is_active=True)'
        },
        
        # get_seasonal_analysis
        {
            'search': 'def get_seasonal_analysis(self):',
            'old_pattern': 'entries = StatisticalEntry.objects.filter(date__year=year)',
            'new_pattern': '''# التحقق من صحة معرف الشركة
        if company_id:
            allowed_companies = self.get_user_companies()
            if not allowed_companies.filter(id=company_id).exists():
                company_id = None

        entries = self.filter_queryset_by_company(StatisticalEntry.objects.filter(date__year=year))'''
        },
        
        # get_performance_ranking
        {
            'search': 'def get_performance_ranking(self):',
            'old_pattern': 'companies = Company.objects.filter(is_active=True)',
            'new_pattern': 'companies = self.get_user_companies().filter(is_active=True)'
        },
        
        # get_growth_analysis
        {
            'search': 'def get_growth_analysis(self):',
            'old_pattern': 'companies = Company.objects.filter(is_active=True)',
            'new_pattern': 'companies = self.get_user_companies().filter(is_active=True)'
        },
        
        # get_risk_assessment
        {
            'search': 'def get_risk_assessment(self):',
            'old_pattern': 'companies = Company.objects.filter(is_active=True)',
            'new_pattern': 'companies = self.get_user_companies().filter(is_active=True)'
        },
        
        # get_forecasting_data
        {
            'search': 'def get_forecasting_data(self):',
            'old_pattern': 'entries = StatisticalEntry.objects.filter(date__year__in=[year-1, year])',
            'new_pattern': '''# التحقق من صحة معرف الشركة
        if company_id:
            allowed_companies = self.get_user_companies()
            if not allowed_companies.filter(id=company_id).exists():
                company_id = None

        entries = self.filter_queryset_by_company(StatisticalEntry.objects.filter(date__year__in=[year-1, year]))'''
        },
        
        # get_benchmark_analysis
        {
            'search': 'def get_benchmark_analysis(self):',
            'old_pattern': 'companies = Company.objects.filter(is_active=True)',
            'new_pattern': 'companies = self.get_user_companies().filter(is_active=True)'
        },
        
        # get_correlation_matrix
        {
            'search': 'def get_correlation_matrix(self):',
            'old_pattern': 'entries = StatisticalEntry.objects.filter(date__year=year)',
            'new_pattern': '''# التحقق من صحة معرف الشركة
        if company_id:
            allowed_companies = self.get_user_companies()
            if not allowed_companies.filter(id=company_id).exists():
                company_id = None

        entries = self.filter_queryset_by_company(StatisticalEntry.objects.filter(date__year=year))'''
        }
    ]
    
    updated_count = 0
    
    for update in updates:
        # البحث عن الدالة
        method_start = content.find(update['search'])
        if method_start != -1:
            # البحث عن النمط القديم بعد بداية الدالة
            pattern_start = content.find(update['old_pattern'], method_start)
            if pattern_start != -1:
                # استبدال النمط
                pattern_end = pattern_start + len(update['old_pattern'])
                content = content[:pattern_start] + update['new_pattern'] + content[pattern_end:]
                updated_count += 1
                print(f"✅ تم تحديث: {update['search']}")
            else:
                print(f"⚠️ لم يتم العثور على النمط في: {update['search']}")
        else:
            print(f"❌ لم يتم العثور على الدالة: {update['search']}")
    
    # كتابة الملف المحدث
    with open(views_file_path, 'w', encoding='utf-8') as file:
        file.write(content)
    
    print(f"\n📊 تم تطبيق {updated_count} تحديث من أصل {len(updates)}")
    
    return updated_count

def test_data_separation():
    """اختبار فصل البيانات"""
    print("\n🧪 اختبار فصل البيانات...")
    
    from django.contrib.auth.models import User
    from stats_app.models import UserProfile, Company, StatisticalEntry
    
    # اختبار مستخدم شركة
    try:
        user = User.objects.get(username='alaa1')
        profile = user.userprofile
        
        if profile.is_company_user and profile.company:
            # عد الإدخالات الإجمالية
            total_entries = StatisticalEntry.objects.count()
            
            # عد إدخالات الشركة
            company_entries = StatisticalEntry.objects.filter(company=profile.company).count()
            
            print(f"👤 المستخدم: {user.username}")
            print(f"🏢 الشركة: {profile.company.name}")
            print(f"📊 إجمالي الإدخالات: {total_entries}")
            print(f"📊 إدخالات الشركة: {company_entries}")
            
            if company_entries < total_entries:
                print("✅ فصل البيانات يعمل بشكل صحيح")
                return True
            else:
                print("⚠️ قد لا يعمل فصل البيانات بشكل صحيح")
                return False
        else:
            print("⚠️ المستخدم ليس مستخدم شركة")
            return False
            
    except User.DoesNotExist:
        print("❌ المستخدم alaa1 غير موجود")
        return False
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 تطبيق فصل البيانات الشامل")
    print("="*60)
    
    # تطبيق التحديثات
    updated_count = apply_remaining_updates()
    
    # اختبار فصل البيانات
    test_success = test_data_separation()
    
    print("\n" + "="*60)
    print("📋 ملخص التطبيق:")
    print(f"✅ تم تحديث {updated_count} دالة JSON API")
    print(f"✅ فصل البيانات: {'يعمل' if test_success else 'يحتاج مراجعة'}")
    
    print("\n🎯 النتيجة:")
    if updated_count > 10 and test_success:
        print("🎉 تم تطبيق فصل البيانات بنجاح!")
        print("✅ كل مستخدم سيرى بيانات شركته فقط")
    else:
        print("⚠️ قد تحتاج بعض التحديثات إلى مراجعة يدوية")
    
    print("\n📋 الاختبارات المطلوبة:")
    print("1. تسجيل الدخول بمستخدم alaa1 (مستخدم شركة)")
    print("2. التحقق من أن الرسوم البيانية تظهر بيانات الشركة فقط")
    print("3. تسجيل الدخول بمستخدم alaa (مدير نظام)")
    print("4. التحقق من أن المدير يرى جميع البيانات")
    print("="*60)

if __name__ == '__main__':
    main()
