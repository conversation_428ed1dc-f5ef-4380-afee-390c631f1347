from django import forms
from django.core.exceptions import ValidationError
from crispy_forms.helper import FormHelper
from crispy_forms.layout import Layout, Submit, Row, Column, Div
from crispy_forms.bootstrap import FormActions
from .models import Company, Item, Target, StatisticalEntry
from datetime import date


class CompanyForm(forms.ModelForm):
    class Meta:
        model = Company
        fields = ['name', 'sector', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'اسم الشركة'}),
            'sector': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'القطاع'}),
        }
        labels = {
            'name': 'اسم الشركة',
            'sector': 'القطاع',
            'is_active': 'نشط',
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.layout = Layout(
            Row(
                Column('name', css_class='form-group col-md-8 mb-0'),
                Column('sector', css_class='form-group col-md-4 mb-0'),
                css_class='form-row'
            ),
            'is_active',
            FormActions(
                Submit('submit', 'حفظ', css_class='btn btn-primary'),
            )
        )


class ItemForm(forms.ModelForm):
    class Meta:
        model = Item
        fields = ['name', 'unit', 'description', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'اسم العنصر'}),
            'unit': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'وحدة القياس'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'الوصف'}),
        }
        labels = {
            'name': 'اسم العنصر',
            'unit': 'وحدة القياس',
            'description': 'الوصف',
            'is_active': 'نشط',
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.layout = Layout(
            Row(
                Column('name', css_class='form-group col-md-8 mb-0'),
                Column('unit', css_class='form-group col-md-4 mb-0'),
                css_class='form-row'
            ),
            'description',
            'is_active',
            FormActions(
                Submit('submit', 'حفظ', css_class='btn btn-primary'),
            )
        )


class TargetForm(forms.ModelForm):
    class Meta:
        model = Target
        fields = ['company', 'item', 'month', 'year', 'target_quantity', 'target_value']
        widgets = {
            'company': forms.Select(attrs={'class': 'form-control'}),
            'item': forms.Select(attrs={'class': 'form-control'}),
            'month': forms.Select(attrs={'class': 'form-control'}),
            'year': forms.NumberInput(attrs={'class': 'form-control', 'min': 2020, 'max': 2030}),
            'target_quantity': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0'}),
            'target_value': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0'}),
        }
        labels = {
            'company': 'الشركة',
            'item': 'العنصر',
            'month': 'الشهر',
            'year': 'السنة',
            'target_quantity': 'الكمية المستهدفة',
            'target_value': 'القيمة المستهدفة',
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['year'].initial = date.today().year
        self.fields['month'].initial = date.today().month
        
        self.helper = FormHelper()
        self.helper.layout = Layout(
            Row(
                Column('company', css_class='form-group col-md-6 mb-0'),
                Column('item', css_class='form-group col-md-6 mb-0'),
                css_class='form-row'
            ),
            Row(
                Column('month', css_class='form-group col-md-6 mb-0'),
                Column('year', css_class='form-group col-md-6 mb-0'),
                css_class='form-row'
            ),
            Row(
                Column('target_quantity', css_class='form-group col-md-6 mb-0'),
                Column('target_value', css_class='form-group col-md-6 mb-0'),
                css_class='form-row'
            ),
            FormActions(
                Submit('submit', 'حفظ', css_class='btn btn-primary'),
            )
        )

    def clean(self):
        cleaned_data = super().clean()
        company = cleaned_data.get('company')
        item = cleaned_data.get('item')
        month = cleaned_data.get('month')
        year = cleaned_data.get('year')

        if company and item and month and year:
            # التحقق من عدم وجود مستهدف مكرر
            existing = Target.objects.filter(
                company=company, item=item, month=month, year=year
            )
            if self.instance.pk:
                existing = existing.exclude(pk=self.instance.pk)
            
            if existing.exists():
                raise ValidationError('يوجد مستهدف مسجل مسبقاً لهذه الشركة والعنصر في نفس الشهر والسنة')

        return cleaned_data


class StatisticalEntryForm(forms.ModelForm):
    class Meta:
        model = StatisticalEntry
        fields = ['company', 'item', 'date', 'quantity', 'value', 'notes']
        widgets = {
            'company': forms.Select(attrs={'class': 'form-control'}),
            'item': forms.Select(attrs={'class': 'form-control'}),
            'date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'quantity': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0'}),
            'value': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'ملاحظات'}),
        }
        labels = {
            'company': 'الشركة',
            'item': 'العنصر',
            'date': 'التاريخ',
            'quantity': 'الكمية المحققة',
            'value': 'القيمة المحققة',
            'notes': 'ملاحظات',
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['date'].initial = date.today()
        
        self.helper = FormHelper()
        self.helper.layout = Layout(
            Row(
                Column('company', css_class='form-group col-md-6 mb-0'),
                Column('item', css_class='form-group col-md-6 mb-0'),
                css_class='form-row'
            ),
            'date',
            Row(
                Column('quantity', css_class='form-group col-md-6 mb-0'),
                Column('value', css_class='form-group col-md-6 mb-0'),
                css_class='form-row'
            ),
            'notes',
            FormActions(
                Submit('submit', 'حفظ', css_class='btn btn-primary'),
            )
        )

    def clean(self):
        cleaned_data = super().clean()
        company = cleaned_data.get('company')
        item = cleaned_data.get('item')
        date_value = cleaned_data.get('date')

        if company and item and date_value:
            # التحقق من عدم وجود إدخال مكرر
            existing = StatisticalEntry.objects.filter(
                company=company, item=item, date=date_value
            )
            if self.instance.pk:
                existing = existing.exclude(pk=self.instance.pk)
            
            if existing.exists():
                raise ValidationError('يوجد إدخال مسجل مسبقاً لهذه الشركة والعنصر في نفس التاريخ')

        return cleaned_data


class ReportFilterForm(forms.Form):
    company = forms.ModelChoiceField(
        queryset=Company.objects.filter(is_active=True),
        required=False,
        empty_label="جميع الشركات",
        widget=forms.Select(attrs={'class': 'form-control'}),
        label='الشركة'
    )
    item = forms.ModelChoiceField(
        queryset=Item.objects.filter(is_active=True),
        required=False,
        empty_label="جميع العناصر",
        widget=forms.Select(attrs={'class': 'form-control'}),
        label='العنصر'
    )
    month = forms.ChoiceField(
        choices=[('', 'جميع الشهور')] + Target.MONTHS,
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'}),
        label='الشهر'
    )
    year = forms.IntegerField(
        required=False,
        widget=forms.NumberInput(attrs={'class': 'form-control', 'min': 2020, 'max': 2030}),
        label='السنة',
        initial=date.today().year
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'get'
        self.helper.layout = Layout(
            Row(
                Column('company', css_class='form-group col-md-3 mb-0'),
                Column('item', css_class='form-group col-md-3 mb-0'),
                Column('month', css_class='form-group col-md-3 mb-0'),
                Column('year', css_class='form-group col-md-3 mb-0'),
                css_class='form-row'
            ),
            FormActions(
                Submit('submit', 'تصفية', css_class='btn btn-primary'),
            )
        )
