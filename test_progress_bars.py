#!/usr/bin/env python
"""
اختبار progress bars في إحصائيات العناصر
"""

import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'statistical_system.settings')
django.setup()

from django.test import Client
from django.contrib.auth.models import User

def test_progress_bars_functionality():
    """اختبار وظائف الـ progress bars"""
    print("📊 اختبار progress bars في إحصائيات العناصر")
    print("="*50)
    
    client = Client()
    
    # تسجيل الدخول
    login_success = client.login(username='alaa1', password='alaa1123')
    
    if not login_success:
        print("❌ فشل في تسجيل الدخول")
        return False
    
    try:
        # اختبار صفحة لوحة المؤشرات
        print("📄 اختبار صفحة لوحة المؤشرات...")
        response = client.get('/dashboard/')
        
        if response.status_code == 200:
            print("✅ صفحة لوحة المؤشرات تعمل")
            
            # فحص محتوى الصفحة للـ progress bars
            content = response.content.decode()
            
            # فحص العناصر المطلوبة للـ progress bars
            required_elements = [
                'progress-bar',
                'item-progress-main',
                'item-progress-detail',
                'الإنجاز الإجمالي',
                'تفصيل أنواع البيانات',
                'progress',
                'aria-valuenow',
                'aria-valuemin',
                'aria-valuemax'
            ]
            
            all_elements_present = True
            for element in required_elements:
                if element in content:
                    print(f"   ✅ {element}: موجود")
                else:
                    print(f"   ❌ {element}: غير موجود")
                    all_elements_present = False
            
            # فحص أيقونات أنواع البيانات
            data_type_icons = [
                'fa-industry',  # الطاقة الإنتاجية المتاحة
                'fa-cogs',      # الإنتاج
                'fa-warehouse', # المخزون
                'fa-chart-line' # المبيعات
            ]
            
            icons_present = True
            for icon in data_type_icons:
                if icon in content:
                    print(f"   ✅ أيقونة {icon}: موجودة")
                else:
                    print(f"   ⚠️ أيقونة {icon}: غير موجودة")
                    # لا نعتبرها خطأ فادح لأن الأيقونات قد لا تظهر إذا لم تكن هناك بيانات
            
            # فحص CSS classes للـ progress bars
            progress_css_classes = [
                'bg-success',
                'bg-info', 
                'bg-warning',
                'bg-danger',
                'progress-bar-text'
            ]
            
            css_ok = True
            for css_class in progress_css_classes:
                if css_class in content:
                    print(f"   ✅ CSS {css_class}: موجود")
                else:
                    print(f"   ⚠️ CSS {css_class}: غير موجود")
                    css_ok = False
            
            return all_elements_present
            
        else:
            print(f"❌ صفحة لوحة المؤشرات فشلت: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار progress bars: {e}")
        return False
    
    finally:
        client.logout()

def test_progress_bars_data():
    """اختبار بيانات الـ progress bars"""
    print("\n📈 اختبار بيانات progress bars")
    print("-"*30)
    
    try:
        from stats_app.models import Item, Target, StatisticalEntry, SystemSettings
        from stats_app.views import DashboardView
        from django.http import HttpRequest
        from django.contrib.auth.models import User
        
        # إنشاء طلب وهمي
        request = HttpRequest()
        request.method = 'GET'
        request.user = User.objects.get(username='alaa1')
        
        # إنشاء الـ view
        view = DashboardView()
        view.request = request
        
        # الحصول على إعدادات النظام
        settings = SystemSettings.get_settings()
        current_fiscal_year = settings.get_current_fiscal_year()
        
        # اختبار إحصائيات العناصر
        print("📦 اختبار إحصائيات العناصر...")
        items_stats = view.get_items_statistics(current_fiscal_year)
        
        print(f"   📊 عدد العناصر المحللة: {len(items_stats['items_statistics'])}")
        
        # فحص كل عنصر
        for i, item_stat in enumerate(items_stats['items_statistics'][:3]):  # أول 3 عناصر فقط
            item_name = item_stat['item'].name
            achievement = item_stat['achievement']
            target_total = item_stat['target_total']
            actual_total = item_stat['actual_total']
            
            print(f"\n   📦 العنصر {i+1}: {item_name}")
            print(f"      🎯 الإنجاز: {achievement}%")
            print(f"      📈 المستهدف: {target_total:,.0f}")
            print(f"      ✅ المحقق: {actual_total:,.0f}")
            
            # فحص تفصيل أنواع البيانات
            data_types_breakdown = item_stat['data_types_breakdown']
            print(f"      📊 أنواع البيانات:")
            
            for dt_key, dt_data in data_types_breakdown.items():
                dt_name = dt_data['name']
                dt_achievement = dt_data['achievement']
                dt_target = dt_data['target']
                dt_actual = dt_data['actual']
                
                print(f"         • {dt_name}: {dt_achievement}% ({dt_actual:,.0f}/{dt_target:,.0f})")
        
        # فحص أفضل وأسوأ عنصر
        if items_stats['top_performing_item']:
            top_item = items_stats['top_performing_item']
            print(f"\n   🏆 أفضل عنصر: {top_item['item'].name} ({top_item['achievement']}%)")
        
        if items_stats['lowest_performing_item']:
            lowest_item = items_stats['lowest_performing_item']
            print(f"   📉 أقل عنصر أداءً: {lowest_item['item'].name} ({lowest_item['achievement']}%)")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار بيانات progress bars: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_progress_bars_styling():
    """اختبار تصميم الـ progress bars"""
    print("\n🎨 اختبار تصميم progress bars")
    print("-"*30)
    
    client = Client()
    
    # تسجيل الدخول
    login_success = client.login(username='alaa1', password='alaa1123')
    
    if not login_success:
        print("❌ فشل في تسجيل الدخول")
        return False
    
    try:
        response = client.get('/dashboard/')
        
        if response.status_code == 200:
            content = response.content.decode()
            
            # فحص CSS المخصص للـ progress bars
            custom_css_elements = [
                'item-progress-main',
                'item-progress-detail',
                'progress-bar-text',
                'item-stats-card',
                'border-radius: 12px',
                'box-shadow',
                'transition'
            ]
            
            styling_ok = True
            for css_element in custom_css_elements:
                if css_element in content:
                    print(f"   ✅ تصميم {css_element}: موجود")
                else:
                    print(f"   ⚠️ تصميم {css_element}: غير موجود")
                    styling_ok = False
            
            # فحص الألوان المختلفة للـ progress bars
            color_classes = [
                'bg-success',  # أخضر للأداء الممتاز
                'bg-info',     # أزرق للأداء الجيد
                'bg-warning',  # أصفر للأداء المتوسط
                'bg-danger'    # أحمر للأداء الضعيف
            ]
            
            colors_ok = True
            for color_class in color_classes:
                if color_class in content:
                    print(f"   ✅ لون {color_class}: موجود")
                else:
                    print(f"   ⚠️ لون {color_class}: غير موجود")
                    colors_ok = False
            
            return styling_ok and colors_ok
            
        else:
            print(f"❌ فشل في الحصول على الصفحة: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار التصميم: {e}")
        return False
    
    finally:
        client.logout()

def main():
    """الدالة الرئيسية"""
    print("🔍 اختبار شامل لـ progress bars في إحصائيات العناصر")
    print("="*60)
    
    # اختبار الوظائف
    functionality_ok = test_progress_bars_functionality()
    
    # اختبار البيانات
    data_ok = test_progress_bars_data()
    
    # اختبار التصميم
    styling_ok = test_progress_bars_styling()
    
    print("\n" + "="*60)
    print("📋 ملخص اختبارات progress bars:")
    
    tests = [
        ("📊 وظائف progress bars", functionality_ok),
        ("📈 بيانات progress bars", data_ok),
        ("🎨 تصميم progress bars", styling_ok)
    ]
    
    all_working = True
    for name, result in tests:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"   {name}: {status}")
        if not result:
            all_working = False
    
    if all_working:
        print("\n🎉 جميع اختبارات progress bars نجحت!")
        print("✅ إحصائيات العناصر تعرض progress bars بشكل مثالي")
        
        print("\n💡 المميزات المحققة:")
        print("   📊 progress bar رئيسي للإنجاز الإجمالي")
        print("   📈 progress bars تفصيلية لكل نوع بيانات")
        print("   🎨 ألوان ديناميكية حسب الأداء")
        print("   🔍 أيقونات مميزة لكل نوع بيانات")
        print("   ✨ تأثيرات تفاعلية وحركية")
        print("   📱 تصميم متجاوب وجميل")
        
        print("\n🌐 للمشاهدة:")
        print("   📊 لوحة المؤشرات: http://127.0.0.1:8000/dashboard/")
        
    else:
        print("\n⚠️ بعض اختبارات progress bars فشلت")
        print("🔧 راجع التفاصيل أعلاه")
    
    print("="*60)

if __name__ == '__main__':
    main()
