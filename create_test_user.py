#!/usr/bin/env python
"""
سكريبت لإنشاء مستخدم تجريبي لاختبار تسجيل الدخول
"""

import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'statistical_system.settings')
django.setup()

from django.contrib.auth.models import User
from stats_app.models import UserProfile, Company

def create_test_user():
    """إنشاء مستخدم تجريبي"""
    print("إنشاء مستخدم تجريبي...")
    
    # بيانات المستخدم التجريبي
    username = "testuser"
    password = "test123456"
    email = "<EMAIL>"
    
    # حذف المستخدم إذا كان موجوداً
    if User.objects.filter(username=username).exists():
        User.objects.filter(username=username).delete()
        print(f"تم حذف المستخدم الموجود: {username}")
    
    # إنشاء المستخدم الجديد
    user = User.objects.create_user(
        username=username,
        password=password,
        email=email,
        first_name="مستخدم",
        last_name="تجريبي"
    )
    
    print(f"✅ تم إنشاء المستخدم: {username}")
    print(f"   كلمة المرور: {password}")
    
    # التحقق من UserProfile
    try:
        profile = user.userprofile
        print(f"✅ تم إنشاء ملف شخصي للمستخدم")
        
        # تعيين شركة إذا كانت متوفرة
        if Company.objects.exists():
            first_company = Company.objects.first()
            profile.company = first_company
            profile.is_company_user = True
            profile.save()
            print(f"✅ تم ربط المستخدم بالشركة: {first_company.name}")
        
    except UserProfile.DoesNotExist:
        print("❌ لم يتم إنشاء ملف شخصي للمستخدم")
        return False
    
    return True

def create_admin_user():
    """إنشاء مستخدم مدير تجريبي"""
    print("\nإنشاء مستخدم مدير تجريبي...")
    
    # بيانات المستخدم المدير
    username = "manager"
    password = "manager123"
    email = "<EMAIL>"
    
    # حذف المستخدم إذا كان موجوداً
    if User.objects.filter(username=username).exists():
        User.objects.filter(username=username).delete()
        print(f"تم حذف المستخدم الموجود: {username}")
    
    # إنشاء المستخدم الجديد
    user = User.objects.create_user(
        username=username,
        password=password,
        email=email,
        first_name="مدير",
        last_name="النظام"
    )
    
    print(f"✅ تم إنشاء المستخدم المدير: {username}")
    print(f"   كلمة المرور: {password}")
    
    # التحقق من UserProfile وتعيين صلاحيات المدير
    try:
        profile = user.userprofile
        profile.is_system_admin = True
        profile.is_company_user = False
        profile.save()
        print(f"✅ تم تعيين صلاحيات مدير النظام")
        
    except UserProfile.DoesNotExist:
        print("❌ لم يتم إنشاء ملف شخصي للمستخدم المدير")
        return False
    
    return True

def list_all_users():
    """عرض جميع المستخدمين"""
    print("\n=== قائمة جميع المستخدمين ===")
    
    users = User.objects.all()
    
    for user in users:
        try:
            profile = user.userprofile
            user_type = []
            if user.is_superuser:
                user_type.append("مدير عام")
            if profile.is_system_admin:
                user_type.append("مدير النظام")
            if profile.is_company_user:
                user_type.append(f"مستخدم شركة ({profile.company.name if profile.company else 'غير محدد'})")
            
            if not user_type:
                user_type.append("مستخدم عادي")
            
            print(f"👤 {user.username} ({user.first_name} {user.last_name})")
            print(f"   📧 {user.email}")
            print(f"   🔑 {' | '.join(user_type)}")
            print(f"   📅 تاريخ الإنشاء: {user.date_joined.strftime('%Y-%m-%d %H:%M')}")
            print()
            
        except UserProfile.DoesNotExist:
            print(f"⚠️ {user.username} - لا يوجد ملف شخصي")
            print()

def test_login_credentials():
    """اختبار بيانات تسجيل الدخول"""
    print("=== اختبار بيانات تسجيل الدخول ===")
    
    test_credentials = [
        ("admin", "admin"),  # المدير العام
        ("testuser", "test123456"),  # المستخدم التجريبي
        ("manager", "manager123"),  # المدير التجريبي
    ]
    
    from django.contrib.auth import authenticate
    
    for username, password in test_credentials:
        user = authenticate(username=username, password=password)
        if user:
            print(f"✅ {username}: تسجيل الدخول ناجح")
            try:
                profile = user.userprofile
                print(f"   - مدير النظام: {profile.is_system_admin}")
                print(f"   - مستخدم شركة: {profile.is_company_user}")
                print(f"   - الشركة: {profile.company.name if profile.company else 'غير محدد'}")
            except:
                print(f"   - لا يوجد ملف شخصي")
        else:
            if User.objects.filter(username=username).exists():
                print(f"❌ {username}: كلمة المرور خاطئة")
            else:
                print(f"❌ {username}: المستخدم غير موجود")
        print()

def main():
    """الدالة الرئيسية"""
    print("🔧 إنشاء مستخدمين تجريبيين لاختبار تسجيل الدخول")
    print("="*60)
    
    # إنشاء المستخدمين التجريبيين
    success1 = create_test_user()
    success2 = create_admin_user()
    
    # عرض جميع المستخدمين
    list_all_users()
    
    # اختبار تسجيل الدخول
    test_login_credentials()
    
    print("="*60)
    print("📋 معلومات تسجيل الدخول:")
    print("🌐 رابط تسجيل الدخول: http://127.0.0.1:8000/accounts/login/")
    print()
    print("👤 المستخدمين المتاحين:")
    print("   1. admin / admin (مدير عام)")
    print("   2. testuser / test123456 (مستخدم شركة)")
    print("   3. manager / manager123 (مدير نظام)")
    print()
    
    if success1 and success2:
        print("✅ تم إنشاء جميع المستخدمين بنجاح!")
        print("يمكنك الآن اختبار تسجيل الدخول بأي من المستخدمين أعلاه")
    else:
        print("❌ حدثت مشاكل في إنشاء بعض المستخدمين")

if __name__ == '__main__':
    main()
