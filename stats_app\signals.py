from django.db.models.signals import post_save
from django.dispatch import receiver
from django.contrib.auth.models import User
from .models import UserProfile


@receiver(post_save, sender=User)
def create_user_profile(sender, instance, created, **kwargs):
    """إنشاء ملف مستخدم تلقائياً عند إنشاء مستخدم جديد"""
    if created:
        # التحقق من عدم وجود UserProfile مسبقاً
        if not UserProfile.objects.filter(user=instance).exists():
            UserProfile.objects.create(user=instance)


@receiver(post_save, sender=User)
def save_user_profile(sender, instance, **kwargs):
    """حفظ ملف المستخدم عند حفظ المستخدم"""
    # تجنب إنشاء ملف مكرر إذا كان المستخدم جديد (سيتم إنشاؤه في الإشارة الأولى)
    if not kwargs.get('created', False):
        try:
            # محاولة الحصول على UserProfile الموجود
            profile = instance.userprofile
            profile.save()
        except UserProfile.DoesNotExist:
            # إنشاء UserProfile جديد إذا لم يكن موجوداً
            if not UserProfile.objects.filter(user=instance).exists():
                UserProfile.objects.create(user=instance)
