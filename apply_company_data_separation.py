#!/usr/bin/env python
"""
سكريپت لتطبيق فصل البيانات على جميع عروض JSON API
"""

import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'statistical_system.settings')
django.setup()

def update_json_views():
    """تحديث عروض JSON لتطبيق فصل البيانات"""
    
    # قراءة ملف views.py
    views_file_path = 'stats_app/views.py'
    
    with open(views_file_path, 'r', encoding='utf-8') as file:
        content = file.read()
    
    # قائمة التحديثات المطلوبة
    updates = [
        # تحديث get_company_performance_data
        {
            'old': 'companies = Company.objects.filter(is_active=True)',
            'new': 'companies = self.get_user_companies().filter(is_active=True)'
        },
        
        # تحديث get_detailed_monthly_data
        {
            'old': 'entries = StatisticalEntry.objects.all()',
            'new': 'entries = self.filter_queryset_by_company(StatisticalEntry.objects.all())'
        },
        
        # تحديث get_data_types_comparison
        {
            'old': 'entries = StatisticalEntry.objects.filter(date__year=year)\n        targets = Target.objects.filter(year=year)',
            'new': 'entries = self.filter_queryset_by_company(StatisticalEntry.objects.filter(date__year=year))\n        targets = self.filter_queryset_by_company(Target.objects.filter(year=year))'
        },
        
        # تحديث get_company_data_types
        {
            'old': 'companies = Company.objects.filter(is_active=True)\n        entries = StatisticalEntry.objects.filter(',
            'new': 'companies = self.get_user_companies().filter(is_active=True)\n        entries = self.filter_queryset_by_company(StatisticalEntry.objects.filter('
        },
        
        # تحديث get_achievement_analysis
        {
            'old': 'entries = StatisticalEntry.objects.filter(date__year=year)\n        targets = Target.objects.filter(year=year)',
            'new': 'entries = self.filter_queryset_by_company(StatisticalEntry.objects.filter(date__year=year))\n        targets = self.filter_queryset_by_company(Target.objects.filter(year=year))'
        },
        
        # تحديث get_trend_analysis
        {
            'old': 'entries = StatisticalEntry.objects.filter(date__year=year)\n        targets = Target.objects.filter(year=year)',
            'new': 'entries = self.filter_queryset_by_company(StatisticalEntry.objects.filter(date__year=year))\n        targets = self.filter_queryset_by_company(Target.objects.filter(year=year))'
        },
        
        # تحديث get_capacity_utilization
        {
            'old': 'entries = StatisticalEntry.objects.filter(date__year=year)',
            'new': 'entries = self.filter_queryset_by_company(StatisticalEntry.objects.filter(date__year=year))'
        },
        
        # تحديث get_kpi_dashboard
        {
            'old': 'entries = StatisticalEntry.objects.filter(date__year=year)\n        targets = Target.objects.filter(year=year)',
            'new': 'entries = self.filter_queryset_by_company(StatisticalEntry.objects.filter(date__year=year))\n        targets = self.filter_queryset_by_company(Target.objects.filter(year=year))'
        },
        
        # تحديث get_variance_analysis
        {
            'old': 'entries = StatisticalEntry.objects.filter(date__year=year)\n        targets = Target.objects.filter(year=year)',
            'new': 'entries = self.filter_queryset_by_company(StatisticalEntry.objects.filter(date__year=year))\n        targets = self.filter_queryset_by_company(Target.objects.filter(year=year))'
        },
        
        # تحديث get_efficiency_metrics
        {
            'old': 'companies = Company.objects.filter(is_active=True)',
            'new': 'companies = self.get_user_companies().filter(is_active=True)'
        },
        
        # تحديث get_seasonal_analysis
        {
            'old': 'entries = StatisticalEntry.objects.filter(date__year=year)',
            'new': 'entries = self.filter_queryset_by_company(StatisticalEntry.objects.filter(date__year=year))'
        },
        
        # تحديث get_performance_ranking
        {
            'old': 'companies = Company.objects.filter(is_active=True)',
            'new': 'companies = self.get_user_companies().filter(is_active=True)'
        },
        
        # تحديث get_growth_analysis
        {
            'old': 'companies = Company.objects.filter(is_active=True)',
            'new': 'companies = self.get_user_companies().filter(is_active=True)'
        },
        
        # تحديث get_risk_assessment
        {
            'old': 'companies = Company.objects.filter(is_active=True)',
            'new': 'companies = self.get_user_companies().filter(is_active=True)'
        },
        
        # تحديث get_forecasting_data
        {
            'old': 'entries = StatisticalEntry.objects.filter(date__year__in=[year-1, year])',
            'new': 'entries = self.filter_queryset_by_company(StatisticalEntry.objects.filter(date__year__in=[year-1, year]))'
        },
        
        # تحديث get_benchmark_analysis
        {
            'old': 'companies = Company.objects.filter(is_active=True)',
            'new': 'companies = self.get_user_companies().filter(is_active=True)'
        },
        
        # تحديث get_correlation_matrix
        {
            'old': 'entries = StatisticalEntry.objects.filter(date__year=year)',
            'new': 'entries = self.filter_queryset_by_company(StatisticalEntry.objects.filter(date__year=year))'
        }
    ]
    
    # تطبيق التحديثات
    updated_count = 0
    for update in updates:
        if update['old'] in content:
            content = content.replace(update['old'], update['new'])
            updated_count += 1
            print(f"✅ تم تحديث: {update['old'][:50]}...")
        else:
            print(f"⚠️ لم يتم العثور على: {update['old'][:50]}...")
    
    # كتابة الملف المحدث
    with open(views_file_path, 'w', encoding='utf-8') as file:
        file.write(content)
    
    print(f"\n📊 تم تطبيق {updated_count} تحديث من أصل {len(updates)}")
    
    return updated_count == len(updates)

def add_company_filter_validation():
    """إضافة التحقق من صحة معرف الشركة في عروض JSON"""
    
    views_file_path = 'stats_app/views.py'
    
    with open(views_file_path, 'r', encoding='utf-8') as file:
        content = file.read()
    
    # إضافة التحقق من صحة company_id
    validation_code = '''
        # التحقق من صحة معرف الشركة
        if company_id:
            allowed_companies = self.get_user_companies()
            if not allowed_companies.filter(id=company_id).exists():
                company_id = None  # إلغاء معرف الشركة إذا لم يكن مسموحاً
'''
    
    # البحث عن الأماكن التي تحتاج إلى التحقق
    methods_needing_validation = [
        'def get_detailed_monthly_data(self):',
        'def get_data_types_comparison(self):',
        'def get_achievement_analysis(self):',
        'def get_trend_analysis(self):',
        'def get_capacity_utilization(self):',
        'def get_kpi_dashboard(self):',
        'def get_variance_analysis(self):',
        'def get_seasonal_analysis(self):',
        'def get_forecasting_data(self):',
    ]
    
    updated_methods = 0
    
    for method in methods_needing_validation:
        if method in content:
            # البحث عن السطر الذي يحتوي على company_id = self.request.GET.get('company')
            method_start = content.find(method)
            if method_start != -1:
                # البحث عن السطر التالي بعد تعريف company_id
                company_id_line = content.find("company_id = self.request.GET.get('company')", method_start)
                if company_id_line != -1:
                    # العثور على نهاية السطر
                    line_end = content.find('\n', company_id_line)
                    if line_end != -1:
                        # إدراج كود التحقق
                        content = content[:line_end] + validation_code + content[line_end:]
                        updated_methods += 1
                        print(f"✅ تم إضافة التحقق إلى: {method}")
    
    # كتابة الملف المحدث
    with open(views_file_path, 'w', encoding='utf-8') as file:
        file.write(content)
    
    print(f"\n🔒 تم إضافة التحقق إلى {updated_methods} دالة")
    
    return updated_methods > 0

def main():
    """الدالة الرئيسية"""
    print("🔧 تطبيق فصل البيانات على عروض JSON API")
    print("="*60)
    
    # تحديث عروض JSON
    json_success = update_json_views()
    
    # إضافة التحقق من صحة معرف الشركة
    validation_success = add_company_filter_validation()
    
    print("\n" + "="*60)
    if json_success and validation_success:
        print("🎉 تم تطبيق فصل البيانات بنجاح!")
        print("✅ جميع عروض JSON API تطبق الآن فصل البيانات حسب الشركة")
        print("✅ تم إضافة التحقق من صحة معرف الشركة")
    else:
        print("⚠️ تم تطبيق بعض التحديثات، يرجى مراجعة الملف يدوياً")
    
    print("\n📋 الخطوات التالية:")
    print("1. اختبار تسجيل الدخول بمستخدمي الشركة")
    print("2. التحقق من أن كل مستخدم يرى بيانات شركته فقط")
    print("3. اختبار الرسوم البيانية والتقارير")
    print("="*60)

if __name__ == '__main__':
    main()
