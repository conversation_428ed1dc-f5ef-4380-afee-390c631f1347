#!/usr/bin/env python
"""
اختبار تقرير المقارنة بالأعوام السابقة
"""

import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'statistical_system.settings')
django.setup()

from django.test import Client
from django.contrib.auth.models import User
from stats_app.models import Company, Item, Target, StatisticalEntry, SystemSettings
from datetime import date, datetime
import json

def test_yearly_comparison_view():
    """اختبار صفحة المقارنة السنوية"""
    print("🧪 اختبار صفحة المقارنة السنوية")
    print("="*40)
    
    client = Client()
    
    # تسجيل الدخول
    login_success = client.login(username='alaa1', password='alaa1123')
    
    if not login_success:
        print("❌ فشل في تسجيل الدخول")
        return False
    
    try:
        # اختبار صفحة المقارنة
        response = client.get('/reports/yearly-comparison/')
        
        if response.status_code == 200:
            print("✅ صفحة المقارنة السنوية تعمل")
            
            # فحص السياق
            context = response.context
            required_context = ['companies', 'items', 'data_types', 'years', 'system_settings']
            
            for key in required_context:
                if key in context:
                    print(f"   ✅ {key}: متوفر")
                else:
                    print(f"   ❌ {key}: غير متوفر")
            
            # فحص السنوات المتوفرة
            years = context.get('years', [])
            print(f"   📅 السنوات المتوفرة: {years}")
            
            return True
        else:
            print(f"❌ صفحة المقارنة لا تعمل: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الصفحة: {e}")
        return False
    
    finally:
        client.logout()

def test_yearly_comparison_api():
    """اختبار API المقارنة السنوية"""
    print("\n🔌 اختبار API المقارنة السنوية")
    print("-"*35)
    
    client = Client()
    
    # تسجيل الدخول
    login_success = client.login(username='alaa1', password='alaa1123')
    
    if not login_success:
        print("❌ فشل في تسجيل الدخول")
        return False
    
    try:
        # اختبار API الأساسي
        response = client.get('/reports/yearly-comparison/data/')
        
        if response.status_code == 200:
            try:
                data = response.json()
                print("✅ API يعمل ويعيد JSON")
                
                # فحص البيانات المطلوبة
                required_keys = ['labels', 'datasets', 'summary', 'growth_rates', 'performance_analysis', 'selected_years']
                
                for key in required_keys:
                    if key in data:
                        print(f"   ✅ {key}: متوفر")
                    else:
                        print(f"   ❌ {key}: غير متوفر")
                
                # فحص البيانات
                print(f"   📊 عدد مجموعات البيانات: {len(data.get('datasets', []))}")
                print(f"   📅 السنوات المختارة: {data.get('selected_years', [])}")
                print(f"   📈 معدلات النمو: {len(data.get('growth_rates', {}))}")
                
                return True
                
            except json.JSONDecodeError:
                print("❌ API لا يعيد JSON صحيح")
                return False
        else:
            print(f"❌ API لا يعمل: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار API: {e}")
        return False
    
    finally:
        client.logout()

def test_api_with_filters():
    """اختبار API مع فلاتر مختلفة"""
    print("\n🎛️ اختبار API مع فلاتر مختلفة")
    print("-"*35)
    
    client = Client()
    
    # تسجيل الدخول
    login_success = client.login(username='alaa1', password='alaa1123')
    
    if not login_success:
        print("❌ فشل في تسجيل الدخول")
        return False
    
    try:
        # الحصول على بيانات للاختبار
        company = Company.objects.first()
        item = Item.objects.first()
        
        if not company or not item:
            print("⚠️ لا توجد شركة أو عنصر للاختبار")
            return False
        
        # اختبار فلاتر مختلفة
        test_cases = [
            {
                'name': 'فلتر الشركة',
                'params': f'company={company.id}'
            },
            {
                'name': 'فلتر العنصر',
                'params': f'item={item.id}'
            },
            {
                'name': 'فلتر نوع البيانات',
                'params': 'data_type=production'
            },
            {
                'name': 'فلتر السنوات',
                'params': 'years[]=2024&years[]=2023'
            },
            {
                'name': 'فلاتر متعددة',
                'params': f'company={company.id}&item={item.id}&data_type=production&years[]=2024'
            }
        ]
        
        for test_case in test_cases:
            response = client.get(f'/reports/yearly-comparison/data/?{test_case["params"]}')
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"   ✅ {test_case['name']}: يعمل")
                    print(f"      📊 مجموعات البيانات: {len(data.get('datasets', []))}")
                    print(f"      📅 السنوات: {data.get('selected_years', [])}")
                except:
                    print(f"   ⚠️ {test_case['name']}: يعمل لكن JSON غير صحيح")
            else:
                print(f"   ❌ {test_case['name']}: فشل ({response.status_code})")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الفلاتر: {e}")
        return False
    
    finally:
        client.logout()

def test_data_availability():
    """اختبار توفر البيانات للمقارنة"""
    print("\n📊 اختبار توفر البيانات للمقارنة")
    print("-"*35)
    
    # فحص السنوات المالية المتوفرة
    target_fiscal_years = Target.objects.filter(fiscal_year__isnull=False).values_list('fiscal_year', flat=True).distinct()
    entry_fiscal_years = StatisticalEntry.objects.filter(fiscal_year__isnull=False).values_list('fiscal_year', flat=True).distinct()
    
    all_fiscal_years = set(target_fiscal_years) | set(entry_fiscal_years)
    sorted_years = sorted(all_fiscal_years, reverse=True)
    
    print(f"📅 السنوات المالية المتوفرة: {sorted_years}")
    
    if len(sorted_years) >= 2:
        print("✅ يوجد سنوات كافية للمقارنة")
        
        # فحص البيانات لكل سنة
        for year in sorted_years:
            targets_count = Target.objects.filter(fiscal_year=year).count()
            entries_count = StatisticalEntry.objects.filter(fiscal_year=year).count()
            
            print(f"   📅 {year}: {targets_count} مستهدف، {entries_count} إدخال")
        
        return True
    else:
        print("⚠️ لا توجد سنوات كافية للمقارنة (يحتاج سنتين على الأقل)")
        return False

def test_fiscal_year_calculations():
    """اختبار حسابات السنة المالية"""
    print("\n📅 اختبار حسابات السنة المالية")
    print("-"*35)
    
    settings = SystemSettings.get_settings()
    
    # اختبار السنة المالية الحالية
    current_fiscal_year = settings.get_current_fiscal_year()
    print(f"✅ السنة المالية الحالية: {current_fiscal_year}")
    
    # اختبار أشهر السنة المالية
    fiscal_months = settings.get_fiscal_year_months(current_fiscal_year)
    print(f"✅ أشهر السنة المالية {current_fiscal_year}: {len(fiscal_months)} شهر")
    
    # عرض الأشهر
    for i, month in enumerate(fiscal_months):
        if i < 3:  # عرض أول 3 أشهر فقط
            print(f"   • {month['display']}")
        elif i == 3:
            print(f"   • ... و {len(fiscal_months) - 3} أشهر أخرى")
            break
    
    return True

def test_performance_analysis():
    """اختبار تحليل الأداء"""
    print("\n📈 اختبار تحليل الأداء")
    print("-"*25)
    
    # محاكاة بيانات للاختبار
    test_summary = {
        2024: {
            'total_actual': 100000,
            'total_target': 120000,
            'achievement_rate': 83.33
        },
        2023: {
            'total_actual': 80000,
            'total_target': 100000,
            'achievement_rate': 80.00
        },
        2022: {
            'total_actual': 60000,
            'total_target': 80000,
            'achievement_rate': 75.00
        }
    }
    
    # اختبار حساب معدلات النمو
    from stats_app.views import YearlyComparisonDataView
    
    view = YearlyComparisonDataView()
    
    # حساب معدلات النمو
    growth_rates = view.calculate_growth_rates(test_summary, [2022, 2023, 2024])
    print(f"✅ معدلات النمو: {growth_rates}")
    
    # تحليل الأداء
    performance = view.analyze_performance(test_summary)
    print(f"✅ تحليل الأداء:")
    print(f"   🏆 أفضل إنجاز: {performance['best_achievement_year']} ({performance['best_achievement_rate']}%)")
    print(f"   📊 أعلى قيمة: {performance['best_actual_year']} ({performance['best_actual_value']:,})")
    print(f"   📉 أسوأ إنجاز: {performance['worst_achievement_year']} ({performance['worst_achievement_rate']}%)")
    print(f"   📈 متوسط الإنجاز: {performance['average_achievement']}%")
    
    return True

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار شامل لتقرير المقارنة بالأعوام السابقة")
    print("="*60)
    
    # اختبار توفر البيانات
    data_available = test_data_availability()
    
    # اختبار حسابات السنة المالية
    fiscal_calculations = test_fiscal_year_calculations()
    
    # اختبار تحليل الأداء
    performance_analysis = test_performance_analysis()
    
    # اختبار الصفحة
    page_working = test_yearly_comparison_view()
    
    # اختبار API
    api_working = test_yearly_comparison_api()
    
    # اختبار الفلاتر
    filters_working = test_api_with_filters()
    
    print("\n" + "="*60)
    print("📋 ملخص الاختبارات:")
    
    results = [
        ("📊 توفر البيانات", data_available),
        ("📅 حسابات السنة المالية", fiscal_calculations),
        ("📈 تحليل الأداء", performance_analysis),
        ("🖥️ صفحة المقارنة", page_working),
        ("🔌 API المقارنة", api_working),
        ("🎛️ فلاتر API", filters_working)
    ]
    
    all_working = True
    for name, result in results:
        status = "✅ يعمل" if result else "❌ لا يعمل"
        print(f"   {name}: {status}")
        if not result:
            all_working = False
    
    if all_working:
        print("\n🎉 جميع اختبارات المقارنة السنوية نجحت!")
        print("✅ تقرير المقارنة بالأعوام السابقة جاهز للاستخدام")
        
        print("\n🌐 للاختبار:")
        print("   📊 صفحة المقارنة: http://127.0.0.1:8000/reports/yearly-comparison/")
        print("   🔌 API المقارنة: http://127.0.0.1:8000/reports/yearly-comparison/data/")
        
        print("\n💡 المميزات:")
        print("   ✅ مقارنة متعددة السنوات")
        print("   ✅ رسوم بيانية تفاعلية")
        print("   ✅ حساب معدلات النمو")
        print("   ✅ تحليل الأداء")
        print("   ✅ فلاتر متقدمة")
        print("   ✅ جداول تفصيلية")
        print("   ✅ تصدير الرسوم البيانية")
        
    else:
        print("\n⚠️ بعض اختبارات المقارنة السنوية فشلت")
        print("🔧 يحتاج مراجعة وإصلاح")
    
    print("="*60)

if __name__ == '__main__':
    main()
