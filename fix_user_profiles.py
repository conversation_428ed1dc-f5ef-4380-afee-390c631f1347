#!/usr/bin/env python
"""
سكريبت لإصلاح مشاكل UserProfile وإنشاء ملفات المستخدمين المفقودة
"""

import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'statistical_system.settings')
django.setup()

from django.contrib.auth.models import User
from stats_app.models import UserProfile

def fix_user_profiles():
    """إصلاح ملفات المستخدمين المفقودة"""
    print("بدء إصلاح ملفات المستخدمين...")
    
    # الحصول على جميع المستخدمين
    users = User.objects.all()
    
    fixed_count = 0
    existing_count = 0
    
    for user in users:
        try:
            # محاولة الوصول إلى UserProfile
            profile = user.userprofile
            existing_count += 1
            print(f"✓ المستخدم {user.username} لديه ملف شخصي موجود")
        except UserProfile.DoesNotExist:
            # إنشاء UserProfile جديد
            UserProfile.objects.create(user=user)
            fixed_count += 1
            print(f"✓ تم إنشاء ملف شخصي للمستخدم {user.username}")
    
    print(f"\n=== ملخص الإصلاح ===")
    print(f"إجمالي المستخدمين: {users.count()}")
    print(f"ملفات موجودة مسبقاً: {existing_count}")
    print(f"ملفات تم إنشاؤها: {fixed_count}")
    
    if fixed_count > 0:
        print(f"\n✅ تم إصلاح {fixed_count} ملف مستخدم بنجاح!")
    else:
        print("\n✅ جميع المستخدمين لديهم ملفات شخصية!")

def remove_duplicate_profiles():
    """إزالة ملفات المستخدمين المكررة"""
    print("\nفحص الملفات المكررة...")
    
    # البحث عن المستخدمين الذين لديهم أكثر من ملف شخصي
    users_with_multiple_profiles = []
    
    for user in User.objects.all():
        profile_count = UserProfile.objects.filter(user=user).count()
        if profile_count > 1:
            users_with_multiple_profiles.append(user)
            print(f"⚠️ المستخدم {user.username} لديه {profile_count} ملفات شخصية")
    
    if users_with_multiple_profiles:
        print(f"\nإزالة الملفات المكررة...")
        removed_count = 0
        
        for user in users_with_multiple_profiles:
            profiles = UserProfile.objects.filter(user=user).order_by('id')
            # الاحتفاظ بالملف الأول وحذف الباقي
            for profile in profiles[1:]:
                profile.delete()
                removed_count += 1
                print(f"✓ تم حذف ملف مكرر للمستخدم {user.username}")
        
        print(f"\n✅ تم حذف {removed_count} ملف مكرر!")
    else:
        print("✅ لا توجد ملفات مكررة!")

def verify_user_profiles():
    """التحقق من سلامة ملفات المستخدمين"""
    print("\nالتحقق من سلامة ملفات المستخدمين...")
    
    users = User.objects.all()
    profiles = UserProfile.objects.all()
    
    print(f"إجمالي المستخدمين: {users.count()}")
    print(f"إجمالي الملفات الشخصية: {profiles.count()}")
    
    # التحقق من أن كل مستخدم لديه ملف شخصي واحد فقط
    issues = []
    
    for user in users:
        profile_count = UserProfile.objects.filter(user=user).count()
        if profile_count == 0:
            issues.append(f"المستخدم {user.username} ليس لديه ملف شخصي")
        elif profile_count > 1:
            issues.append(f"المستخدم {user.username} لديه {profile_count} ملفات شخصية")
    
    if issues:
        print("\n❌ مشاكل موجودة:")
        for issue in issues:
            print(f"  - {issue}")
        return False
    else:
        print("\n✅ جميع ملفات المستخدمين سليمة!")
        return True

def main():
    """الدالة الرئيسية"""
    print("=== أداة إصلاح ملفات المستخدمين ===\n")
    
    # التحقق الأولي
    if not verify_user_profiles():
        # إزالة الملفات المكررة أولاً
        remove_duplicate_profiles()
        
        # إصلاح الملفات المفقودة
        fix_user_profiles()
        
        # التحقق النهائي
        print("\n" + "="*50)
        verify_user_profiles()
    
    print("\n=== انتهى الإصلاح ===")

if __name__ == '__main__':
    main()
