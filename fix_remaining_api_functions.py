#!/usr/bin/env python
"""
سكريپت لإصلاح الدوال المتبقية في JSON API
"""

import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'statistical_system.settings')
django.setup()

def fix_remaining_functions():
    """إصلاح الدوال المتبقية"""
    
    views_file_path = 'stats_app/views.py'
    
    with open(views_file_path, 'r', encoding='utf-8') as file:
        content = file.read()
    
    # قائمة الإصلاحات المطلوبة
    fixes = [
        # get_benchmark_analysis
        {
            'search': 'def get_benchmark_analysis(self):',
            'old': 'companies = Company.objects.filter(is_active=True)',
            'new': 'companies = self.get_user_companies().filter(is_active=True)'
        },
        
        # get_benchmark_analysis - entries
        {
            'search': 'def get_benchmark_analysis(self):',
            'old': 'entries = StatisticalEntry.objects.filter(',
            'new': 'entries = self.filter_queryset_by_company(StatisticalEntry.objects.filter('
        },
        
        # get_performance_ranking
        {
            'search': 'def get_performance_ranking(self):',
            'old': 'companies = Company.objects.filter(is_active=True)',
            'new': 'companies = self.get_user_companies().filter(is_active=True)'
        },
        
        # get_performance_ranking - entries and targets
        {
            'search': 'def get_performance_ranking(self):',
            'old': 'entries = StatisticalEntry.objects.filter(\n                company=company,\n                data_type=data_type,\n                date__year=year\n            )\n            targets = Target.objects.filter(\n                company=company,\n                data_type=data_type,\n                year=year\n            )',
            'new': 'entries = self.filter_queryset_by_company(StatisticalEntry.objects.filter(\n                company=company,\n                data_type=data_type,\n                date__year=year\n            ))\n            targets = self.filter_queryset_by_company(Target.objects.filter(\n                company=company,\n                data_type=data_type,\n                year=year\n            ))'
        },
        
        # get_growth_analysis
        {
            'search': 'def get_growth_analysis(self):',
            'old': 'companies = Company.objects.filter(is_active=True)',
            'new': 'companies = self.get_user_companies().filter(is_active=True)'
        },
        
        # get_risk_assessment
        {
            'search': 'def get_risk_assessment(self):',
            'old': 'companies = Company.objects.filter(is_active=True)',
            'new': 'companies = self.get_user_companies().filter(is_active=True)'
        },
        
        # get_forecasting_data
        {
            'search': 'def get_forecasting_data(self):',
            'old': 'entries = StatisticalEntry.objects.filter(date__year__in=[year-1, year])',
            'new': 'entries = self.filter_queryset_by_company(StatisticalEntry.objects.filter(date__year__in=[year-1, year]))'
        },
        
        # get_seasonal_analysis
        {
            'search': 'def get_seasonal_analysis(self):',
            'old': 'entries = StatisticalEntry.objects.filter(date__year=year)',
            'new': 'entries = self.filter_queryset_by_company(StatisticalEntry.objects.filter(date__year=year))'
        },
        
        # get_trend_analysis
        {
            'search': 'def get_trend_analysis(self):',
            'old': 'entries = StatisticalEntry.objects.filter(date__year=year)\n        targets = Target.objects.filter(year=year)',
            'new': 'entries = self.filter_queryset_by_company(StatisticalEntry.objects.filter(date__year=year))\n        targets = self.filter_queryset_by_company(Target.objects.filter(year=year))'
        },
        
        # get_capacity_utilization
        {
            'search': 'def get_capacity_utilization(self):',
            'old': 'entries = StatisticalEntry.objects.filter(date__year=year)',
            'new': 'entries = self.filter_queryset_by_company(StatisticalEntry.objects.filter(date__year=year))'
        },
        
        # get_kpi_dashboard
        {
            'search': 'def get_kpi_dashboard(self):',
            'old': 'entries = StatisticalEntry.objects.filter(date__year=year)\n        targets = Target.objects.filter(year=year)',
            'new': 'entries = self.filter_queryset_by_company(StatisticalEntry.objects.filter(date__year=year))\n        targets = self.filter_queryset_by_company(Target.objects.filter(year=year))'
        },
        
        # get_variance_analysis
        {
            'search': 'def get_variance_analysis(self):',
            'old': 'entries = StatisticalEntry.objects.filter(date__year=year)\n        targets = Target.objects.filter(year=year)',
            'new': 'entries = self.filter_queryset_by_company(StatisticalEntry.objects.filter(date__year=year))\n        targets = self.filter_queryset_by_company(Target.objects.filter(year=year))'
        }
    ]
    
    # تطبيق الإصلاحات
    fixed_count = 0
    
    for fix in fixes:
        # البحث عن الدالة
        function_start = content.find(fix['search'])
        if function_start != -1:
            # البحث عن النمط القديم بعد بداية الدالة
            pattern_start = content.find(fix['old'], function_start)
            if pattern_start != -1:
                # استبدال النمط
                pattern_end = pattern_start + len(fix['old'])
                content = content[:pattern_start] + fix['new'] + content[pattern_end:]
                fixed_count += 1
                print(f"✅ تم إصلاح: {fix['search']} - {fix['old'][:30]}...")
            else:
                print(f"⚠️ لم يتم العثور على النمط في: {fix['search']}")
        else:
            print(f"❌ لم يتم العثور على الدالة: {fix['search']}")
    
    # كتابة الملف المحدث
    with open(views_file_path, 'w', encoding='utf-8') as file:
        file.write(content)
    
    print(f"\n📊 تم تطبيق {fixed_count} إصلاح من أصل {len(fixes)}")
    
    return fixed_count

def add_company_validation():
    """إضافة التحقق من صحة معرف الشركة"""
    
    views_file_path = 'stats_app/views.py'
    
    with open(views_file_path, 'r', encoding='utf-8') as file:
        content = file.read()
    
    # الدوال التي تحتاج إلى التحقق من company_id
    functions_needing_validation = [
        'def get_trend_analysis(self):',
        'def get_capacity_utilization(self):',
        'def get_kpi_dashboard(self):',
        'def get_variance_analysis(self):',
        'def get_seasonal_analysis(self):',
        'def get_forecasting_data(self):',
    ]
    
    validation_code = '''
        # التحقق من صحة معرف الشركة
        if company_id:
            allowed_companies = self.get_user_companies()
            if not allowed_companies.filter(id=company_id).exists():
                company_id = None'''
    
    updated_functions = 0
    
    for function_name in functions_needing_validation:
        function_start = content.find(function_name)
        if function_start != -1:
            # البحث عن السطر الذي يحتوي على company_id = self.request.GET.get('company')
            company_id_line = content.find("company_id = self.request.GET.get('company')", function_start)
            if company_id_line != -1:
                # العثور على نهاية السطر
                line_end = content.find('\n', company_id_line)
                if line_end != -1:
                    # التحقق من عدم وجود التحقق مسبقاً
                    next_lines = content[line_end:line_end+200]
                    if 'التحقق من صحة معرف الشركة' not in next_lines:
                        # إدراج كود التحقق
                        content = content[:line_end] + validation_code + content[line_end:]
                        updated_functions += 1
                        print(f"✅ تم إضافة التحقق إلى: {function_name}")
                    else:
                        print(f"✓ التحقق موجود مسبقاً في: {function_name}")
    
    # كتابة الملف المحدث
    with open(views_file_path, 'w', encoding='utf-8') as file:
        file.write(content)
    
    print(f"\n🔒 تم إضافة التحقق إلى {updated_functions} دالة")
    
    return updated_functions

def test_api_functions():
    """اختبار دوال API"""
    print("\n🧪 اختبار دوال API...")
    
    from django.test import Client
    from django.contrib.auth.models import User
    
    client = Client()
    
    # تسجيل الدخول بمستخدم شركة
    login_success = client.login(username='alaa1', password='alaa1123')
    
    if login_success:
        print("✅ تم تسجيل الدخول بنجاح")
        
        # اختبار بعض دوال API
        test_urls = [
            '/reports/charts/api/?chart_type=monthly_comparison',
            '/reports/charts/api/?chart_type=company_performance',
            '/reports/charts/api/?chart_type=achievement_analysis',
        ]
        
        success_count = 0
        
        for url in test_urls:
            try:
                response = client.get(url)
                if response.status_code == 200:
                    print(f"✅ {url}: يعمل بشكل صحيح")
                    success_count += 1
                else:
                    print(f"❌ {url}: خطأ {response.status_code}")
            except Exception as e:
                print(f"❌ {url}: خطأ {e}")
        
        print(f"\n📊 نتيجة الاختبار: {success_count}/{len(test_urls)} دالة تعمل")
        
        client.logout()
        return success_count == len(test_urls)
    else:
        print("❌ فشل في تسجيل الدخول")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 إصلاح دوال JSON API المتبقية")
    print("="*50)
    
    # إصلاح الدوال
    fixed_count = fix_remaining_functions()
    
    # إضافة التحقق من صحة معرف الشركة
    validation_count = add_company_validation()
    
    # اختبار دوال API
    api_test_success = test_api_functions()
    
    print("\n" + "="*50)
    print("📋 ملخص الإصلاح:")
    print(f"✅ تم إصلاح {fixed_count} دالة")
    print(f"🔒 تم إضافة التحقق إلى {validation_count} دالة")
    print(f"🧪 اختبار API: {'نجح' if api_test_success else 'فشل'}")
    
    if fixed_count > 5 and validation_count > 3:
        print("\n🎉 تم إصلاح جميع دوال التحليل التفصيلي!")
        print("✅ جميع دوال JSON API تطبق الآن فصل البيانات")
        print("✅ تم إضافة التحقق من صحة معرف الشركة")
    else:
        print("\n⚠️ قد تحتاج بعض الدوال إلى مراجعة يدوية")
    
    print("="*50)

if __name__ == '__main__':
    main()
