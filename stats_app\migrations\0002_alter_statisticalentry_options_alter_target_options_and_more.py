# Generated by Django 5.0.1 on 2025-06-07 13:43

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('stats_app', '0001_initial'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='statisticalentry',
            options={'ordering': ['-date', 'company__name', 'item__name', 'data_type'], 'verbose_name': 'إدخال إحصائي', 'verbose_name_plural': 'الإدخالات الإحصائية'},
        ),
        migrations.AlterModelOptions(
            name='target',
            options={'ordering': ['-year', '-month', 'company__name', 'item__name', 'data_type'], 'verbose_name': 'مستهدف', 'verbose_name_plural': 'المستهدفات'},
        ),
        migrations.AlterUniqueTogether(
            name='statisticalentry',
            unique_together=set(),
        ),
        migrations.AlterUniqueTogether(
            name='target',
            unique_together=set(),
        ),
        migrations.AddField(
            model_name='statisticalentry',
            name='data_type',
            field=models.CharField(choices=[('capacity', 'الطاقة الإنتاجية المتاحة'), ('production', 'الإنتاج'), ('inventory', 'المخزون'), ('sales', 'البيع')], default='production', max_length=20, verbose_name='نوع البيانات'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='target',
            name='data_type',
            field=models.CharField(choices=[('capacity', 'الطاقة الإنتاجية المتاحة'), ('production', 'الإنتاج'), ('inventory', 'المخزون'), ('sales', 'البيع')], default='production', max_length=20, verbose_name='نوع البيانات'),
            preserve_default=False,
        ),
        migrations.AlterUniqueTogether(
            name='statisticalentry',
            unique_together={('company', 'item', 'data_type', 'date')},
        ),
        migrations.AlterUniqueTogether(
            name='target',
            unique_together={('company', 'item', 'data_type', 'month', 'year')},
        ),
    ]
