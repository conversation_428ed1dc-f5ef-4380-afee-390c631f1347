{% extends 'base.html' %}
{% load static %}

{% block title %}المقارنة بالأعوام السابقة{% endblock %}

{% block extra_css %}
<style>
.filter-card {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.chart-container {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.summary-card {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    text-align: center;
}

.year-selector {
    border: 2px solid #dee2e6;
    border-radius: 8px;
    padding: 10px;
    margin-bottom: 15px;
}

.year-checkbox {
    margin: 5px;
}

.growth-indicator {
    font-weight: bold;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 0.9rem;
}

.growth-positive {
    background-color: #d4edda;
    color: #155724;
}

.growth-negative {
    background-color: #f8d7da;
    color: #721c24;
}

.performance-highlight {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
}

.loading-spinner {
    display: none;
    text-align: center;
    padding: 40px;
}

.comparison-table {
    font-size: 0.9rem;
}

.comparison-table th {
    background-color: #f8f9fa;
    font-weight: 600;
}

.fiscal-year-info {
    background: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 6px;
    padding: 10px;
    margin-bottom: 15px;
    font-size: 0.9rem;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-chart-line me-2"></i>المقارنة بالأعوام السابقة</h2>
                <button id="exportChart" class="btn btn-outline-primary">
                    <i class="fas fa-download me-2"></i>تصدير الرسم البياني
                </button>
            </div>
        </div>
    </div>

    <!-- معلومات السنة المالية -->
    <div class="row">
        <div class="col-12">
            <div class="fiscal-year-info">
                <i class="fas fa-info-circle me-2"></i>
                <strong>ملاحظة:</strong> السنة المالية تبدأ من 
                {{ system_settings.fiscal_year_start_day }}/{{ system_settings.fiscal_year_start_month }} 
                وتنتهي في {{ system_settings.fiscal_year_end_day }}/{{ system_settings.fiscal_year_end_month }}
            </div>
        </div>
    </div>

    <!-- فلاتر التقرير -->
    <div class="row">
        <div class="col-12">
            <div class="filter-card">
                <h5 class="mb-3"><i class="fas fa-filter me-2"></i>فلاتر التقرير</h5>
                
                <form id="comparisonForm">
                    <div class="row">
                        <div class="col-md-3">
                            <label for="company" class="form-label">الشركة</label>
                            <select class="form-select" id="company" name="company">
                                <option value="">جميع الشركات</option>
                                {% for company in companies %}
                                <option value="{{ company.id }}">{{ company.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <div class="col-md-3">
                            <label for="item" class="form-label">العنصر</label>
                            <select class="form-select" id="item" name="item">
                                <option value="">جميع العناصر</option>
                                {% for item in items %}
                                <option value="{{ item.id }}">{{ item.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <div class="col-md-3">
                            <label for="data_type" class="form-label">نوع البيانات</label>
                            <select class="form-select" id="data_type" name="data_type">
                                {% for value, label in data_types %}
                                <option value="{{ value }}" {% if value == 'production' %}selected{% endif %}>{{ label }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-chart-line me-2"></i>عرض المقارنة
                            </button>
                        </div>
                    </div>
                    
                    <!-- اختيار السنوات -->
                    <div class="row mt-3">
                        <div class="col-12">
                            <label class="form-label">اختر السنوات المالية للمقارنة:</label>
                            <div class="year-selector">
                                <div class="row">
                                    {% for year in years %}
                                    <div class="col-md-2 col-sm-3 col-4">
                                        <div class="form-check year-checkbox">
                                            <input class="form-check-input" type="checkbox" 
                                                   id="year{{ year }}" name="years[]" value="{{ year }}"
                                                   {% if forloop.counter <= 3 %}checked{% endif %}>
                                            <label class="form-check-label" for="year{{ year }}">
                                                {{ year }}
                                            </label>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>
                                <small class="text-muted">
                                    <i class="fas fa-lightbulb me-1"></i>
                                    يمكنك اختيار عدة سنوات للمقارنة (يُنصح بعدم تجاوز 5 سنوات للوضوح)
                                </small>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- مؤشر التحميل -->
    <div class="loading-spinner" id="loadingSpinner">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">جاري التحميل...</span>
        </div>
        <p class="mt-2">جاري تحضير بيانات المقارنة...</p>
    </div>

    <!-- الرسم البياني -->
    <div class="row" id="chartSection" style="display: none;">
        <div class="col-12">
            <div class="chart-container">
                <h5 class="mb-3"><i class="fas fa-chart-line me-2"></i>مقارنة الأداء عبر السنوات</h5>
                <canvas id="comparisonChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>

    <!-- ملخص الأداء -->
    <div class="row" id="summarySection" style="display: none;">
        <div class="col-md-4">
            <div class="performance-highlight">
                <h6><i class="fas fa-trophy me-2"></i>أفضل أداء</h6>
                <div id="bestPerformance"></div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="summary-card">
                <h6><i class="fas fa-chart-bar me-2"></i>متوسط الإنجاز</h6>
                <div id="averagePerformance" class="h4 text-primary"></div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="summary-card">
                <h6><i class="fas fa-trending-up me-2"></i>معدلات النمو</h6>
                <div id="growthRates"></div>
            </div>
        </div>
    </div>

    <!-- جدول المقارنة التفصيلي -->
    <div class="row" id="tableSection" style="display: none;">
        <div class="col-12">
            <div class="chart-container">
                <h5 class="mb-3"><i class="fas fa-table me-2"></i>جدول المقارنة التفصيلي</h5>
                <div class="table-responsive">
                    <table class="table table-striped comparison-table" id="comparisonTable">
                        <thead>
                            <tr>
                                <th>السنة المالية</th>
                                <th>إجمالي الفعلي</th>
                                <th>إجمالي المستهدف</th>
                                <th>نسبة الإنجاز</th>
                                <th>معدل النمو</th>
                            </tr>
                        </thead>
                        <tbody id="comparisonTableBody">
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
let comparisonChart = null;

document.addEventListener('DOMContentLoaded', function() {
    // تحميل البيانات الافتراضية
    loadComparisonData();
    
    // ربط الأحداث
    document.getElementById('comparisonForm').addEventListener('submit', function(e) {
        e.preventDefault();
        loadComparisonData();
    });
    
    // تصدير الرسم البياني
    document.getElementById('exportChart').addEventListener('click', function() {
        if (comparisonChart) {
            const link = document.createElement('a');
            link.download = 'yearly-comparison.png';
            link.href = comparisonChart.toBase64Image();
            link.click();
        }
    });
});

function loadComparisonData() {
    // إظهار مؤشر التحميل
    document.getElementById('loadingSpinner').style.display = 'block';
    document.getElementById('chartSection').style.display = 'none';
    document.getElementById('summarySection').style.display = 'none';
    document.getElementById('tableSection').style.display = 'none';
    
    // جمع البيانات من النموذج
    const formData = new FormData(document.getElementById('comparisonForm'));
    
    // جمع السنوات المختارة
    const selectedYears = [];
    document.querySelectorAll('input[name="years[]"]:checked').forEach(checkbox => {
        selectedYears.push(checkbox.value);
    });
    
    // إضافة السنوات للطلب
    selectedYears.forEach(year => {
        formData.append('years[]', year);
    });
    
    // إرسال الطلب
    const params = new URLSearchParams(formData);
    
    fetch(`/reports/yearly-comparison/data/?${params}`)
        .then(response => response.json())
        .then(data => {
            // إخفاء مؤشر التحميل
            document.getElementById('loadingSpinner').style.display = 'none';
            
            // عرض البيانات
            displayComparisonChart(data);
            displaySummary(data);
            displayComparisonTable(data);
            
            // إظهار الأقسام
            document.getElementById('chartSection').style.display = 'block';
            document.getElementById('summarySection').style.display = 'block';
            document.getElementById('tableSection').style.display = 'block';
        })
        .catch(error => {
            console.error('خطأ في تحميل البيانات:', error);
            document.getElementById('loadingSpinner').style.display = 'none';
            alert('حدث خطأ في تحميل البيانات');
        });
}

function displayComparisonChart(data) {
    const ctx = document.getElementById('comparisonChart').getContext('2d');
    
    // تدمير الرسم البياني السابق
    if (comparisonChart) {
        comparisonChart.destroy();
    }
    
    comparisonChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: data.labels,
            datasets: data.datasets
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: 'مقارنة الأداء عبر السنوات المالية'
                },
                legend: {
                    position: 'top'
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'القيمة'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'الأشهر'
                    }
                }
            },
            interaction: {
                intersect: false,
                mode: 'index'
            }
        }
    });
}

function displaySummary(data) {
    const performance = data.performance_analysis;
    
    // أفضل أداء
    document.getElementById('bestPerformance').innerHTML = `
        <div><strong>أفضل إنجاز:</strong> ${performance.best_achievement_year} (${performance.best_achievement_rate}%)</div>
        <div><strong>أعلى قيمة:</strong> ${performance.best_actual_year} (${performance.best_actual_value.toLocaleString()})</div>
    `;
    
    // متوسط الأداء
    document.getElementById('averagePerformance').textContent = `${performance.average_achievement}%`;
    
    // معدلات النمو
    let growthHtml = '';
    for (const [period, rate] of Object.entries(data.growth_rates)) {
        const isPositive = rate >= 0;
        const className = isPositive ? 'growth-positive' : 'growth-negative';
        const icon = isPositive ? 'fa-arrow-up' : 'fa-arrow-down';
        
        growthHtml += `
            <div class="growth-indicator ${className} mb-1">
                <i class="fas ${icon} me-1"></i>
                ${period}: ${rate}%
            </div>
        `;
    }
    document.getElementById('growthRates').innerHTML = growthHtml || 'لا توجد بيانات كافية';
}

function displayComparisonTable(data) {
    const tbody = document.getElementById('comparisonTableBody');
    tbody.innerHTML = '';
    
    const sortedYears = Object.keys(data.summary).sort((a, b) => b - a);
    
    sortedYears.forEach((year, index) => {
        const yearData = data.summary[year];
        const nextYear = sortedYears[index + 1];
        
        let growthCell = '-';
        if (nextYear && data.summary[nextYear]) {
            const currentActual = yearData.total_actual;
            const previousActual = data.summary[nextYear].total_actual;
            
            if (previousActual > 0) {
                const growth = ((currentActual - previousActual) / previousActual) * 100;
                const isPositive = growth >= 0;
                const className = isPositive ? 'text-success' : 'text-danger';
                const icon = isPositive ? 'fa-arrow-up' : 'fa-arrow-down';
                
                growthCell = `<span class="${className}"><i class="fas ${icon} me-1"></i>${growth.toFixed(2)}%</span>`;
            }
        }
        
        const achievementClass = yearData.achievement_rate >= 100 ? 'text-success' : 
                               yearData.achievement_rate >= 75 ? 'text-warning' : 'text-danger';
        
        const row = `
            <tr>
                <td><strong>${year}</strong></td>
                <td>${yearData.total_actual.toLocaleString()}</td>
                <td>${yearData.total_target.toLocaleString()}</td>
                <td><span class="${achievementClass}">${yearData.achievement_rate}%</span></td>
                <td>${growthCell}</td>
            </tr>
        `;
        
        tbody.innerHTML += row;
    });
}
</script>
{% endblock %}
