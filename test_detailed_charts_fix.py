#!/usr/bin/env python
"""
اختبار إصلاح صفحة التحليلات التفصيلية
"""

import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'statistical_system.settings')
django.setup()

from django.test import Client
from django.contrib.auth.models import User

def test_detailed_charts_page():
    """اختبار صفحة التحليلات التفصيلية"""
    print("🧪 اختبار صفحة التحليلات التفصيلية")
    print("="*50)
    
    client = Client()
    
    # اختبار مع مستخدم شركة
    print("👤 اختبار مع مستخدم الشركة (alaa1)...")
    login_success = client.login(username='alaa1', password='alaa1123')
    
    if login_success:
        print("✅ تم تسجيل الدخول بنجاح")
        
        try:
            # اختبار الوصول للصفحة
            response = client.get('/reports/detailed-charts/')
            
            if response.status_code == 200:
                print("✅ صفحة التحليلات التفصيلية تعمل بشكل صحيح")
                
                # التحقق من وجود filter_form في السياق
                if 'filter_form' in response.context:
                    print("✅ نموذج التصفية موجود في السياق")
                else:
                    print("❌ نموذج التصفية غير موجود في السياق")
                
                # التحقق من وجود allowed_companies
                if 'allowed_companies' in response.context:
                    companies_count = response.context['allowed_companies'].count()
                    print(f"✅ الشركات المسموحة: {companies_count}")
                else:
                    print("❌ الشركات المسموحة غير موجودة في السياق")
                
                return True
                
            else:
                print(f"❌ خطأ في الوصول للصفحة: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في اختبار الصفحة: {e}")
            return False
    else:
        print("❌ فشل في تسجيل الدخول")
        return False

def test_chart_api():
    """اختبار API الرسوم البيانية"""
    print("\n🔍 اختبار API الرسوم البيانية...")
    
    client = Client()
    client.login(username='alaa1', password='alaa1123')
    
    # اختبار بعض دوال API
    api_tests = [
        ('monthly_comparison', 'مقارنة شهرية'),
        ('company_performance', 'أداء الشركات'),
        ('achievement_analysis', 'تحليل الإنجاز'),
    ]
    
    success_count = 0
    
    for chart_type, description in api_tests:
        try:
            response = client.get(f'/reports/charts/api/?chart_type={chart_type}')
            
            if response.status_code == 200:
                print(f"✅ {description}: يعمل")
                success_count += 1
            else:
                print(f"❌ {description}: خطأ {response.status_code}")
                
        except Exception as e:
            print(f"❌ {description}: خطأ {e}")
    
    client.logout()
    
    print(f"\n📊 نتيجة اختبار API: {success_count}/{len(api_tests)} يعمل")
    return success_count == len(api_tests)

def test_data_separation():
    """اختبار فصل البيانات"""
    print("\n🔒 اختبار فصل البيانات...")
    
    from stats_app.views import DetailedChartsView
    from django.test import RequestFactory
    from django.contrib.auth.models import AnonymousUser
    
    factory = RequestFactory()
    
    # إنشاء طلب وهمي
    request = factory.get('/reports/detailed-charts/')
    
    try:
        # اختبار مع مستخدم الشركة
        user = User.objects.get(username='alaa1')
        request.user = user
        
        view = DetailedChartsView()
        view.request = request
        
        # اختبار get_user_companies
        allowed_companies = view.get_user_companies()
        print(f"✅ الشركات المسموحة للمستخدم alaa1: {allowed_companies.count()}")
        
        # اختبار filter_queryset_by_company
        from stats_app.models import StatisticalEntry
        filtered_entries = view.filter_queryset_by_company(StatisticalEntry.objects.all())
        total_entries = StatisticalEntry.objects.count()
        
        print(f"✅ إجمالي الإدخالات: {total_entries}")
        print(f"✅ الإدخالات المفلترة: {filtered_entries.count()}")
        
        if filtered_entries.count() < total_entries:
            print("✅ فصل البيانات يعمل بشكل صحيح")
            return True
        else:
            print("⚠️ قد لا يعمل فصل البيانات بشكل صحيح")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار فصل البيانات: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 اختبار إصلاح صفحة التحليلات التفصيلية")
    print("="*60)
    
    # اختبار صفحة التحليلات التفصيلية
    page_works = test_detailed_charts_page()
    
    # اختبار API
    api_works = test_chart_api()
    
    # اختبار فصل البيانات
    separation_works = test_data_separation()
    
    print("\n" + "="*60)
    print("📋 ملخص النتائج:")
    print(f"   📄 صفحة التحليلات التفصيلية: {'✅ تعمل' if page_works else '❌ لا تعمل'}")
    print(f"   🔌 API الرسوم البيانية: {'✅ يعمل' if api_works else '❌ لا يعمل'}")
    print(f"   🔒 فصل البيانات: {'✅ يعمل' if separation_works else '❌ لا يعمل'}")
    
    if page_works and api_works and separation_works:
        print("\n🎉 تم إصلاح جميع المشاكل بنجاح!")
        print("✅ صفحة التحليلات التفصيلية تعمل بشكل مثالي")
        print("✅ جميع APIs تطبق فصل البيانات")
        print("✅ النظام جاهز للاستخدام")
    else:
        print("\n⚠️ لا تزال هناك بعض المشاكل")
        print("يرجى مراجعة الأخطاء أعلاه")
    
    print("="*60)

if __name__ == '__main__':
    main()
